<template>
  <div>
    <div class="wrapper wrapper-full-page">
      <nav class="navbar navbar-expand-lg navbar-transparent navbar-absolute">
        <div class="container">
          <div class="navbar-wrapper">
            <div class="d-flex align-items-center">
              <img :src="logo" alt="Logo" class="navbar-logo mr-2">
              <a class="navbar-brand fs-4" href="/">ISSUES MANAGEMENT SYSTEM</a>
            </div>
            <button
              class="navbar-toggler"
              type="button"
              @click="showMenu = !showMenu"
              data-toggle="collapse"
              aria-controls="navigation-index"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span class="navbar-toggler-bar burger-lines"></span>
              <span class="navbar-toggler-bar burger-lines"></span>
              <span class="navbar-toggler-bar burger-lines"></span>
            </button>
          </div>
          <el-collapse-transition>
            <div
              class="navbar-collapse justify-content-end"
              id="navbar"
              v-show="showMenu"
            >
              <ul class="navbar-nav">
                <router-link v-if="!isLoggedIn" to="/submitissue" tag="li" class="nav-item">
                  <a class="nav-link">
                    <i class="nc-icon nc-notification-70"></i> +Issue
                  </a>
                </router-link>
                <router-link to="/signup" tag="li"  v-if="!isLoggedIn" class="nav-item">
                  <a class="nav-link">
                    <i class="nc-icon nc-badge"></i> Signup
                  </a>
                </router-link>
                <a
                  v-if="isLoggedIn"
                  href="#"
                  tag="li"
                  class="nav-item"
                  @click="logout"
                >
                  <a class="nav-link">
                    <i class="fas fa-sign-out"></i> Logout
                  </a>
                </a>
                <router-link v-else to="/login" tag="li" class="nav-item">
                  <a class="nav-link">
                    <i class="nc-icon nc-mobile"></i> Login
                  </a>
                </router-link>
              </ul>
            </div>
          </el-collapse-transition>
        </div>
      </nav>
      <div class="full-page" :data-color="backgroundColor" :class="pageClass">
        <!--   you can change the color of the filter page using: data-color="blue | azure | green | orange | red | purple | black" -->
        <div class="content">
          <div class="container">
            <slot></slot>
          </div>
        </div>

        <footer class="footer footer-transparent">
          <div
            class="container d-flex flex-lg-row flex-column justify-content-lg-between justify-content-center align-items-center"
          >
            <nav>
              <ul>
                <li>
                  <router-link to="/">Home</router-link>
                </li>
              </ul>
            </nav>
            <div class="copyright">
              &copy; Coded with
              <i class="fa fa-heart heart"></i> by
              <a
                href="https://www.creative-tim.com/?ref=pdf-vuejs"
                target="_blank"
                rel="noopener"
                >Small World</a
              >.
            </div>
          </div>
        </footer>

        <div
          class="full-page-background"
          style="background-image: url('../static/img/full-screen-image-1.jpg')"
        ></div>
      </div>
    </div>
    <div class="collapse navbar-collapse off-canvas-sidebar">
      <ul class="nav nav-mobile-menu">
        <router-link to="/register" tag="li">
          <a>Register</a>
        </router-link>
        <router-link to="/admin/overview" tag="li">
          <a>Dashboard</a>
        </router-link>
      </ul>
    </div>
  </div>
</template>
<script>
import CollapseTransition from "element-ui/lib/transitions/collapse-transition";

export default {
  components: {
    [CollapseTransition.name]: CollapseTransition,
  },
  props: {
    pageClass: {
      type: String,
      default: "login-page",
    },
    backgroundColor: {
      type: String,
      default: "black",
    },
    logo: {
      type: String,
      default: '/static/img/logo.png'
    },
  },
  data() {
    return {
      isLoggedIn: false,
      showMenu: false,
    };
  },
  methods: {
    checkLoginStatus() {
      this.isLoggedIn = !!localStorage.getItem("token");
    },
    logout() {
      // Clear session storage
      localStorage.clear();
      this.isLoggedIn = false;
      this.$alert.success("You have been logged out successfully.");
      this.$router.push("/login");
    },
    toggleNavbar() {
      document.body.classList.toggle("nav-open");
    },
    closeMenu() {
      document.body.classList.remove("nav-open");
      document.body.classList.remove("off-canvas-sidebar");
    },
  },
  beforeDestroy() {
    this.closeMenu();
  },
  mounted() {
    this.checkLoginStatus();
  },
};
</script>
<style>
.navbar-nav .nav-item p {
  line-height: inherit;
  margin-left: 5px;
}
.navbar-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}
.wrapper-full-page .navbar-toggler,
.wrapper-full-page .navbar-collapse .navbar-nav {
  margin-right: 30px;
}
.navbar-collapse .navbar-nav .nav-link {
  width: 100%;
  display: flex;
  justify-content: center;
}
.navbar-logo {
  height: 40px;
  width: auto;
  margin-right: 10px;
}
</style>
