<template>
  <auth-layout pageClass="reset-password-page mt-4">
    <div class="row d-flex justify-content-center align-items-center">
      <div class="col-lg-4 col-md-6 col-sm-8">
        <ValidationObserver v-slot="{ handleSubmit }">
          <form @submit.prevent="handleSubmit(submit)">
            <fade-render-transition>
              <card>
                <div slot="header">
                  <h3 class="card-title text-center">Reset Password</h3>
                </div>
                <div>
                  <p class="text-center">Enter your new password below.</p>
                  <ValidationProvider
                    name="password"
                    rules="required|min:6"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="password"
                      :error="
                        failed
                          ? 'Password must be at least 6 characters long'
                          : null
                      "
                      :hasSuccess="passed"
                      v-model="password"
                      placeholder="New Password"
                    >
                    </fg-input>
                  </ValidationProvider>
                  <ValidationProvider
                    name="confirmPassword"
                    rules="required|confirmed:password"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="password"
                      :error="failed ? 'Passwords do not match' : null"
                      :hasSuccess="passed"
                      v-model="confirmPassword"
                      placeholder="Confirm Password"
                    >
                    </fg-input>
                  </ValidationProvider>
                </div>
                <div class="text-center mt-3">
                  <button
                    type="submit"
                    class="btn btn-fill btn-info btn-round btn-wd"
                  >
                    Reset Password
                  </button>
                  <br />
                  <router-link to="/login" class="card-category mt-2">
                    Back to Login
                  </router-link>
                </div>
              </card>
            </fade-render-transition>
          </form>
        </ValidationObserver>
      </div>
    </div>
  </auth-layout>
</template>
  
  <script>
import { FadeRenderTransition } from "src/components/index";
import AuthLayout from "./Layout/AuthLayout.vue";
import { extend } from "vee-validate";
import { required, min, confirmed } from "vee-validate/dist/rules";
import axios from "axios";

// Extend vee-validate rules
extend("required", required);
extend("min", min);
extend("confirmed", confirmed);
import API from "@/services/api";

export default {
  components: {
    FadeRenderTransition,
    AuthLayout,
  },
  data() {
    return {
      userService: new API(process.env.VUE_APP_API_URL, "auth/reset-password"),
      password: "",
      confirmPassword: "",
      email: "",
      token: "",
    };
  },
  created() {
    // Retrieve email and token from query parameters
    const query = this.$route.query;
    this.email = query.email || "";
    this.token = query.token || "";

    if (!this.email || !this.token) {
      // Redirect to an error page if email or token is missing
      this.$router.push("/error");
    }
  },
  methods: {
    async submit() {
      this.isLoading = true; // Set loading state
      try {
        const payload = {
          email: this.email,
          token: this.token,
          password: this.password,
        };
        const response = await this.userService.create(payload);
        if (response.token) {
          this.$alert.success(response.message);
          this.$router.push("/login");
        } else {
          this.$alert.success("Failed to reset password: " + response.message);
        }
      } catch (error) {
        console.error("Error sending reset link:", error);
        this.$alert.error(
          "An error occurred while processing your request. Please try again later."
        );
      } finally {
        this.isLoading = false; // Reset loading state
      }
    },
  },
};
</script>
  
  <style>
.navbar-nav .nav-item p {
  line-height: inherit;
  margin-left: 5px;
}
</style>
  