<template>
  <div class="py-3">
    <!-- Quick Action Buttons -->
    <div class="row mb-4">
      <div class="col-12 text-start">
        <router-link
          to="/executive/issues"
          class="btn btn-primary me-2 mr-3"
          @click="openReportIssue"
        >
          <i class="fas fa-plus-circle me-1"></i> Report New Issue
        </router-link>
      </div>
    </div>

    <!-- Stats Cards Row -->
    <div class="row">
      <div
        class="col-12 col-sm-6 col-xl-3 mb-1"
        v-for="(stat, index) in statsList"
        :key="index"
      >
        <stats-card
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :color="stat.color"
          :loading="loading"
        >
          <template #footer>
            <div v-if="stat.footer" class="d-flex justify-content-between">
              <small v-for="(footerText, idx) in stat.footer" :key="idx">{{
                footerText
              }}</small>
            </div>
          </template>
        </stats-card>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12 col-lg-8 mb-3">
        <div class="card h-100">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h5 class="card-title mb-0">
              Issue Trends [Since
              {{ formatDate(getDateForOneYearFromToday()) }}]
            </h5>
            <select
              v-model="trendsPeriod"
              class="form-select form-select-sm w-auto"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
          </div>
          <div class="card-body">
            <div id="trends-chart"></div>
          </div>
        </div>
      </div>

      <div class="col-12 col-lg-4 mb-3">
        <div class="card h-100">
          <div class="card-header">
            <h5 class="card-title mb-0">Issues by Category</h5>
          </div>
          <div class="card-body">
            <div id="category-chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Issues Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h5 class="card-title mb-0">Recent Issues</h5>
            <button
              class="btn btn-sm btn-outline-primary"
              @click="refreshIssues"
            >
              <i class="fas fa-sync-alt"></i> Refresh
            </button>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody v-if="!loading">
                  <tr v-for="issue in recentIssues" :key="issue.issueId">
                    <td>{{ issue.issueRef }}</td>
                    <td>{{ issue.title }}</td>
                    <td>{{ issue.organization }}</td>
                    <td>
                      <span :class="getStatusBadgeClass(issue.status)">
                        {{ issue.status }}
                      </span>
                    </td>
                  
                    <td>{{ formatDate(issue.reportedAt) }}</td>
                    <td>
                      <button
                        class="btn btn-sm btn-outline-primary me-1"
                        @click="viewIssue(issue.issueId)"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <tr>
                    <td colspan="7" class="text-center">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="card-footer text-center">
            <router-link
              class="btn btn-outline-primary"
              to="/executive/issues"
            >
              View All Issues
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import StatsCard from "@/components/Cards/Tile.vue";
import * as dc from "dc";
import * as crossfilter from "crossfilter2";
import * as d3 from "d3";
import API from "@/services/api";
import moment from "moment";

export default {
  name: "Dashboard",

  components: {
    StatsCard,
  },

  data() {
    return {
      issuesService: new API(process.env.VUE_APP_API_URL, "Issues/all-issues"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      issues: [],
      period: "",
      statsList: [],
      loading: true,
      trendsPeriod: "30",
      stats: {
        activeIssues: 0,
        criticalIssues: 0,
        highPriorityIssues: 0,
        slaComplianceRate: 0,
        issuesWithinSLA: 0,
        pendingAssignments: 0,
        recentAssignments: 0,
        satisfactionRate: 0,
        totalFeedbacks: 0,
      },
      recentIssues: [],
      ndx: null, // Crossfilter instance
    };
  },

  async mounted() {
    await this.fetchDepartments(), await this.fetchDashboardData();
    this.startAutoRefresh();
  },

  beforeDestroy() {
    this.stopAutoRefresh();
    if (this.ndx) {
      this.ndx.remove(); // Clean up crossfilter
    }
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    company() {
      return this.loggedInUser?.department;
    },
  },
  methods: {
    async fetchDepartments() {
      try {
        this.departments = await this.departmentService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },
    getDepartment(issueId) {
      let department = this.departments?.find((a) => a.departmentId == issueId);
      return `${department?.hod}(${department?.name})`;
    },
    computeStats(issues) {
      const totalIssues = issues.length;
      const resolvedIssues = issues.filter(
        (issue) => issue.status === "Resolved"
      ).length;
      const newIssues = issues.filter((issue) => issue.status == "New").length;
      const slaComplianceRate = ((resolvedIssues / totalIssues) * 100).toFixed(
        2
      );

      this.statsList = [
        {
          title: "Total Issues",
          value: totalIssues,
          icon: "fas fa-tasks",
          color: "primary",
        },
        {
          title: "Resolved Issues",
          value: resolvedIssues,
          icon: "fas fa-check-circle",
          color: "success",
        },
        {
          title: "New Issues",
          value: newIssues,
          icon: "fas fa-exclamation-circle",
          color: "warning",
        },
        {
          title: "Resolution %",
          value: `${slaComplianceRate}%`,
          icon: "fas fa-clock",
          color: "info",
        },
      ];
    },

    getDepartmentIdByName(departmentName) {
      const department = this.departments.find(
        (d) => d.name === departmentName
      );
      return department ? department.departmentId : null; // Return null if not found
    },

    async fetchDashboardData() {
      try {
        this.loading = true;
        const response = await this.issuesService.getAll();
        let departmentId = this.getDepartmentIdByName(
          this.loggedInUser.department
        );
        this.issues = response|| []
          //response?.filter((x) => x.assignedDepartmentId == departmentId) || [];
        this.recentIssues = this.issues.slice(0, 5);

        // Compute stats from the fetched data
        this.computeStats(this.issues);

        // Initialize crossfilter
        this.ndx = crossfilter.default(this.issues);

        // Render dc.js charts
        this.renderCharts();
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        this.$toast.error("Failed to load dashboard data");
      } finally {
        this.loading = false;
      }
    },

    getDateForOneYearFromToday() {
      const today = new Date(); // Get today's date
      const oneYearFromToday = new Date(today); // Create a copy of today's date
      oneYearFromToday.setFullYear(today.getFullYear() - 1); // Add one year to the copied date
      return oneYearFromToday; // Return the updated date
    },

    renderCharts() {
      // Define your theme colors
      const themeColors = {
        primary: "#302e77", // Primary color (e.g., blue)
        secondary: "#87CB16", // Secondary color (e.g., gray)
        success: "#28A745", // Success color (e.g., green)
        warning: "#FFA534", // Warning color (e.g., yellow)
        danger: "#DC3545", // Danger color (e.g., red)
        info: "#17A2B8", // Info color (e.g., teal)
      };

      // Create a color scale using the theme colors
      const colorScale = d3
        .scaleOrdinal()
        .range([
          themeColors.primary,
          themeColors.secondary,
          themeColors.success,
          themeColors.warning,
          themeColors.danger,
          themeColors.info,
        ]);

      // Trends Chart (Line Chart)
      const dateFormat = d3.timeFormat("%Y-%m");
      const dateDim = this.ndx.dimension((d) =>
        dateFormat(new Date(d.reportedAt))
      );
      const dateGroup = dateDim.group().reduceCount();

      const today = new Date();
      const oneYearFromToday = this.getDateForOneYearFromToday();

      const yearMonths = dateGroup.all().map((d) => d.key);

      // Create a band scale for the x-axis
      const xScale = d3
        .scaleBand()
        .domain(yearMonths) // Set domain to the unique year-month keys
        .padding(0.1);

      this.trendsChart = dc.lineChart("#trends-chart");
      this.trendsChart
        .width(600)
        .height(300)
        .dimension(dateDim)
        .group(dateGroup)
        .x(xScale)
        .elasticY(true)
        .brushOn(false)
        .colors(colorScale) // Apply the color scale
        .colorAccessor((d) => d.key); // Use the year-month as the color key

      this.trendsChart.xAxis().tickFormat((d) => d);

      this.trendsChart.render();

      // Category Chart (Pie Chart)
      const statusDim = this.ndx.dimension((d) => d.status);
      const statusGroup = statusDim.group();

      this.categoryChart = dc.pieChart("#category-chart");
      this.categoryChart
        .width(250)
        .height(250)
        .dimension(statusDim)
        .group(statusGroup)
        .colors(colorScale) // Apply the color scale
        .colorAccessor((d) => d.key); // Use the status as the color key

      this.categoryChart.render();

      // Redraw charts on window resize
      window.addEventListener("resize", this.redrawCharts);
    },

    redrawCharts() {
      if (this.trendsChart) {
        this.trendsChart.width(window.innerWidth * 0.8).redraw();
      }
      if (this.categoryChart) {
        this.categoryChart.width(window.innerWidth * 0.3).redraw();
      }
    },

    getStatusBadgeClass(status) {
      const classes = {
        New: "badge bg-info",
        Resolved: "badge bg-success",
      };
      return classes[status] || "badge bg-secondary";
    },

    formatDate(date) {
      return moment(date).format("DD-MM-YYYY");
    },

    startAutoRefresh() {
      this.refreshInterval = setInterval(this.fetchDashboardData, 300000); // 5 minutes
    },

    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
      }
    },

    async refreshIssues() {
      await this.fetchDashboardData();
      this.$toast.success("Dashboard data refreshed");
    },

    openReportIssue() {
      this.$router.push("./internal/issues");
    },

    openMyIssues() {
      this.$router.push("./internal/issues/my-issues");
    },

    viewIssue(id) {
      this.$router.push(`/executive/issue-view/${id}`);
    },

    editIssue(id) {
      this.$router.push(`/issues/${id}/edit`);
    },

    viewAllIssues() {
      this.$router.push("/issues");
    },
  },

  watch: {
    trendsPeriod: {
      handler: "fetchDashboardData",
      immediate: false,
    },
  },
};
</script>
<style scoped>
#trends-chart,
#category-chart {
  width: 100%;
  height: 300px;
}

/* Ensure the chart resizes when the window is resized */
.dc-chart {
  width: 100% !important;
  height: auto !important;
}

.card-tools {
  position: absolute;
  right: 1rem;
  top: 0.75rem;
}

.table td {
  vertical-align: middle;
}

.badge {
  font-size: 0.8rem;
  padding: 0.35em 0.65em;
}
</style>