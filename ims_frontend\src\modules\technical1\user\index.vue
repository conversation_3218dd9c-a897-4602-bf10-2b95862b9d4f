<template>
  <div class="user-management">
    <!-- User Form -->
    <card v-if="showUserForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">{{ isEditing ? "Edit User" : "Add User" }}</h4>
      </template>
      <form @submit.prevent="submitUser">
        <div class="row">
          <div class="col-md-6">
            <fg-input
              label="Full Name"
              type="text"
              v-model="currentUser.fullName"
              :required="true"
              placeholder="Enter full name"
            />
          </div>
          <div class="col-md-6">
            <fg-input
              label="Email"
              type="email"
              v-model="currentUser.email"
              :required="true"
              placeholder="Enter email address"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input
              label="Phone"
              type="tel"
              v-model="currentUser.phoneNumber"
              placeholder="Enter phoneNumber number"
            />
          </div>
          <div class="col-md-6">
            <fg-input label="Role">
              <el-select
                v-model="currentUser.role"
                placeholder="Select user role"
                class="w-100"
              >
                <el-option
                  v-for="role in roles"
                  :key="role"
                  :label="role"
                  :value="role"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Department">
              <el-select
                v-model="currentUser.departmentId"
                placeholder="Select department"
                class="w-100"
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.departmentId"
                  :label="dept.name"
                  :value="dept.departmentId"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Active Status">
              <div class="col-md-12">
                <l-switch
                  v-model="currentUser.isActive"
                  type="primary"
                  on-text="Active"
                  off-text="Inactive"
                ></l-switch>
              </div>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update User" : "Add User" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeUserForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- User List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">User List</h4></div>
          <div class="col-6 text-right">
            <!-- <router-link to="/technical/departments" class="btn btn-success mx-2">
              Departments
            </router-link>

            <router-link to="/technical/customers" class="btn btn-warning mx-2">
              Customers
            </router-link> -->
          </div>
        </div>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            class="select-default mb-3"
            style="width: 200px"
            v-model="pagination.perPage"
            placeholder="Per page"
          >
            <el-option
              class="select-default"
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <el-input
            type="search"
            class="mb-3"
            style="width: 200px"
            placeholder="Search records"
            v-model="searchQuery"
            aria-controls="datatables"
          />
        </div>
        <div class="col-sm-12">
          <el-table :data="queriedData" style="width: 100%" stripe border>
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
            />
            <el-table-column label="Status">
              <template v-slot="scope">
                <span
                  :class="
                    scope.row.isActive
                      ? 'badge badge-success'
                      : 'badge badge-danger'
                  "
                >
                  {{ scope.row.isActive ? "Active" : "Inactive" }}
                </span>
              </template>
            </el-table-column>

          </el-table>
        </div>
      </div>

      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <div class="">
          <p class="card-category">
            Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
          </p>
        </div>
        <l-pagination
          class="pagination-no-border"
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Switch as LSwitch,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input } from "element-ui";
import API from "@/services/api"; // Import userService

export default {
  components: {
    FgInput,
    LSwitch,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
  },
  data() {
    return {
      departmentService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "departments" // Endpoint
      ),
      userService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "users" // Endpoint
      ),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      tableColumns: [
        { prop: "fullName", label: "Name", minWidth: 150 },
        { prop: "email", label: "Email", minWidth: 200 },
        { prop: "phoneNumber", label: "Phone", minWidth: 100 },
        { prop: "role", label: "Role", minWidth: 180 },
      ],
      users: [],
      departments: [],
      roles: ["Customer", "Engineer", "Admin", "HOD"],
      currentUser: {
        id: null,
        fullName: "",
        email: "",
        phoneNumber: "",
        role: "",
        departmentId: null,
        isActive: true,
      },
      showUserForm: false,
      isEditing: false,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.users?.filter((user) =>
        Object.values(user).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.users.length;
    },

    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    getDepartmentName(id) {
      return this.departments.find((x) => (id = x.departmentId)).name;
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = response; // Assuming API returns data in `data` field
      } catch (error) {
        //console.error("Error fetching departments:", error);
      }
    },
    async fetchUsers() {
      this.loading = true;
      try {
        // Fetch departments from the API
        const response = await this.userService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          search: this.searchQuery,
        });

        this.users = response;
        this.pagination.total = response?.length;

        // // Success alert (optional)
        // this.$alert.success("Departments fetched successfully");
      } catch (error) {
        // Error handling
        this.$alert.error(
          error.response?.data?.message || "Failed to fetch users"
        );
      } finally {
        this.loading = false;
      }
    },
    async addUser() {
      try {
        let newObject = { ...this.currentUser };
        delete newObject.id;
        const response = await this.userService.create(newObject);
        this.$alert.success("User added successfully");
        this.closeUserForm();
      } catch (error) {
        //console.error("Error adding user:", error);
      }
    },
    async updateUser() {
      try {
        const userService = await this.userService.update(
          this.currentUser.id,
          this.currentUser
        );

        this.$alert.success("User updated successfully");
        this.closeUserForm();
      } catch (error) {
        //console.error("Error updating user:", error);
      }
    },

    async deleteService(id) {
      try {
        // Confirm before deletion
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this user?"
        );

        if (confirmDelete.isConfirmed) {
          await this.userService.delete(id);

          // Remove from local state
          this.services = this.users?.filter((s) => s.id !== id);

          this.$alert.success("User deleted successfully");
          this.fetchUsers();
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete user"
        );
      }
    },

    async deleteUser(id) {
      try {
        // Confirm before deletion
        const confirmDelete = window.confirm(
          "Are you sure you want to delete this user?"
        );

        if (confirmDelete) {
          await this.userService.delete(id);

          // Remove from local state
          this.departments = this.departments?.filter((d) => d.id !== id);

          this.$alert.success("User deleted successfully");
          this.fetchUsers();
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete user"
        );
      }
    },

    async submitUser() {
      this.loading = true;
      try {
        if (this.isEditing) {
          await this.updateUser();
        } else {
          await this.addUser();
        }

        this.fetchUsers();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to submit user"
        );
      } finally {
        this.loading = false;
      }
    },
    editUser(user) {
      this.currentUser = { ...user };
      this.isEditing = true;
      this.showUserForm = true;
    },
    closeUserForm() {
      this.isEditing = false;
      this.showUserForm = false;
      this.currentUser = {
        id: null,
        name: "",
        email: "",
        phoneNumber: "",
        role: "",
        departmentId: null,
        isActive: true,
      };
    },
  },
  created() {
    this.fetchDepartments();
    this.fetchUsers();
  },
};
</script>
