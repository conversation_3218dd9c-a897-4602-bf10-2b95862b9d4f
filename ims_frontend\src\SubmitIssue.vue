<template>
  <auth-layout pageClass="signup-page">
    <div class="row d-flex justify-content-center align-items-center mt-4">
      <!-- Anonymous Issue Form -->
      <card class="mt-4 col-lg-6 col-md-6 col-sm-8">
        <template slot="header">
          <h4 class="card-title">
            Report an Issue [<span class="text-danger">
              works best when you login
              <router-link to="/login">here</router-link></span
            >
            ]
          </h4>
        </template>
        <ValidationObserver v-slot="{ handleSubmit }">
          <form @submit.prevent="handleSubmit(submitIssue)">
            <!-- Toggle Switch for Optional Fields -->
            <div class="row mb-3">
              <div class="col-md-12">
                <el-switch
                  v-model="showAllFields"
                  active-text="Customer"
                  inactive-text="Non customer"
                />
              </div>
            </div>

            <!-- Required Fields -->
            <div class="row">
              <div class="col-md-6">
                <fg-input label="Full name" required>
                  <input
                    v-model="currentIssue.customer"
                    type="text"
                    class="form-control"
                    placeholder="Enter full name"
                    required
                  />
                </fg-input>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <fg-input label="Phone" required>
                  <input
                    v-model="currentIssue.phone"
                    type="tel"
                    class="form-control"
                    placeholder="Enter phone"
                    required
                  />
                </fg-input>
              </div>
              <div class="col-md-6">
                  <fg-input label="Email">
                    <input
                      v-model="currentIssue.email"
                      type="email"
                      class="form-control"
                      placeholder="Enter email"
                    />
                  </fg-input>
                </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <fg-input :label="`Company`">
                  <input
                    v-model="currentIssue.company"
                    filterable
                    class="form-control"
                    placeholder="Enter company name"
                    required
                  />
                </fg-input>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <fg-input label="Select District" required>
                  <el-select
                    v-model="currentIssue.district"
                    placeholder="Select a District"
                    class="w-100"
                    required
                  >
                    <el-option
                      v-for="district in malawiDistricts"
                      :key="district"
                      :label="district"
                      :value="district"
                    />
                  </el-select>
                </fg-input>
              </div>
              <div class="col-md-6">
                <fg-input label="Location" required>
                  <input
                    v-model="currentIssue.location"
                    type="text"
                    class="form-control"
                    placeholder="Enter where issue happened"
                    required
                  />
                </fg-input>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <fg-input label="Subject" required>
                  <input
                    v-model="currentIssue.title"
                    type="text"
                    class="form-control"
                    placeholder="Enter issue title"
                    required
                  />
                </fg-input>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <fg-input label="Service" required>
                  <el-select
                    v-model="currentIssue.serviceId"
                    placeholder="Select service"
                    filterable
                    class="w-100"
                    required
                  >
                    <el-option
                      v-for="service in services"
                      :key="service.i_service_type"
                      :label="`${service.name} - ${service.i_service_type}`"
                      :value="service.i_service_type"
                    />
                  </el-select>
                </fg-input>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <fg-input label="Description" required>
                  <textarea
                    v-model="currentIssue.description"
                    class="form-control"
                    rows="4"
                    placeholder="Enter issue description"
                    required
                  ></textarea>
                </fg-input>
              </div>

              
            </div>

            <div class="row">
              <div class="col-md-6">
                  <fg-input label="Fault Start Date" required>
                    <el-date-picker
                      v-model="currentIssue.faultStartDate"
                      type="datetime"
                      placeholder="Select date and time"
                      class="w-100"
                    />
                  </fg-input>
                </div>
            </div>
            
            <!-- Optional Fields (Conditionally Rendered) -->
            <div v-if="showAllFields">
              <div class="row">
                <div class="col-md-6">
                  <fg-input label="Account">
                    <input
                      v-model="currentIssue.account"
                      type="text"
                      class="form-control"
                      placeholder="Enter account"
                    />
                  </fg-input>
                </div>
               
               
             
                <div class="col-md-6">
                  <fg-input label="Criticality">
                    <el-select
                      v-model="currentIssue.priority"
                      placeholder="Select Criticality"
                      class="w-100"
                    >
                      <el-option
                        v-for="cat in priorities"
                        :key="cat"
                        :label="cat"
                        :value="cat"
                      />
                    </el-select>
                  </fg-input>
                </div>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="row">
              <div class="col-md-12">
                <button
                  type="submit"
                  class="btn btn-fill btn-info"
                  :disabled="isSubmitting"
                >
                  <span v-if="isSubmitting">Submitting...</span>
                  <span v-else>Submit Issue</span>
                </button>
              </div>
            </div>
          </form>
        </ValidationObserver>
      </card>
    </div>
  </auth-layout>
</template>

<script>
import { FormGroupInput as FgInput } from "src/components/index";
import { Select, Option, Switch, DatePicker } from "element-ui";
import AuthLayout from "./Layout/AuthLayout.vue";
import API from "@/services/api";
import { required, email } from "vee-validate/dist/rules";
import { extend } from "vee-validate";

extend("email", email);
extend("required", required);

export default {
  name: "AnonymousIssueSubmission",
  components: {
    AuthLayout,
    FgInput,
    "el-select": Select,
    "el-option": Option,
    "el-switch": Switch,
    "el-date-picker": DatePicker,
  },
  data() {
    return {
      isSubmitting: false,
      showAllFields: false,
      reportService: new API(process.env.VUE_APP_API_URL, "report"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      services: [],
      priorities: ["Low", "Medium", "High", "Critical"],
      malawiDistricts: [
        "Blantyre",
        "Lilongwe",
        "Mzuzu",
        "Zomba",
        "Mangochi",
        "Kasungu",
        "Balaka",
        "Chikwawa",
        "Chiradzulu",
        "Chitipa",
        "Dedza",
        "Dowa",
        "Karonga",
        "Likoma",
        "Machinga",
        "Mchinji",
        "Mulanje",
        "Mwanza",
        "Mzimba",
        "Neno",
        "Nkhata Bay",
        "Nkhotakota",
        "Nsanje",
        "Ntcheu",
        "Ntchisi",
        "Phalombe",
        "Rumphi",
        "Salima",
        "Thyolo",
      ],
      currentIssue: {
        customer: null,
        account: "",
        serviceId: null,
        location: "",
        title: "",
        description: "",
        priority: "",
        phone: "",
        email: "",
        district: "",
        faultStartDate: null,
      },
    };
  },
  methods: {
    async fetchServices() {
      try {
        let data = await this.serviceService.getAll();
        this.services=data?.services
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },

    async submitIssue() {
      if (this.isSubmitting) return;
      this.isSubmitting = true;
      try {
        const newObject = { ...this.currentIssue };
        newObject.source = "Website";
        newObject.issueType = "customer";
        const response = await this.reportService.create(newObject);

        // Assuming response contains the issue reference
        const issueRef = response?.issueRef || "N/A";

        // Show a confirmation dialog with the issue reference
        this.$alert.successPermanent(
          `Your issue has been reported successfully. Your reference number is: <strong>${issueRef}</strong>, which you will use when following up with MTL`
        );

        // Clear the form after successful submission
        this.currentIssue = {
          customer: null,
          account: "",
          serviceId: null,
          location: "",
          title: "",
          description: "",
          priority: "",
          phone: "",
          email: "",
          district: "",
          faultStartDate: null,
        };
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to report the issue"
        );
      } finally {
        this.isSubmitting = false;
      }
    },
  },
  async created() {
    await this.fetchServices();
  },
};
</script>

