<template>
  <div class="issues-management">
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-4"><h4 class="card-title">Logs</h4></div>
          <div class="col-8 text-right">
            <router-link
              to="/audit/timeline"
              class="btn btn-success mx-2"
            >
              Issues Timeline
            </router-link>
          </div>
        </div>
      </template>

      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search issues"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table
            :data="queriedData"
            @row-click="handleRowClick"
            stripe
            border
            style="width: 100%"
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            ></el-table-column>

            <!-- Assignment Column -->
            <el-table-column label="Assigned To" min-width="150">
              <template v-slot="{ row }">
                <div v-if="getAssignment(row.issueId)">
                  {{ formatAssignee(getAssignment(row.issueId)) }}
                </div>
                <router-link
                  v-if="isSupervisor && !getAssignment(row.issueId)"
                  class="btn btn-warning btn-sm"
                  :to="`/audit/assignments/${row.issueId}`"
                >
                  Assign Issue
                </router-link>
              </template>
            </el-table-column>

            <!-- Actions Column -->
            <el-table-column label="Actions" width="90">
              <template v-slot="{ row }">            
                <router-link
                  class="btn btn-info btn-sm mx-1"
                  :to="`/audit/view-log/${row.issueId}`"
                >
                  <i class="fa fa-eye"></i>
                </router-link>
              </template>
            </el-table-column>
          </el-table>

        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        />
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Button, Input } from "element-ui";
import API from "@/services/api";

export default {
  name: "IssuesManagement",
  components: {
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
  },
  data() {
    return {
      // Services for APIs
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      teamMemberService: new API(process.env.VUE_APP_API_URL, "users"),
      reportService: new API(process.env.VUE_APP_API_URL, "report"),
      validateUserService: new API(
        process.env.VUE_APP_API_URL,
        "auth/validate-customer"
      ),
      searchBy: "name",
      selectedCustomer: null,
      searchByValueCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/search"
      ),
      searchByIdCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers"
      ),
      searchByAccountsByCustomerIdService: new API(
        process.env.VUE_APP_API_URL,
        "accounts"
      ),
      searchByAccountCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/account"
      ),
      btnloading: false,
      searchResults: [],
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      loading: false,
      // Status Filter
      selectedStatus: "New", // Default to show only "New" issues
      issueStatuses: ["New", "In Progress", "Resolved", "On Hold", "Closed"], // All statuses
      visibleStatuses: ["New", "In Progress", "Unassigned"], // Statuses visible in the dropdown by default

      // Table Columns
      tableColumns: [
        { prop: "issueRef", label: "Issue ID", minWidth: 120,sortable:true },
        { prop: "title", label: "Subject", minWidth: 200 },
        { prop: "status", label: "Status", minWidth: 100,sortable:true },
        { prop: "phone", label: "Phone", minWidth: 150 },
      ],

      // Dropdown Options
      issueCategories: [
        "Service Requests",
        "Incidents",
        "Problems",
        "Changes",
        "Billing Issues",
        "audit Support",
        "Customer Feedback",
        "Account Management",
      ],
      issuePriorities: ["Lowest", "Low", "Medium", "High", "Highest"],
      // Data Arrays
      issues: [],
      customers: [],
      accounts: [],
      services: [],
      departments: [],
      assignments: [],
      searchInput: "",
      // Current Issue for Form
      currentIssue: {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "",
        priority: "Medium",
        status: "New",
        district: "",
        assignedDepartmentId: null,
        issueType: "customer",
      },

      // Form Control
      showIssueForm: false,
      isEditing: false,

      // Malawi Districts
      malawiDistricts: [
        "Balaka",
        "Blantyre",
        "Chikwawa",
        "Chiradzulu",
        "Chitipa",
        "Dedza",
        "Dowa",
        "Karonga",
        "Kasungu",
        "Likoma",
        "Lilongwe",
        "Machinga",
        "Mangochi",
        "Mchinji",
        "Mulanje",
        "Mwanza",
        "Mzimba",
        "Neno",
        "Nkhata Bay",
        "Nkhotakota",
        "Nsanje",
        "Ntcheu",
        "Ntchisi",
        "Phalombe",
        "Rumphi",
        "Salima",
        "Thyolo",
        "Zomba",
      ],
    };
  },
  computed: {
    isSupervisor() {
      return JSON.parse(localStorage.getItem("isSupervisor"));
    },
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    customerId() {
      return this.loggedInUser?.account;
    },
    email() {
      return this.loggedInUser?.email;
    },
    phone() {
      return this.loggedInUser?.phoneNumber;
    },
    company() {
      return this.loggedInUser?.department;
    },
    queriedData() {
      let filtered = this.issues.filter((issue) =>
        Object.values(issue).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.queriedData.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    async fetchTeamMembers() {
      try {
        this.teamMembers = await this.teamMemberService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Team Members");
      }
    },
    formatAssignee(assignment) {
      const member = this.teamMembers.find(
        (m) => m.id === assignment.assignedTo
      );
      return member ? member.fullName : "Unknown";
    },
    async assignIssue() {
      //console.log(this.selectedAssignee);
      try {
        let startDate = new Date();
        await this.GetSLAHrsPriorty(
          this.currentIssueForAssignment.issueId,
          startDate
        );
        await this.assignmentService.create({
          issueId: this.currentIssueForAssignment.issueId,
          AssignedTo: this.selectedAssignee,
          assignedAt: new Date(),
          startDate,
          dueDate: this.dueDate,
        });

        await this.fetchAssignments();
        this.showAssignmentModal = false;
        this.$alert.success("Issue assigned successfully");
      } catch (error) {
        //console.log(error);
        this.$alert.error("Failed to assign issue");
      }
    },
    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },
    async GetSLAHrsPriorty(issueId, start) {
      let issue = await this.getIssue(issueId);

      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.customerId,
        issue.serviceId,
        start
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.dueDate = result.dueDate;
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },
    async fetchAssignments() {
      try {
        this.assignments = await this.assignmentService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Assignments");
      }
    },
    getAssignment(issueId) {
      return this.assignments.find((a) => a.issueId === issueId);
    },
    async fetchAccountByCustomerId(_customer) {
      this.btnloading = true;
      try {
        let customer=await _customer
        //console.log(customer.i_customer)
        const input = customer.i_customer;
        if (!input) {
          this.$alert.warning("Please enter a customer Id");
          return;
        }

        //console.log(input)
        const response = await this.searchByAccountsByCustomerIdService.getById(input);
        this.accounts = response

      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to find accounts"
        );
      } finally {
        this.btnloading = false;
      }
    },
    async searchCustomer() {
      this.btnloading = true;
      try {
        const input = this.searchInput.trim();
        if (!input) {
          this.$alert.warning("Please enter a customer " + searchBy);
          return;
        }

        // Check if input is a number (ID or Account)
        if (this.searchBy == "customerid") {
          // Search by ID or Account
          const response = await this.searchByIdCustomerService.getById(input);
          this.populateCustomerDetails(response);
        } else if (this.searchBy == "account") {
          // Search by ID or Account
          const response = await this.searchByAccountCustomerService.getById(
            input
          );
          this.populateCustomerDetails(response);
        } else {
          // Search by Name
          const response = await this.searchByValueCustomerService.getById(
            input + "_" + this.searchBy
          );
          this.searchResults = response;
        }
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to search for customer"
        );
      } finally {
        this.btnloading = false;
      }
    },
    onCustomerSelect(customerId) {
      const customer = this.searchResults.find(
        (c) => c.i_customer === customerId
      );
      if (customer) {
        this.populateCustomerDetails(customer);
        this.fetchAccountByCustomerId(customer)
      }
    },
    setDetails() {
      if (this.currentIssue.issueType == "noncustomer") {
        let user = this.loggedInUser;
        this.currentIssue.customerId = "0001";
        this.currentIssue.customer = user.fullName;
        this.currentIssue.company = "MTL";
        this.currentIssue.serviceId =2;
        this.currentIssue.email = user.email;

        this.currentIssue.phone = user.phoneNumber;
      } else {
        this.currentIssue.customerId = "";
        this.currentIssue.customer = "";
        this.currentIssue.company = "";

        this.currentIssue.email = "";

        this.currentIssue.phone = "";
      }
    },
    populateCustomerDetails(customer) {
      this.currentIssue.customerId = customer.i_customer;
      this.currentIssue.customer = customer.firstname + " " + customer.lastname;
      this.currentIssue.company = customer.name;

      this.currentIssue.email = customer.email;

      this.currentIssue.phone = customer.phone1 || customer.phone2;

      this.currentIssue.location = customer.city;

      // Clear search results after selection
      this.searchResults = [];
    },
    async validateUser() {
      if (!this.currentIssue.Id) {
        this.$alert.error("Please fill in all required fields.");
        return;
      }
      this.btnloading = true; // Start loading
      try {
        // Call the backend validation endpoint
        const response = await this.validateUserService.create({
          name: "",
          account: this.currentIssue.Id,
          userType: "customer",
        });

        if (response.message == "Validation successful.") {
          this.$alert.success("Client found");

          const customerData = response.customer;

          //console.log(customerData);

          // Bind the customer data to the form fields

          this.currentIssue.customerId = customerData.i_customer; // Bind customer ID
          this.currentIssue.company =
            customerData.name ||
            customerData?.firstname + " " + customerData?.lastname; // Bind customer name
          this.currentIssue.phone = customerData.phone1 || customerData.phone2; // Bind phone
          this.currentIssue.email = customerData.email; // Bind email
          this.currentIssue.district = customerData.city; // Bind location
          this.currentIssue.location = customerData.address_line_2; // Bind location

          this.currentIssue.customer =
            customerData?.firstname + " " + customerData?.lastname;
        } else {
          this.$alert.error("Client not found");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message ||
            "Error validating user. Please try again."
        );
      } finally {
        this.btnloading = false; // Stop loading
      }
    },
    handleRowClick(row) {
      this.$router.push(`/audit/issue-view/${row.issueId}`);
    },
    // Formatting Methods
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },
    formatCustomer(row, column, cellValue) {
      const customer = this.customers.find((c) => c.customerId === cellValue);
      return customer ? customer.name : "Unknown";
    },
    formatAccount(row, column, cellValue) {
      const customer = this.customers.find(
        (c) => c.customerId === row.customerId
      );
      return customer ? customer.account : "Unknown";
    },

    // Fetch Methods
    async fetchIssues() {
      this.loading = true;
      try {
        const response = await this.issueService.getAll();
        this.issues = response || [];
        this.pagination.total = this.issues.length || 0;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch Issues");
      } finally {
        this.loading = false;
      }
    },
    async fetchCustomers() {
      try {
        this.customers = await this.customerService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },
    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll())?.services ?? [];
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
        //console.log(this.departments);
      } catch (error) {
        this.$alert.error("Failed to fetch Departments");
      }
    },
    resetCurrentIssue() {
      this.currentIssue = {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
        district: "",
        assignedDepartmentId: null,
      };
    },
    editIssue(row) {
      this.currentIssue = { ...row };
      this.isEditing = true;
      this.showIssueForm = true;
    },
    closeIssueForm() {
      this.resetCurrentIssue();
      this.isEditing = false;
      this.showIssueForm = false;
    },
    async submitIssue() {
      if (this.isEditing) {
        await this.updateIssue();
      } else {
        await this.addIssue();
      }

      //window.scrollTo({ top: 0, behavior: 'smooth' });
    },
    async addIssue() {
      try {
        const newObject = { ...this.currentIssue };
        delete newObject.issueId;
        if (newObject.assignedDepartmentId) {
          newObject.assignedDate = new Date();
        } else {
          newObject.assignedDepartmentId = 0;
        }

        newObject.source = "System";
        await this.issueService.create(newObject);
        this.$alert.success("Issue added successfully");
        this.closeIssueForm();
        this.$nextTick(() => {
          window.scrollTo({ top: 0, behavior: "smooth" });
        });
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to add Issue"
        );
      }
    },
    scrollToTop() {
      //console.log("Scrolling to top...");
      if (document.documentElement.scrollTop || document.body.scrollTop > 0) {
        //console.log("Scrolling window to top");
        window.scrollTo({ top: 0, behavior: "smooth" });
      } else {
        //console.log("Scrolling scrollable parent to top");
        const scrollableParent = this.getScrollableParent(this.$el);
        if (scrollableParent) {
          //console.log("Scrollable parent found:", scrollableParent);
          scrollableParent.scrollTo({ top: 0, behavior: "smooth" });
        } else {
          //console.log("No scrollable parent found");
        }
      }
    },
    getScrollableParent(element) {
      while (element) {
        if (element.scrollHeight > element.clientHeight) {
          return element;
        }
        element = element.parentElement;
      }
      return null;
    },
    async updateIssue() {
      try {
        await this.issueService.update(
          this.currentIssue.issueId,
          this.currentIssue
        );
        this.$alert.success("Issue updated successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update Issue"
        );
      }
    },
    async loadall() {
      await Promise.all([
        await this.fetchAssignments(),
        await this.fetchDepartments(),
        await this.fetchTeamMembers(),
        this.fetchIssues(),
        this.fetchCustomers(),
        this.fetchServices(),
        this.fetchDepartments()
      ]);
    },
    getDepartment(departmentId) {
      const department = this.departments.find(
        (d) => d.departmentId === departmentId
      );
      return department ? department.name : "Unknown Department";
    },
  },
  created() {
    this.loadall();
  },
};
</script>

<style>
tr,
.el-table__row {
  cursor: pointer !important;
}
</style>