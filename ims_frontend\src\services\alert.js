import Swal from 'sweetalert2';

class AlertService {
  /**
   * Show a SweetAlert
   * @param {Object} options Alert configuration
   * @param {string} options.message Alert message
   * @param {string} [options.type='info'] Alert type (success, error, warning, info, question)
   * @param {number} [options.duration=3000] Duration to show alert (set to `null` for no auto-close)
   * @param {boolean} [options.showConfirmButton=false] Show confirm button
   * @param {string} [options.title=''] Optional alert title
   * @param {Object} [options.additionalOptions={}] Additional SweetAlert2 options
   */
  static show(options) {
    // Default options
    const defaultOptions = {
      text: options.message,
      icon: options.type || 'info',
      timer: options.duration || 3000,
      showConfirmButton: options.showConfirmButton || false,
      title: options.title || '',
      toast: true,
      position: 'top-end',
      showCloseButton: true,
      timerProgressBar: true,
      didOpen: (toast) => {
        toast.addEventListener('mouseenter', Swal.stopTimer);
        toast.addEventListener('mouseleave', Swal.resumeTimer);
      },
    };

    // Merge provided options with defaults
    const alertOptions = {
      ...defaultOptions,
      ...options.additionalOptions,
      customClass: {
        popup: 'swal2-custom-z-index'
      }
    };

    // Remove timer if duration is set to null
    if (options.duration === null) {
      delete alertOptions.timer;
    }

    return Swal.fire(alertOptions);
  }

  // Convenience methods for different alert types
  static success(message, options = {}) {
    return this.show({
      message,
      type: 'success',
      ...options,
    });
  }

  static successPermanent(message, options = {}) {
    return Swal.fire({
      html: message, // Use 'html' instead of 'text' to allow HTML content
      icon: 'success',
      showConfirmButton: true,
      confirmButtonText: options.okButtonText || 'OK',
      ...options.additionalOptions, // Include any additional options
    }).then((result) => {
      if (result.isConfirmed && typeof options.onOk === 'function') {
        options.onOk();
      }
    });
  }
  

  static error(message, options = {}) {
    return this.show({
      message,
      type: 'error',
      ...options,
    });
  }

  static warning(message, options = {}) {
    return this.show({
      message,
      type: 'warning',
      ...options,
    });
  }

  static info(message, options = {}) {
    return this.show({
      message,
      type: 'info',
      ...options,
    });
  }

  // Confirm dialog method
  static confirm(message, options = {}) {
    const confirmOptions = {
      title: 'Are you sure?',
      text: message,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, proceed!',
      ...options,
    };

    return Swal.fire(confirmOptions);
  }
}

export default AlertService;
