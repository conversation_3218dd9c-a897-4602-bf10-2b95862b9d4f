<template>
  <div class="timeline-container">
    <card class="mr-3">
      <div class="card-title">
        <div class="d-flex align-items-center row">
          <div class="col-8"><h4 class="card-title">Issue Timeline</h4></div>
          <div class="col-4 text-right">
            <router-link class="btn btn-primary mx-2" to="/customer/issues">
              Add Issue
            </router-link>
          </div>
        </div>
      </div>
      <div class="filters container mb-4 p-3 bg-light rounded">
        <div class="row gy-3">
          <!-- Status Filter -->
          <div class="col-md-3 col-6">
            <label for="status-filter" class="form-label">Status:</label>
            <el-select
              v-model="filters.status"
              id="status-filter"
              placeholder="Select Status"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option value="Pending" label="Pending"></el-option>
              <el-option value="In Progress" label="In Progress"></el-option>
              <el-option value="Resolved" label="Resolved"></el-option>
            </el-select>
          </div>

          <!-- Department Filter -->
          <div class="col-md-3 col-6">
            <label for="department-filter" class="form-label">Department:</label>
            <el-select
              v-model="filters.department"
              id="department-filter"
              placeholder="Select Department"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option value="Engineering" label="Engineering"></el-option>
              <el-option value="IT Support" label="IT Support"></el-option>
              <el-option value="HR" label="HR"></el-option>
            </el-select>
          </div>

          <!-- Organization Filter -->
          <div class="col-md-3 col-6">
            <label for="organization-filter" class="form-label">Organization:</label>
            <el-select
              v-model="filters.organization"
              id="organization-filter"
              placeholder="Select Organization"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option value="XYZ Corp" label="XYZ Corp"></el-option>
              <el-option value="ABC Ltd" label="ABC Ltd"></el-option>
            </el-select>
          </div>

          <!-- Time Filter -->
          <div class="col-md-3 col-6">
            <label for="time-filter" class="form-label">Time:</label>
            <el-select
              v-model="filters.time"
              id="time-filter"
              placeholder="Select Time"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option value="today" label="Today"></el-option>
              <el-option value="past-week" label="Past Week"></el-option>
              <el-option value="past-month" label="Past Month"></el-option>
            </el-select>
          </div>

          <!-- Search Filter -->
          <div class="col-12 pt-3">
            <label for="search-filter" class="form-label">Search:</label>
            <FgInput
              v-model="filters.search"
              id="search-filter"
              placeholder="Search by issue ID or description"
              class="w-100"
            />
          </div>
        </div>
      </div>

      <div class="issue-counter text-left mb-3 ml-3">
        <p><b>{{ filteredIssues.length }} Issues Found</b></p>
      </div>
    </card>

    <!-- Timeline -->
    <div class="timeline container">
  <el-timeline>
    <el-timeline-item 
      v-for="issue in filteredIssues" 
      :key="issue.issueId"
      :timestamp="formatDate(issue.createdAt)"
      :color="getStatusClass(issue)"
    >
      <router-link :to="`/customer/issue-timeline/${issue.issueId}`">
        <div class="timeline-content bg-light p-3 rounded shadow">
          <div class="issue-header d-flex justify-content-between">
            <!-- Issue ID -->
            <span class="issue-id fw-bold">#{{ issue.issueId }}</span>
            <!-- Issue Status -->
            <span class="issue-status" :class="getStatusTextClass(issue)">
              {{ issue.status }}
            </span>
          </div>
          <div class="issue-details">
            <!-- Issue Details -->
            <p><strong>Department:</strong> {{ issue.department }}</p>
            <p><strong>Assigned To:</strong> {{ issue.assignedTo }}</p>
            <p><strong>Organization:</strong> {{ issue.organization }}</p>
            <p><strong>SLA:</strong> {{ issue.sla }} hour(s)</p>
          </div>
        </div>
      </router-link>
    </el-timeline-item>
  </el-timeline>
</div>

  </div>
</template>

<script>
import { FormGroupInput as FgInput } from "src/components/index";
import { Select, Option } from "element-ui";
import API from "@/services/api";

export default {
  components: {
    FgInput,
    "el-select": Select,
    "el-option": Option,
  },
  data() {
    return {
      issuesService: new API(
        process.env.VUE_APP_API_URL,
        "Issues/all-issues"
      ),
      issues: [],
      filters: {
        status: "",
        department: "",
        organization: "",
        time: "",
        search: "",
      },
    };
  },
  computed: {
    filteredIssues() {
      return this.issues.filter((issue) => {
        const matchesStatus = !this.filters.status || issue.status === this.filters.status;
        const matchesDepartment = !this.filters.department || issue.department === this.filters.department;
        const matchesOrganization = !this.filters.organization || issue.organization === this.filters.organization;
        const matchesTime = this.applyTimeFilter(issue.createdAt);
        const matchesSearch = this.applySearchFilter(issue);

        return matchesStatus && matchesDepartment && matchesOrganization && matchesTime && matchesSearch;
      });
    },
  },
  methods: {
    async fetchIssues() {
      try {
        const response = await this.issuesService.getAll();
        this.issues = response;
      } catch (error) {
        console.error("Error fetching issues:", error);
      }
    },

    applyTimeFilter(createdAt) {
      if (!this.filters.time) return true;
      
      const now = new Date();
      const createdDate = new Date(createdAt);
      
      switch (this.filters.time) {
        case "today":
          return createdDate.toDateString() === now.toDateString();
        case "past-week":
          const weekAgo = new Date(now.setDate(now.getDate() - 7));
          return createdDate >= weekAgo;
        case "past-month":
          const monthAgo = new Date(now.setMonth(now.getMonth() - 1));
          return createdDate >= monthAgo;
        default:
          return true;
      }
    },

    applySearchFilter(issue) {
      if (!this.filters.search) return true;
      
      const searchTerm = this.filters.search.toLowerCase();
      return (
        issue.issueId.toString().includes(searchTerm) ||
        issue.department.toLowerCase().includes(searchTerm) ||
        issue.organization.toLowerCase().includes(searchTerm) ||
        issue.assignedTo.toLowerCase().includes(searchTerm)
      );
    },

    formatDate(date) {
      return new Date(date).toLocaleString("default", {
        month: "long",
        day: "numeric",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    getStatusClass(issue) {
      const now = new Date();
      const createdAt = new Date(issue.createdAt);
      const diffInMs = now - createdAt;
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      if (issue.status === "Resolved") return "status-resolved";
      if (diffInMinutes >= issue.sla) return "status-overdue";
      if (diffInMinutes >= issue.sla * 0.8) return "status-near-sla";
      return "status-in-progress";
    },

    getStatusTextClass(issue) {
      const statusClass = this.getStatusClass(issue);
      const classMap = {
        "status-resolved": "text-green-600",
        "status-overdue": "text-red-600",
        "status-near-sla": "text-yellow-600",
        "status-in-progress": "text-blue-600",
      };
      return classMap[statusClass];
    },

    getStatusIcon(issue) {
      const statusMap = {
        Resolved: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check text-green-500"><polyline points="20 6 9 17 4 12"></polyline></svg>`,
        "In Progress": `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-refresh-cw text-blue-500"><polyline points="23 4 23 10 17 10"></polyline><polyline points="20.49 15 17 15 17 20.49"></polyline><path d="M9 3.49V6h2.91a8.91 8.91 0 1 1-2.91 7H6a11 11 0 1 0 3-7.49z"></path></svg>`,
        Pending: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-pause text-yellow-500"><rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect></svg>`,
      };

      const now = new Date();
      const createdAt = new Date(issue.createdAt);
      const diffInMs = now - createdAt;
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      if (issue.status === "Resolved") return statusMap["Resolved"];
      if (diffInMinutes >= issue.sla)
        return `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-alert-triangle text-red-500"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>`;
      if (diffInMinutes >= issue.sla * 0.8) return statusMap["Pending"];
      return statusMap["In Progress"];
    },
  },
  mounted() {
    this.fetchIssues();
  },
};
</script>

<style scoped>
.timeline-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.timeline-line {
  width: 2px;
  height: 100%;
  background-color: #ddd;
  margin: auto;
}

.timeline-content {
  max-width: 100%;
}

.timeline-container {
  margin: 0 auto;
  font-family: "Arial", sans-serif;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  color: #333;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.timeline-item:hover {
  transform: scale(1.02);
}

.status-resolved { background-color: #10b981; }
.status-overdue { background-color: #ef4444; }
.status-near-sla { background-color: #fbbf24; }
.status-in-progress { background-color: #3b82f6; }
</style>