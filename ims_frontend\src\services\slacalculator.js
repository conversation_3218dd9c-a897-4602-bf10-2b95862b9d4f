import API from "@/services/api";

export class SLACalculator {
    constructor() {
        this.customerServiceSLAService = new API(process.env.VUE_APP_API_URL, "slas");
    }

    /**
     * Calculate due date based on customer and service specific SLA
     * 
     * @param {number} customerId - ID of the customer
     * @param {number} serviceId - ID of the service
     * @param {Date} [startDate] - Optional start date (defaults to current date/time)
     * @returns {Promise<Date>} Calculated due date
     */
    async calculateSLADueDate(serviceId, startDate = new Date()) {
        try {
            // Fetch call-center specific SLA
            const slaConfig = await this.getSLAHoursAndPriority(serviceId);

            if (!slaConfig) {
                // If no SLA found, use a default of 48 hours
                return this.calculateDueDate(48, startDate);
            }

            return this.calculateDueDate(serviceSLA.defaultSLAHours, startDate);


            // Use call-center specific SLA hours
            return this.calculateDueDate(slaConfig.slaHours, startDate);
        } catch (error) {
           // console.error("Error calculating SLA due date:", error);

            // Fallback to a standard 48-hour SLA in case of any errors
            return this.calculateDueDate(48, startDate);
        }
    }


    async getSLAHoursAndPriority(serviceId,start) {
        try {
            const response = await this.customerServiceSLAService.getAll();

            // Filter the response by both customerId and serviceId
            const matchedSLA = response.find(
                sla => sla.serviceId == serviceId
            );

            // If a matching SLA is found, return its hours and priority
            if (matchedSLA) {
                 return {
                    dueDate:  await this.calculateDueDate(matchedSLA.resolutionTimeHours,start),
                    priority: matchedSLA.priority
                };
            }

            // Return null if no matching SLA is found
            return null;
        } catch (error) {
            // Consider adding error handling
            //console.error('Error fetching SLA:', error);
            throw error;
        }
    }

    /**
     * Fetch default service SLA configuration
     * 
     * @param {number} serviceId 
     * @returns {Promise<Object|null>} Service SLA configuration or null
     */
    async fetchDefaultServiceSLA(serviceId) {
        try {
            const service = await this.serviceService.getById(serviceId);
            return service.defaultSLAConfiguration || null;
        } catch (error) {
            //console.warn("Could not fetch default service SLA:", error);
            return null;
        }
    }

    /**
     * Calculate due date based on hours
     * 
     * @param {number} slaHours - Number of hours for SLA
     * @param {Date} startDate - Start date for calculation
     * @returns {Date} Calculated due date
     */
    calculateDueDate(slaHours, startDate) {
        return this.calculateSLADueDate(slaHours, startDate, {
            excludeWeekends: false,
            workingHoursStart: [7, 0],
            workingHoursEnd: [17, 0]
        });
    }

    /**
     * Calculate due date with working hours consideration
     * 
     * @param {number} slaHours - Number of hours for SLA
     * @param {Date} startDate - Start date for calculation
     * @param {Object} options - SLA calculation options
     * @returns {Date} Calculated due date
     */
    calculateSLADueDate(
        slaHours,
        startDate = new Date(),
        options = {}
    ) {
        // Default options
        const defaultOptions = {
            excludeWeekends: false,
            workingHoursStart: [7, 0],  // 9:00 AM
            workingHoursEnd: [17, 0],   // 5:00 PM
        };

        // Merge provided options with defaults
        const config = { ...defaultOptions, ...options };

        // Clone the start date to avoid modifying the original
        let currentDate = new Date(startDate);

        // Remaining hours to calculate
        let remainingHours = slaHours;

        while (remainingHours > 0) {
            // Check if current day is a weekend and should be skipped
            if (config.excludeWeekends &&
                (currentDate.getDay() === 0 || currentDate.getDay() === 6)) {
                currentDate.setDate(currentDate.getDate() + 1);
                currentDate.setHours(config.workingHoursStart[0], config.workingHoursStart[1], 0, 0);
                continue;
            }

            // Set working hours boundaries for the current day
            const workDayStart = new Date(currentDate);
            workDayStart.setHours(config.workingHoursStart[0], config.workingHoursStart[1], 0, 0);

            const workDayEnd = new Date(currentDate);
            workDayEnd.setHours(config.workingHoursEnd[0], config.workingHoursEnd[1], 0, 0);

            // If current time is before work hours, set to work day start
            if (currentDate < workDayStart) {
                currentDate = workDayStart;
            }

            // Calculate available hours in the current work day
            const availableHoursInDay =
                (workDayEnd - (currentDate > workDayStart ? currentDate : workDayStart)) / (1000 * 60 * 60);

            if (remainingHours <= availableHoursInDay) {
                // We can complete the hours in this day
                currentDate = new Date(currentDate.getTime() + remainingHours * 60 * 60 * 1000);
                remainingHours = 0;
            } else {
                // Move to next day
                remainingHours -= availableHoursInDay;
                currentDate.setDate(currentDate.getDate() + 1);
                currentDate.setHours(config.workingHoursStart[0], config.workingHoursStart[1], 0, 0);
            }
        }

        return currentDate;
    }
}

// Singleton instance for easy importing
export const slaCalculator = new SLACalculator();