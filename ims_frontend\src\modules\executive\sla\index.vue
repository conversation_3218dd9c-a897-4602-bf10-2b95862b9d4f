<template>
  <div class="sla-management">
    <!-- SLA Form -->
    <card v-if="showSLAForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">{{ isEditing ? "Edit SLA" : "Add SLA" }}</h4>
      </template>
      <form @submit.prevent="submitSLA">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Service">
              <el-select
                v-model="currentSLA.serviceId"
                placeholder="Select service"
                filterable
                class="w-100"
              >
              <el-option
                  v-for="service in services"
                  :key="service.i_service_type"
                  :label="`${service.name} - ${service.i_service_type}`"
                  :value="service.i_service_type"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Priority">
              <el-select
                v-model="currentSLA.priority"
                placeholder="Select priority"
                class="w-100"
              >
                <el-option
                  v-for="priority in slaPriorities"
                  :key="priority"
                  :label="priority"
                  :value="priority"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input
              label="Resolution Time (Hours)"
              type="number"
              v-model="currentSLA.resolutionTimeHours"
              :required="true"
              placeholder="Enter resolution time"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update SLA" : "Add SLA" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeSLAForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- SLA List -->
    <card>
      <template slot="header">
        <div class="d-flex justify-content-between align-items-center">
          <h4 class="card-title">Service Level Agreements</h4>
          <div>
            <button class="btn btn-sm btn-primary" @click="showAddSLAForm">
              <i class="fa fa-plus"></i> Add SLA
            </button>
            <button class="btn btn-sm btn-success ml-2" @click="navigateToEscalations">
              <i class="fa fa-cog"></i> Manage Escalations
            </button>
          </div>
        </div>
        <p class="card-category">Manage service level agreements</p>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search records"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <div v-if="loading" class="loader-container">
            <div class="loader"></div>
            <p>Loading data...</p>
          </div>
          <el-table  v-else-if="services.length > 0"   :data="queriedData" stripe border style="width: 100%">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            ></el-table-column>


            <!-- Actions Column -->
            <el-table-column label="Actions" width="280" >
              <template v-slot="{ row }">
                <button class="btn btn-info btn-sm mr-1" @click="editSLA(row)" title="Edit SLA">
                  <i class="fa fa-pencil"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm mr-1"
                  @click="deleteSLA(row.slaid)"
                  title="Delete SLA"
                >
                  <i class="fa fa-trash"></i>
                </button>
                <button
                  class="btn btn-success btn-sm"
                  @click="navigateToServiceEscalations(row.serviceId)"
                  title="Manage Escalations"
                >
                  <i class="fa fa-cog"></i> Escalations
                </button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input,Skeleton,SkeletonItem } from "element-ui";
import API from "@/services/api"; // Importing this.slaService service

export default {
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input
  },

  data() {
    return {
      loading: true,
      slaService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "slas" // Endpoint
      ),
      customerService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "customers" // Endpoint
      ),
      servsService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "services" // Endpoint
      ),
      searchQuery: "",
      pagination: {
        perPage: 20,
        currentPage: 1,
        perPageOptions: [20, 40, 60],
        total: 0,
      },
      tableColumns: [
        { prop: "serviceId", label: "Service", formatter: (row, column) => this.getServiceById(row.serviceId), minWidth: 120 },
        { prop: "priority", label: "Priority", minWidth: 120 },
        {
          prop: "resolutionTimeHours",
          label: "Resolution Time (Hours)",
          minWidth: 160,
        },
      ],
      slaPriorities: ["Low", "Medium", "High", "Critical"], // Priority options
      slas: [], // Data to display
      services: [],
      currentSLA: {
        slaid: null,
        serviceId: "",
        priority: "Medium",
        resolutionTimeHours: 0,
      },
      customers: [],
      showSLAForm: false,
      isEditing: false,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.slas.filter((sla) =>
        Object.values(sla).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.slas.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    getCustomerById(id) {
      const customer = this.customers.find((c) => c.customerId === id);
      return customer ? customer.name : "Unknown Customer"; // Default to "Unknown Customer" if not found
    },

    // Function to get service by ID
    getServiceById(id) {
      const service = this.services.find((s) => s.i_service_type === id);
      return service ? service.name : "Unknown Service"; // Default to "Unknown Service" if not found
    },

    async fetchSLAs() {
      try {
        const response = await this.slaService.getAll();

        this.slas = response; // Assuming response contains the data array
        this.pagination.total = response.length; // Assuming response contains total count
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch SLAs");
        console.error("Error fetching SLAs:", error);
      }
    },

    async submitSLA() {
      if (this.isEditing) {
        await this.updateSLA();
      } else {
        await this.addSLA();
      }
    },

    async addSLA() {
      try {
        const newObject = { ...this.currentSLA };
        delete newObject.slaid; // Remove ID for new SLA

        await this.slaService.create(newObject); // Call this.slaService to create a new SLA
        this.$alert.success("SLA added successfully");
        await this.fetchSLAs(); // Refresh the list after adding
        this.closeSLAForm();
      } catch (error) {
        this.$alert.error(error.response?.data?.message || "Failed to add SLA");
      }
    },

    async updateSLA() {
      try {
        await this.slaService.update(this.currentSLA.slaid, this.currentSLA); // Call this.slaService to update existing SLA
        await this.fetchSLAs(); // Refresh the list after updating
        this.$alert.success("SLA updated successfully");
        this.closeSLAForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update SLA"
        );
      }
    },

    editSLA(sla) {
      this.currentSLA = { ...sla };
      this.isEditing = true;
      this.showSLAForm = true;
    },

    async deleteSLA(slaid) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this SLA?"
        );
        if (confirmDelete.isConfirmed) {
          await this.slaService.delete(slaid); // Call this.slaService to delete the SLA
          await this.fetchSLAs(); // Refresh the list after deletion
          this.$alert.success("SLA deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete SLA"
        );
      }
    },

    closeSLAForm() {
      this.isEditing = false;
      this.showSLAForm = false;
      // Reset current SLA object
      this.currentSLA = {
        slaid: null,
        customerId: "",
        serviceId: "",
        priority: "Medium",
        resolutionTimeHours: 0,
      };
    },

    showAddSLAForm() {
      this.isEditing = false;
      this.showSLAForm = true;
      this.currentSLA = {
        slaid: null,
        customerId: "",
        serviceId: "",
        priority: "Medium",
        resolutionTimeHours: 0,
      };
    },

    navigateToEscalations() {
      this.$router.push('/executive/escalations');
    },

    navigateToServiceEscalations(serviceId) {
      // Navigate to escalations page with service ID as query parameter
      this.$router.push({
        path: '/executive/escalations',
        query: { serviceId: serviceId }
      });
    },
  },
  async created() {
    this.loading = true;
    // Fetch SLAs when component is created
    this.fetchSLAs();

    this.customers = await this.customerService.getAll();
    this.services = await this.servsService.getAll();
    this.services=this.services.services
    this.slas;

    this.loading = false;
  },
};
</script>

<style>
/* Loader Styles */
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loader {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #302e77;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

