<template>
  <div class="service-management">
    <!-- Service Form -->
    <card v-if="showServiceForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">{{ isEditing ? "Edit Service" : "Add Service" }}</h4>
      </template>
      <form @submit.prevent="submitService">
        <div class="row">
          <div class="col-md-4">
            <fg-input
              label="Service Name"
              type="text"
              v-model="currentService.serviceName"
              :required="true"
              placeholder="Enter service name"
            />
          </div>
          <div class="col-md-4">
            <fg-input
              label="Service Code"
              type="text"
              v-model="currentService.serviceCode"
              :required="true"
              placeholder="Enter service code"
            />
          </div>
          <div class="col-md-4">
            <fg-input label="Category">
              <el-select
                v-model="currentService.category"
                placeholder="Select service category"
                class="w-100"
                :required="true"
              >
                <el-option
                  v-for="category in serviceCategories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <fg-input label="Description">
              <el-input
                v-model="currentService.description"
                placeholder="Enter service description"
                type="textarea"
                rows="3"
              />
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Status">
              <el-select
                v-model="currentService.status"
                placeholder="Select service status"
                class="w-100"
              >
                <el-option
                  v-for="status in serviceStatuses"
                  :key="status"
                  :label="status"
                  :value="status"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Service" : "Add Service" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeServiceForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>
    
    <!-- Service List -->
    <card>
      <template slot="header">
        <div class="d-flex justify-content-between align-items-center">
          <h4 class="card-title">Service List</h4>
        </div>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            class="select-default mb-3"
            style="width: 200px"
            v-model="pagination.perPage"
            placeholder="Per page"
          >
            <el-option
              class="select-default"
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <el-input
            type="search"
            class="mb-3"
            style="width: 200px"
            placeholder="Search records"
            v-model="searchQuery"
            aria-controls="datatables"
          />
        </div>
        <div class="col-sm-12">
          <el-table :data="queriedData" style="width: 100%" stripe border>
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
            />
          </el-table>
        </div>
      </div>

      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <div class="">
          <p class="card-category">
            Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
          </p>
        </div>
        <l-pagination
          class="pagination-no-border"
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input } from "element-ui";
import API from "@/services/api";

export default {
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
  },
  data() {
    return {
      serviceService: new API(
        process.env.VUE_APP_API_URL,
        "services"
      ),
      searchQuery: "",
      pagination: {
        perPage: 20,
        currentPage: 1,
        perPageOptions: [20, 40, 60],
        total: 0,
      },
      tableColumns: [
        { prop: "name", label: "Name", minWidth: 150 },
        { prop: "i_service_type", label: "Code", minWidth: 150 },
      ],
      services: [],
      serviceCategories: [
        'Internet',
        'Email',
        'VPN',
        'Network',
        'Other'
      ],
      serviceStatuses: [
        'Active',
        'Inactive'
      ],
      currentService: {
        serviceId: null,
        serviceName: "",
        description: "",
        category: "",
        status: "Active",
      },
      showServiceForm: false,
      isEditing: false,
      loading: false,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.services?.filter((service) =>
        Object.values(service).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.services.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    async fetchServices() {
      this.loading = true;
      try {
        const response = await this.serviceService.getAll();
        this.services = response.services;
        this.pagination.total = response.services?.length;
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to fetch services"
        );
      } finally {
        this.loading = false;
      }
    },
    async addService() {
      try {
        let newObject = { ...this.currentService };
        delete newObject.serviceId;
        const response = await this.serviceService.create(newObject);
        this.closeServiceForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to add service"
        );
        console.error("Error adding service:", error);
      }
    },
    async updateService() {
      try {
        const response = await this.serviceService.update(
          this.currentService.serviceId,
          this.currentService
        );

        this.$alert.success("Service updated successfully");
        this.closeServiceForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update service"
        );
        console.error("Error updating service:", error);
      }
    },
    async deleteService(serviceId) {
      try {
        // Confirm before deletion
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this service?"
        );

        if (confirmDelete.isConfirmed) {
          await this.serviceService.delete(serviceId);

          // Remove from local state
          this.services = this.services?.filter(
            (s) => s.serviceId !== serviceId
          );

          this.$alert.success("Service deleted successfully");
          this.fetchServices();
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete service"
        );
      }
    },
    async submitService() {
      this.loading = true;
      try {
        if (this.isEditing) {
          await this.updateService();
        } else {
          await this.addService();
        }

        this.fetchServices();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to submit service"
        );
      } finally {
        this.loading = false;
      }
    },
    editService(service) {
      this.currentService = { ...service };
      this.isEditing = true;
      this.showServiceForm = true;
    },
    closeServiceForm() {
      this.isEditing = false;
      this.showServiceForm = false;
      this.currentService = {
        serviceId: null,
        serviceName: "",
        description: "",
        category: "",
        status: "Active",
      };
    },
  },
  created() {
    this.fetchServices();
  },
};
</script>