<template>
  <div class="py-2">
    <!-- Quick Action Buttons -->
    <div class="row mb-4">
      <div class="col-12 text-start">
        <router-link
          to="/call-center/issues"
          class="btn btn-primary me-2 mr-3"
          @click="openReportIssue"
        >
          <i class="fas fa-plus-circle me-1"></i> Report New Issue
        </router-link>
      </div>
    </div>
    <!-- Filter Section -->
    <div class="row mb-1">
      <div class="col-12">
        <card>
          <div class="row">
            <div class="col-md-3">
              <fg-input label="Department">
                <el-select
                  v-model="filters.department"
                  placeholder="Filter by department"
                  class="w-100"
                >
                  <el-option
                    v-for="dept in departments"
                    :key="dept.departmentId"
                    :label="dept.name"
                    :value="dept.departmentId"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-3">
              <fg-input label="Status">
                <el-select
                  v-model="filters.status"
                  placeholder="Filter by status"
                  class="w-100"
                >
                  <el-option
                    v-for="status in issueStatuses"
                    :key="status"
                    :label="status"
                    :value="status"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-3">
              <fg-input label="District">
                <el-select
                  v-model="filters.district"
                  placeholder="Filter by district"
                  class="w-100"
                >
                  <el-option
                    v-for="district in districts"
                    :key="district"
                    :label="district"
                    :value="district"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-3">
              <fg-input label="Organization">
                <el-select
                  v-model="filters.organization"
                  placeholder="Filter by organization"
                  class="w-100"
                >
                  <el-option
                    v-for="org in organizations"
                    :key="org"
                    :label="org"
                    :value="org"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Date Range">
                <el-date-picker
                  v-model="filters.dateRange"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="Start date"
                  end-placeholder="End date"
                  class="w-100"
                />
              </fg-input>
            </div>
            <div class="col-md-3 mt-4">
              <button class="btn btn-secondary mt-2" @click="resetFilters">
                Reset
              </button>
            </div>
          </div>
        </card>
      </div>
    </div>

    <!-- Stats Cards Row -->
    <div class="row">
      <div
        class="col-12 col-sm-6 col-xl-3 mb-1"
        v-for="(stat, index) in statsList"
        :key="index"
      >
        <stats-card
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :color="stat.color"
          :loading="loading"
        >
          <template #footer>
            <div v-if="stat.footer" class="d-flex justify-content-between">
              <small v-for="(footerText, idx) in stat.footer" :key="idx">{{
                footerText
              }}</small>
            </div>
          </template>
        </stats-card>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12 col-lg-8 mb-3">
        <div class="card h-100">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h5 class="card-title mb-0">
              Issue Trends [Since
              {{ formatDate(getDateForOneYearFromToday()) }}]
            </h5>
            <select
              v-model="trendsPeriod"
              class="form-select form-select-sm w-auto"
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
          </div>
          <div class="card-body">
            <div id="trends-chart"></div>
          </div>
        </div>
      </div>

      <div class="col-12 col-lg-4 mb-3">
        <div class="card h-100">
          <div class="card-header">
            <h5 class="card-title mb-0">Issues by Category</h5>
          </div>
          <div class="card-body">
            <div id="category-chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Issues Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h5 class="card-title mb-0">Recent Issues</h5>
            <button
              class="btn btn-sm btn-outline-primary"
              @click="refreshIssues"
            >
              <i class="fas fa-sync-alt"></i> Refresh
            </button>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Assigned To</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody v-if="!loading">
                  <tr
                    v-for="issue in recentFilteredIssues"
                    :key="issue.issueId"
                  >
                    <td>{{ issue.issueRef }}</td>
                    <td>{{ issue.title }}</td>
                    <td>{{ issue.organization }}</td>
                    <td>
                      <span :class="getStatusBadgeClass(issue.status)">
                        {{ issue.status }}
                      </span>
                    </td>
                    <td>
                      {{
                        issue.assignedDepartmentId
                          ? getDepartment(issue.assignedDepartmentId)
                          : "Unassigned"
                      }}
                    </td>
                    <td>{{ formatDate(issue.reportedAt) }}</td>
                    <td>
                      <button
                        class="btn btn-sm btn-outline-primary me-1"
                        @click="viewIssue(issue.issueId)"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <tr>
                    <td colspan="7" class="text-center">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="card-footer text-center">
            <router-link
              class="btn btn-outline-primary"
              to="/call-center/issues"
            >
              View All Issues
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import StatsCard from "@/components/Cards/Tile.vue";
import * as dc from "dc";
import * as crossfilter from "crossfilter2";
import * as d3 from "d3";
import API from "@/services/api";
import moment from "moment";

export default {
  name: "Dashboard",

  components: {
    StatsCard,
  },

  data() {
    return {
      issuesService: new API(process.env.VUE_APP_API_URL, "Issues/all-issues"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      issues: [],
      period: "",
      statsList: [],
      loading: true,
      trendsPeriod: "30",
      stats: {
        activeIssues: 0,
        criticalIssues: 0,
        highPriorityIssues: 0,
        slaComplianceRate: 0,
        issuesWithinSLA: 0,
        pendingAssignments: 0,
        recentAssignments: 0,
        satisfactionRate: 0,
        totalFeedbacks: 0,
      },
      recentIssues: [],
      ndx: null, // Crossfilter instance
      filters: {
        department: this.loggedInUser?.departmentId, // Set initial department filter to user's department
        status: "",
        district: "",
        organization: "",
        dateRange: [],
      },

      issueCategories: [
        "Service Requests",
        "Incidents",
        "Problems",
        "Changes",
        "Billing Issues",
        "Technical Support",
        "Customer Feedback",
        "Account Management",
      ],
      issuePriorities: ["Lowest", "Low", "Medium", "High", "Highest"],
      issueStatuses: ["New", "In Progress", "Resolved", "On Hold", "Closed"],
      issueSources: ["Call Center", "Website", "Email", "Direct Entry"],
      departments: [],
      districts: [
        "Dedza",
        "Lilongwe",
        "Blantyre",
        "Zomba",
        "Mzuzu",
        // Add more districts as needed
      ],
      organizations: [], // Will be populated from unique organizations in issues
    };
  },

  // async mounted() {
  //   await this.fetchDepartments();
  //   await this.fetchDashboardData();
  //   this.renderCharts(); // Add this line to render charts after data is loaded
  //   this.startAutoRefresh();
  // },

  async mounted() {
    await this.fetchDepartments();

    // Set initial department filter to user's department
    this.filters.department = this.loggedInUser?.departmentId || null;

    await this.fetchDashboardData();
    this.renderCharts();
    this.startAutoRefresh();
  },

  beforeDestroy() {
    this.stopAutoRefresh();
    if (this.ndx) {
      this.ndx.remove(); // Clean up crossfilter
    }
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    company() {
      return this.loggedInUser?.department;
    },
    // filteredIssues() {
    //   let filtered = this.issues;
    //   // Handle department filtering
    //   if (this.filters.department !== null) {
    //     // If specific department is selected
    //     filtered = filtered.filter(
    //       (issue) => issue.assignedDepartmentId === this.filters.department
    //     );
    //   } else {
    //     // If department is null (reset was clicked), show ALL issues
    //     filtered = this.issues;
    //   }

    //   // Rest of your filtering code...

    //   return filtered;
    // },
    filteredIssues() {
  let filtered = this.issues;
  
  // Handle department filtering
  if (this.filters.department !== null) {
    // If specific department is selected
    filtered = filtered.filter(
      (issue) => issue.assignedDepartmentId === this.filters.department
    );
  }

  // Apply status filter
  if (this.filters.status) {
    filtered = filtered.filter(
      (issue) => issue.status === this.filters.status
    );
  }

  // Apply district filter
  if (this.filters.district) {
    filtered = filtered.filter(
      (issue) => issue.district === this.filters.district
    );
  }

  // Apply organization filter
  if (this.filters.organization) {
    filtered = filtered.filter(
      (issue) => issue.organization === this.filters.organization
    );
  }

  // Apply date range filter
  if (this.filters.dateRange && this.filters.dateRange.length === 2) {
    const [start, end] = this.filters.dateRange;
    filtered = filtered.filter(issue => {
      const issueDate = new Date(issue.reportedAt);
      return issueDate >= start && issueDate <= end;
    });
  }

  return filtered;
},
    queriedData() {
      return this.filteredIssues.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },

    total() {
      return this.filteredIssues.length;
    },

    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },

    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },

    // Stats computed from filtered issues
    statsFromFiltered() {
      const filtered = this.filteredIssues;
      const totalIssues = filtered.length;
      const resolvedIssues = filtered.filter(
        (issue) => issue.status === "Resolved"
      ).length;
      const newIssues = filtered.filter(
        (issue) => issue.status === "New"
      ).length;
      const slaComplianceRate = totalIssues
        ? ((resolvedIssues / totalIssues) * 100).toFixed(2)
        : "0.00";

      return [
        {
          title: "Total Issues",
          value: totalIssues,
          icon: "fas fa-tasks",
          color: "primary",
        },
        {
          title: "Resolved Issues",
          value: resolvedIssues,
          icon: "fas fa-check-circle",
          color: "success",
        },
        {
          title: "New Issues",
          value: newIssues,
          icon: "fas fa-exclamation-circle",
          color: "warning",
        },
        {
          title: "Resolution %",
          value: `${slaComplianceRate}%`,
          icon: "fas fa-clock",
          color: "info",
        },
      ];
    },

    // Chart data from filtered issues
    chartData() {
      const filtered = this.filteredIssues;

      // Group by date for trends
      const dateGroups = {};
      filtered.forEach((issue) => {
        const date = moment(issue.reportedAt).format("YYYY-MM-DD");
        dateGroups[date] = (dateGroups[date] || 0) + 1;
      });

      // Group by category
      const categoryGroups = {};
      filtered.forEach((issue) => {
        categoryGroups[issue.category] =
          (categoryGroups[issue.category] || 0) + 1;
      });

      return {
        trends: dateGroups,
        categories: categoryGroups,
      };
    },

    recentFilteredIssues() {
      // Create a new array, sort by reportedAt in descending order and take only 5 items
      return [...this.filteredIssues]
        .sort((a, b) => new Date(b.reportedAt) - new Date(a.reportedAt))
        .slice(0, 5);
    },
  },
  methods: {
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },
    getDepartment(issueId) {
      let department = this.departments?.find((a) => a.departmentId == issueId);
      return `${department?.name}`;
    },
    computeStats(issues) {
      const totalIssues = issues.length;
      const resolvedIssues = issues.filter(
        (issue) => issue.status === "Resolved"
      ).length;
      const newIssues = issues.filter((issue) => issue.status == "New").length;
      const slaComplianceRate = ((resolvedIssues / totalIssues) * 100).toFixed(
        2
      );

      this.statsList = [
        {
          title: "Total Issues",
          value: totalIssues,
          icon: "fas fa-tasks",
          color: "primary",
        },
        {
          title: "Resolved Issues",
          value: resolvedIssues,
          icon: "fas fa-check-circle",
          color: "success",
        },
        {
          title: "New Issues",
          value: newIssues,
          icon: "fas fa-exclamation-circle",
          color: "warning",
        },
        {
          title: "Resolution %",
          value: `${slaComplianceRate}%`,
          icon: "fas fa-clock",
          color: "info",
        },
      ];
    },

    getDepartmentIdByName(departmentName) {
      const department = this.departments.find(
        (d) => d.name === departmentName
      );
      return department ? department.departmentId : null; // Return null if not found
    },

    async fetchDashboardData() {
      try {
        this.loading = true;
        const response = await this.issuesService.getAll();
        this.issues = response || [];

        // First update the lists
        this.updateOrganizationsList();

        // Initialize crossfilter with filtered data
        if (this.ndx) {
          this.ndx.remove();
        }
        this.ndx = crossfilter.default(this.filteredIssues);

        // Compute stats from filtered data
        this.computeStats(this.filteredIssues);

        // Render charts with filtered data
        this.renderCharts();
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        this.$toast.error("Failed to load dashboard data");
      } finally {
        this.loading = false;
      }
    },

    getDateForOneYearFromToday() {
      const today = new Date(); // Get today's date
      const oneYearFromToday = new Date(today); // Create a copy of today's date
      oneYearFromToday.setFullYear(today.getFullYear() - 1); // Add one year to the copied date
      return oneYearFromToday; // Return the updated date
    },

    renderCharts() {
      if (!this.ndx || !this.issues.length) return; // Don't render if no data

      // Clear existing charts
      d3.select("#trends-chart").html("");
      d3.select("#category-chart").html("");

      // Define your theme colors
      const themeColors = {
        primary: "#302e77",
        secondary: "#87CB16",
        success: "#28A745",
        warning: "#FFA534",
        danger: "#DC3545",
        info: "#17A2B8",
      };

      const colorScale = d3.scaleOrdinal().range(Object.values(themeColors));

      // Trends Chart
      const dateFormat = d3.timeFormat("%Y-%m");
      const dateDim = this.ndx.dimension((d) =>
        dateFormat(new Date(d.reportedAt))
      );
      const dateGroup = dateDim.group().reduceCount();

      const yearMonths = dateGroup.all().map((d) => d.key);
      const xScale = d3.scaleBand().domain(yearMonths).padding(0.1);

      this.trendsChart = dc.lineChart("#trends-chart");
      this.trendsChart
        .width(null) // Let it be responsive
        .height(300)
        .dimension(dateDim)
        .group(dateGroup)
        .x(xScale)
        .elasticY(true)
        .brushOn(false)
        .colors(colorScale)
        .renderArea(true) // Add area under the line
        .renderTitle(true)
        .title((d) => `${d.key}: ${d.value} issues`)
        .margins({ top: 20, right: 20, bottom: 40, left: 40 });

      this.trendsChart.xAxis().tickFormat((d) => d);

      // Category Chart
      const statusDim = this.ndx.dimension((d) => d.status || "Unknown");
      const statusGroup = statusDim.group();

      this.categoryChart = dc.pieChart("#category-chart");
      this.categoryChart
        .width(null) // Let it be responsive
        .height(300)
        .dimension(statusDim)
        .group(statusGroup)
        .colors(colorScale)
        //.legend(dc.legend())
        .renderLabel(true)
        .renderTitle(true)
        .title((d) => `${d.key}: ${d.value} issues`);

      dc.renderAll(); // Render all charts

      // Redraw charts on window resize
      window.addEventListener("resize", this.redrawCharts);
    },

    redrawCharts() {
      if (this.trendsChart) {
        this.trendsChart.width(window.innerWidth * 0.8).redraw();
      }
      if (this.categoryChart) {
        this.categoryChart.width(window.innerWidth * 0.3).redraw();
      }
    },

    getStatusBadgeClass(status) {
      const classes = {
        New: "badge bg-info",
        Resolved: "badge bg-success",
      };
      return classes[status] || "badge bg-secondary";
    },

    formatDate(date) {
      return moment(date).format("DD-MM-YYYY");
    },

    startAutoRefresh() {
      this.refreshInterval = setInterval(this.fetchDashboardData, 300000); // 5 minutes
    },

    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
      }
    },

    async refreshIssues() {
      await this.fetchDashboardData();
      this.$toast.success("Dashboard data refreshed");
    },

    openReportIssue() {
      this.$router.push("./internal/issues");
    },

    openMyIssues() {
      this.$router.push("./internal/issues/my-issues");
    },

    viewIssue(id) {
      this.$router.push(`/call-center/issue-view/${id}`);
    },

    editIssue(id) {
      this.$router.push(`/issues/${id}/edit`);
    },

    viewAllIssues() {
      this.$router.push("/issues");
    },

    resetFilters() {
      this.filters.department=null;
      this.filters.status = "";
      this.filters.district = "";
      this.filters.organization = "";
      this.filters.dateRange = [];
      this.fetchDashboardData();
    },

    updateOrganizationsList() {
      // Extract unique organizations from issues
      this.organizations = [
        ...new Set(this.issues.map((issue) => issue.organization)),
      ];
    },

    updateDashboards() {
      // Update stats from filtered data
      const totalIssues = this.filteredIssues.length;
      const resolvedIssues = this.filteredIssues.filter(
        (issue) => issue.status === "Resolved"
      ).length;
      const newIssues = this.filteredIssues.filter(
        (issue) => issue.status === "New"
      ).length;
      const slaComplianceRate = ((resolvedIssues / totalIssues) * 100).toFixed(
        2
      );

      this.statsList = [
        {
          title: "Total Issues",
          value: totalIssues,
          icon: "fas fa-tasks",
          color: "primary",
        },
        {
          title: "Resolved Issues",
          value: resolvedIssues,
          icon: "fas fa-check-circle",
          color: "success",
        },
        {
          title: "New Issues",
          value: newIssues,
          icon: "fas fa-exclamation-circle",
          color: "warning",
        },
        {
          title: "Resolution %",
          value: `${slaComplianceRate}%`,
          icon: "fas fa-clock",
          color: "info",
        },
      ];

      // Update charts
      this.updateCharts();
    },

    updateCharts() {
      if (this.ndx) {
        this.ndx.remove(); // Clean up existing crossfilter
      }
      this.ndx = crossfilter.default(this.filteredIssues);
      this.renderCharts(); // Re-render charts with filtered data
    },
  },

  watch: {
    trendsPeriod: {
      handler: "fetchDashboardData",
      immediate: false,
    },
    // Watch filters for changes
    filters: {
      deep: true,
      handler() {
        this.updateDashboards();
      },
    },
  },
};
</script>
<style scoped>
#trends-chart,
#category-chart {
  width: 100%;
  height: 300px;
}

/* Ensure the chart resizes when the window is resized */
.dc-chart {
  width: 100% !important;
  height: auto !important;
}

.card-tools {
  position: absolute;
  right: 1rem;
  top: 0.75rem;
}

.table td {
  vertical-align: middle;
}

.badge {
  font-size: 0.8rem;
  padding: 0.35em 0.65em;
}
</style>