<template>
  <div class="row">
    <div class="col-md-12">
      <h4 class="title">Custom table with pagination</h4>
      <p class="category">
        We combine
        <a
          href="http://element.eleme.io/#/en-US/component/quickstart"
          target="_blank"
          rel="noopener"
          >Element-UI</a
        >
        table functionalities together with a custom pagination component which
        should provide a very good starting point to integrate tables in your
        application. Check out more functionalities at
        <a
          href="http://element.eleme.io/#/en-US/component/table"
          target="_blank"
          >Element-UI table documentation</a
        >.
      </p>
    </div>

    <div class="col-12">
      <card title="Paginated tables">
        <div>
          <div
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <el-select
              class="select-default mb-3"
              style="width: 200px"
              v-model="pagination.perPage"
              placeholder="Per page"
            >
              <el-option
                class="select-default"
                v-for="item in pagination.perPageOptions"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
            <el-input
              type="search"
              class="mb-3"
              style="width: 200px"
              placeholder="Search records"
              v-model="searchQuery"
              aria-controls="datatables"
            />
          </div>
          <div class="col-sm-12">
            <el-table stripe style="width: 100%" :data="queriedData" border>
              <el-table-column
                v-for="column in tableColumns"
                :key="column.label"
                :min-width="column.minWidth"
                :prop="column.prop"
                :label="column.label"
              >
              </el-table-column>
              <el-table-column :min-width="120" fixed="right" label="Actions">
                <template slot-scope="props">
                  <a
                    v-tooltip.top-center="'Like'"
                    class="btn-info btn-simple btn-link"
                    @click="handleLike(props.$index, props.row)"
                  >
                    <i class="fa fa-heart"></i
                  ></a>
                  <a
                    v-tooltip.top-center="'Edit'"
                    class="btn-warning btn-simple btn-link"
                    @click="handleEdit(props.$index, props.row)"
                    ><i class="fa fa-edit"></i
                  ></a>
                  <a
                    v-tooltip.top-center="'Delete'"
                    class="btn-danger btn-simple btn-link"
                    @click="handleDelete(props.$index, props.row)"
                    ><i class="fa fa-times"></i
                  ></a>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div
          slot="footer"
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <div class="">
            <p class="card-category">
              Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
            </p>
          </div>
          <l-pagination
            class="pagination-no-border"
            v-model="pagination.currentPage"
            :per-page="pagination.perPage"
            :total="pagination.total"
          >
          </l-pagination>
        </div>
      </card>
    </div>
  </div>
</template>
<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import { Pagination as LPagination } from "src/components/index";
import users from "./users";
import Fuse from "fuse.js";

export default {
  components: {
    LPagination,
    [Select.name]: Select,
    [Option.name]: Option,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
  },
  data() {
    return {
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 25, 50],
        total: 0,
      },
      searchQuery: "",
      propsToSearch: ["name", "email", "age"],
      tableColumns: [
        {
          prop: "name",
          label: "Name",
          minWidth: 200,
        },
        {
          prop: "email",
          label: "Email",
          minWidth: 250,
        },
        {
          prop: "age",
          label: "Age",
          minWidth: 100,
        },
        {
          prop: "salary",
          label: "Salary",
          minWidth: 120,
        },
      ],
      tableData: users,
      fuseSearch: null,
    };
  },
  computed: {
    pagedData() {
      return this.tableData.slice(this.from, this.to);
    },
    /***
     * Searches through table data and returns a paginated array.
     * Note that this should not be used for table with a lot of data as it might be slow!
     * Do the search and the pagination on the server and display the data retrieved from server instead.
     * @returns {computed.pagedData}
     */
    queriedData() {
      let result = this.tableData;
      if (this.searchQuery !== "") {
        result = this.fuseSearch.search(this.searchQuery);
        this.paginationTotal(result.length);
      }
      return result.slice(this.from, this.to);
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    total() {
      this.paginationTotal(this.tableData.length);
      return this.tableData.length;
    },
  },
  methods: {
    handleLike(index, row) {
      alert(`Your want to like ${row.name}`);
    },
    handleEdit(index, row) {
      alert(`Your want to edit ${row.name}`);
    },
    handleDelete(index, row) {
      let indexToDelete = this.tableData.findIndex(
        (tableRow) => tableRow.id === row.id
      );
      if (indexToDelete >= 0) {
        this.tableData.splice(indexToDelete, 1);
      }
    },
    paginationTotal(value) {
      this.pagination.total = value;
    },
    // Fetch departments for dropdown
    async fetchDepartments() {
      try {
        // Simulate API call
        // this.departments = await departmentService.getAll()
        this.departments = [
          { departmentId: 1, name: "Engineering" },
          { departmentId: 2, name: "HR" },
          { departmentId: 3, name: "Sales" },
        ];
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    },

    // Fetch users from API
    async fetchUsers() {
      try {
        // Simulate API call
        // this.users = await userService.getAll()
        this.users = [
          {
            userID: 1,
            name: "John Doe",
            email: "<EMAIL>",
            phone: "1234567890",
            role: "Engineer",
            departmentId: 1,
            isActive: true,
            createdAt: new Date(),
          },
        ];
      } catch (error) {
        console.error("Error fetching users:", error);
      }
    },

    // Get department name for display
    getDepartmentName(departmentId) {
      const dept = this.departments.find(
        (d) => d.departmentId === departmentId
      );
      return dept ? dept.name : "N/A";
    },

    // Submit user (add or update)
    submitUser() {
      if (this.isEditing) {
        this.updateUser();
      } else {
        this.addUser();
      }
    },

    // Add new user
    addUser() {
      try {
        const newUser = {
          ...this.currentUser,
          userID: this.users.length + 1,
          createdAt: new Date(),
        };

        // Simulate API call
        // await userService.create(newUser)
        this.users.push(newUser);

        // Reset form and hide
        this.resetForm();
        this.showUserForm = false;
      } catch (error) {
        console.error("Error adding user:", error);
      }
    },

    // Prepare user for editing
    editUser(user) {
      this.currentUser = { ...user };
      this.isEditing = true;
      this.showUserForm = true;
    },

    // Update existing user
    updateUser() {
      try {
        const index = this.users.findIndex(
          (u) => u.userID === this.currentUser.userID
        );

        if (index !== -1) {
          // Simulate API call
          // await userService.update(this.currentUser)
          this.users.splice(index, 1, this.currentUser);
          this.resetForm();
          this.showUserForm = false;
        }
      } catch (error) {
        console.error("Error updating user:", error);
      }
    },

    // Delete user
    deleteUser(userID) {
      try {
        // Simulate API call
        // await userService.delete(userID)
        this.users = this.users.filter((u) => u.userID !== userID);
      } catch (error) {
        console.error("Error deleting user:", error);
      }
    },

    // Reset form to initial state
    resetForm() {
      this.currentUser = {
        userID: null,
        name: "",
        email: "",
        phone: "",
        role: "",
        departmentId: null,
        isActive: true,
        createdAt: null,
      };
      this.isEditing = false;
    },

    // Close user form
    closeUserForm() {
      this.resetForm();
      this.showUserForm = false;
    },

    // Format date for display
    formatDate(date) {
      if (!date) return "N/A";
      return new Date(date).toLocaleDateString();
    },
  },
  mounted() {
    this.fuseSearch = new Fuse(this.tableData, { keys: ["name", "email"] });
  },
};
</script>
<style>
</style>
