import axios from 'axios';

class API {
  /**
   * Constructor for the Generic Service
   * @param {string} baseURL - Base URL of the API
   * @param {string} endpoint - Specific endpoint for the resource (e.g., 'issues', 'users')
   * @param {object} [config] - Optional Axios configuration
   */
  constructor(baseURL, endpoint, config = {}) {
    this.endpoint = endpoint;
    this.axiosInstance = axios.create({
      baseURL,
      ...config
    });

    // Request interceptor for adding common headers
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // You can add common headers here, like authentication tokens
        // config.headers['Authorization'] = `Bearer ${token}`;
        return config;
      },
      (error) => Promise.reject(error)
    );
  }

  /**
   * Get all items
   * @param {object} [params] - Optional query parameters
   * @returns {Promise<Array>} - Array of items
   */
  async getAll(params) {
    try {
      const response = await this.axiosInstance.get(this.endpoint, { params });
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Get item by ID
   * @param {number|string} id - Identifier of the item
   * @param {object} [params] - Optional query parameters
   * @returns {Promise<Object>} - Single item
   */
  async getById(id, params) {
    try {
      const response = await this.axiosInstance.get(`${this.endpoint}/${id}`, { params });
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Create a new item
   * @param {Object} data - Item to create
   * @returns {Promise<Object>} - Created item
   */
  async create(data) {
    try {
      const response = await this.axiosInstance.post(this.endpoint, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Update an existing item
   * @param {number|string} id - Identifier of the item to update
   * @param {Object} data - Updated item data
   * @returns {Promise<Object>} - Updated item
   */
  async update(id, data) {
    try {
      const response = await this.axiosInstance.put(`${this.endpoint}/${id}`, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Partially update an existing item
   * @param {number|string} id - Identifier of the item to patch
   * @param {Object} data - Partial data to update
   * @returns {Promise<Object>} - Patched item
   */
  async patch(id, data) {
    try {
      const response = await this.axiosInstance.patch(`${this.endpoint}/${id}`, data);
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Delete an item
   * @param {number|string} id - Identifier of the item to delete
   * @returns {Promise<void>}
   */
  async delete(id) {
    try {
      await this.axiosInstance.delete(`${this.endpoint}/${id}`);
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Custom method for additional API calls
   * @param {string} method - HTTP method
   * @param {string} url - Endpoint URL
   * @param {Object} [data] - Optional request data
   * @param {Object} [config] - Optional Axios config
   * @returns {Promise<*>} - Response data
   */
  async customRequest(method, url, data, config) {
    try {
      const response = await this.axiosInstance.request({
        method,
        url: `${this.endpoint}/${url}`,
        data,
        ...config
      });
      return response.data;
    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  /**
   * Error handling method
   * @param {*} error - Axios error
   */
  handleError(error) {
    if (axios.isAxiosError(error)) {
      if (error.response) {
        // Server responded with an error status
        // console.error('Error response:', {
        //   data: error.response.data,
        //   status: error.response.status,
        //   headers: error.response.headers
        // });
      } else if (error.request) {
        // Request made but no response received
        //console.error('No response received:', error.request);
      } else {
        // Error in setting up the request
        //console.error('Request setup error:', error.message);
      }
    } else {
      // Generic error
      //console.error('Unexpected error:', error);
    }
  }
}

// Usage Examples:
// Create a service instance
// const userService = new GenericService(
//   'https://api.example.com',  // Base URL
//   'users'                     // Endpoint
// );

// // Get all users
// userService.getAll()
//   .then(users => console.log(users))
//   .catch(error => console.error(error));

// // Create a new user
// const newUser = {
//   name: 'John Doe',
//   email: '<EMAIL>'
// };
// userService.create(newUser)
//   .then(createdUser => console.log(createdUser))
//   .catch(error => console.error(error));

export default API