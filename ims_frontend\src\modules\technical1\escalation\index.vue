<template>
  <div class="escalation-management">
    <!-- Escalation Form -->
    <card v-if="showEscalationForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">{{ isEditing ? "Edit Escalation Level" : "Add Escalation Level" }}</h4>
      </template>
      <form @submit.prevent="submitEscalation">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Service">
              <el-select
                v-model="currentEscalation.serviceID"
                placeholder="Select service"
                filterable
                class="w-100"
              >
                <el-option
                  v-for="service in services"
                  :key="service.i_service_type"
                  :label="service.name"
                  :value="service.i_service_type"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Level">
              <el-select
                v-model="currentEscalation.level"
                placeholder="Select level"
                class="w-100"
              >
                <el-option
                  v-for="level in availableLevels"
                  :key="level.value"
                  :label="level.label"
                  :value="level.value"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input
              label="Time Threshold (Hours)"
              type="number"
              v-model="currentEscalation.timeThresholdHours"
              :required="true"
              placeholder="Enter time threshold"
            />
          </div>
          <div class="col-md-6">
            <fg-input
              label="Description"
              type="text"
              v-model="currentEscalation.description"
              :required="true"
              placeholder="Enter description"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Escalation Level" : "Add Escalation Level" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeEscalationForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Escalation List -->
    <card>
      <template slot="header">
        <div class="d-flex justify-content-between align-items-center">
          <h4 class="card-title">Escalation Levels</h4>
          <div>
            <button class="btn btn-sm btn-success" @click="showAddEscalationForm">
              <i class="fa fa-plus"></i> Add Escalation Level
            </button>
          </div>
        </div>
        <p class="card-category">Manage escalation levels for services</p>
        <div class="row mt-3">
          <div class="col-md-4">
            <fg-input placeholder="Search..." v-model="searchQuery" />
          </div>
        </div>
      </template>

      <div class="row">
        <div class="col-sm-12">
          <div v-if="loading" class="loader-container">
            <div class="loader"></div>
            <p>Loading data...</p>
          </div>
          <el-table
            v-else-if="escalations.length > 0"
            :data="queriedData"
            stripe
            border
            style="width: 100%"
            :default-sort="{prop: 'level', order: 'ascending'}"
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
              sortable
            ></el-table-column>

            <!-- Actions Column -->
            <el-table-column label="Actions" width="180" fixed="right">
              <template v-slot="{ row }">
                <button class="btn btn-info btn-sm mr-2" @click="editEscalation(row)" title="Edit">
                  <i class="fa fa-pencil"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm"
                  @click="deleteEscalation(row.id)"
                  title="Delete"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="text-center py-5">
            <p>No escalation levels found. Click "Add Escalation Level" to create one.</p>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <div class="d-flex align-items-center">
          <p class="card-category mb-0 mr-3">
            Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
          </p>
          <div class="d-flex align-items-center">
            <span class="mr-2">Show</span>
            <select
              v-model="pagination.perPage"
              class="form-control form-control-sm"
              style="width: auto;"
              @change="pagination.currentPage = 1"
            >
              <option v-for="option in pagination.perPageOptions" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
            <span class="ml-2">entries</span>
          </div>
          <button
            v-if="searchQuery || pagination.currentPage > 1"
            class="btn btn-sm btn-primary ml-3"
            @click="resetFiltersAndPagination"
          >
            <i class="fa fa-refresh mr-1"></i> Reset Filters
          </button>
        </div>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import API from "src/services/api";
import { Table, TableColumn, Select, Option } from "element-ui";
import LPagination from "src/components/Pagination.vue";

export default {
  components: {
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-select": Select,
    "el-option": Option,
    "l-pagination": LPagination,
  },
  data() {
    return {
      loading: true,
      escalationService: new API(
        process.env ? process.env.VUE_APP_API_URL : "https://localhost:7179/api",
        "escalationlevels"
      ),
      servsService: new API(
        process.env ? process.env.VUE_APP_API_URL : "https://localhost:7179/api",
        "services"
      ),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      tableColumns: [
        {
          prop: "serviceID",
          label: "Service",
          formatter: (row) => this.getServiceById(row.serviceID),
          minWidth: 120,
          sortMethod: (a, b) => {
            const serviceA = this.getServiceById(a);
            const serviceB = this.getServiceById(b);
            return serviceA.localeCompare(serviceB);
          }
        },
        { prop: "level", label: "Level", formatter: (row) => this.getLevelLabel(row.level), minWidth: 120 },
        { prop: "timeThresholdHours", label: "Time Threshold (Hours)", minWidth: 160 },
        { prop: "description", label: "Description", minWidth: 200 },
      ],
      escalationLevels: [
        { label: "Level 1", value: 1 },
        { label: "Level 2", value: 2 },
        { label: "Level 3", value: 3 },
        { label: "Level 4", value: 4 },
        { label: "Level 5", value: 5 },
        { label: "Level 6", value: 6 },
        { label: "Level 7", value: 7 },
        { label: "Level 8", value: 8 },
        { label: "Level 9", value: 9 },
        { label: "Level 10", value: 10 }
      ],
      escalations: [],
      services: [],
      currentEscalation: {
        id: null,
        serviceID: "",
        level: 1,
        timeThresholdHours: 0,
        description: "",
        escalationLevel: 1,
      },
      showEscalationForm: false,
      isEditing: false,
    };
  },
  computed: {
    // Filter out levels that are already used for the selected service
    availableLevels() {
      // If no service is selected or we're editing an existing escalation, return all levels
      if (!this.currentEscalation.serviceID || (this.isEditing && this.currentEscalation.id)) {
        return this.escalationLevels;
      }

      // Get all levels already used for the selected service
      const usedLevels = this.escalations
        .filter(e => e.serviceID === this.currentEscalation.serviceID)
        .map(e => e.level);

      // Return only levels that are not already used for this service
      return this.escalationLevels.filter(level => !usedLevels.includes(level.value));
    },

    queriedData() {
      if (!this.searchQuery) {
        return this.paginatedData;
      }
      const searchQuery = this.searchQuery.toLowerCase();
      const filteredData = this.escalations.filter((item) => {
        return (
          this.getServiceById(item.serviceID).toLowerCase().includes(searchQuery) ||
          this.getLevelLabel(item.level).toLowerCase().includes(searchQuery) ||
          item.description.toLowerCase().includes(searchQuery)
        );
      });
      return this.paginate(filteredData);
    },
    paginatedData() {
      return this.paginate(this.escalations);
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    to() {
      return Math.min(
        this.from + this.pagination.perPage,
        this.pagination.total
      );
    },
    total() {
      return this.pagination.total;
    },
  },
  methods: {
    async fetchEscalations() {
      try {
        this.loading = true;
        const response = await this.escalationService.getAll();
        this.escalations = response;
        this.pagination.total = response.length;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch escalation levels");
        console.error("Error fetching escalation levels:", error);
      } finally {
        this.loading = false;
      }
    },

    async fetchServices() {
      try {
        const response = await this.servsService.getAll();
        // Make sure we're accessing the services array correctly
        this.services = response.services || [];
        if (!Array.isArray(this.services)) {
          console.error("Services data is not an array:", this.services);
          this.services = [];
        }
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch services");
        console.error("Error fetching services:", error);
        this.services = [];
      }
    },

    getServiceById(serviceId) {
      if (!serviceId) return "Unknown Service";

      // Make sure services is an array before using find
      if (!Array.isArray(this.services) || this.services.length === 0) {
        console.warn("Services is not available when calling getServiceById");
        // Return a more user-friendly label for the service ID
        return `Service ${serviceId}`;
      }

      // Try to find the service by ID
      const service = this.services.find((s) => s.i_service_type === serviceId);

      // If service is found, return its name, otherwise return a formatted label
      return service ? service.name : `Service ${serviceId}`;
    },

    getLevelLabel(levelValue) {
      if (levelValue === null || levelValue === undefined) return "Unknown Level";

      const level = this.escalationLevels.find(l => l.value === levelValue);
      return level ? level.label : `Level ${levelValue}`;
    },

    paginate(data) {
      const { currentPage, perPage } = this.pagination;
      const from = (currentPage - 1) * perPage;
      const to = currentPage * perPage;
      return data.slice(from, to);
    },

    showAddEscalationForm() {
      this.isEditing = false;
      this.showEscalationForm = true;

      // Default values
      let serviceID = "";
      let initialLevel = 1;

      // If a service ID was provided in the route query, pre-select it
      const serviceId = this.$route.query.serviceId;
      if (serviceId) {
        // Convert serviceId to the correct type (number) if needed
        const numericServiceId = parseInt(serviceId);
        serviceID = !isNaN(numericServiceId) ? numericServiceId : serviceId;

        // Get all levels already used for the selected service
        const usedLevels = this.escalations
          .filter(e => e.serviceID === serviceID)
          .map(e => e.level);

        // Find the first available level (not already used)
        for (let i = 1; i <= 10; i++) {
          if (!usedLevels.includes(i)) {
            initialLevel = i;
            break;
          }
        }
      }

      this.currentEscalation = {
        id: null,
        serviceID: serviceID,
        level: initialLevel,
        timeThresholdHours: 0,
        description: "",
        escalationLevel: initialLevel,
      };
    },

    editEscalation(escalation) {
      this.isEditing = true;
      this.showEscalationForm = true;

      // Create a copy of the escalation data
      const escalationCopy = { ...escalation };

      // Make sure the level and escalationLevel fields are set as integers
      escalationCopy.level = parseInt(escalationCopy.level);

      if (!escalationCopy.escalationLevel) {
        escalationCopy.escalationLevel = escalationCopy.level;
      } else {
        escalationCopy.escalationLevel = parseInt(escalationCopy.escalationLevel);
      }

      // Ensure timeThresholdHours is a number
      escalationCopy.timeThresholdHours = parseFloat(escalationCopy.timeThresholdHours);

      this.currentEscalation = escalationCopy;
    },

    async submitEscalation() {
      if (this.isEditing) {
        await this.updateEscalation();
      } else {
        await this.addEscalation();
      }
    },

    async addEscalation() {
      try {
        // Make sure the level and escalationLevel are in sync and are integers
        this.currentEscalation.level = parseInt(this.currentEscalation.level);
        this.currentEscalation.escalationLevel = this.currentEscalation.level;

        // Create a copy of the data to send to the API
        const escalationData = { ...this.currentEscalation };

        // Ensure all numeric fields are properly converted to numbers
        escalationData.level = parseInt(escalationData.level);
        escalationData.escalationLevel = parseInt(escalationData.escalationLevel);
        escalationData.timeThresholdHours = parseFloat(escalationData.timeThresholdHours);

        await this.escalationService.create(escalationData);
        this.$alert.success("Escalation level added successfully");
        this.closeEscalationForm();
        await this.fetchEscalations();
      } catch (error) {
        console.error("Error adding escalation level:", error);
        this.$alert.error(
          error.response?.data?.message || "Failed to add escalation level"
        );
      }
    },

    async updateEscalation() {
      try {
        // Make sure the level and escalationLevel are in sync and are integers
        this.currentEscalation.level = parseInt(this.currentEscalation.level);
        this.currentEscalation.escalationLevel = this.currentEscalation.level;

        // Create a copy of the data to send to the API
        const escalationData = { ...this.currentEscalation };

        // Ensure all numeric fields are properly converted to numbers
        escalationData.level = parseInt(escalationData.level);
        escalationData.escalationLevel = parseInt(escalationData.escalationLevel);
        escalationData.timeThresholdHours = parseFloat(escalationData.timeThresholdHours);
        escalationData.id = parseInt(escalationData.id);

        await this.escalationService.update(
          escalationData.id,
          escalationData
        );
        this.$alert.success("Escalation level updated successfully");
        this.closeEscalationForm();
        await this.fetchEscalations();
      } catch (error) {
        console.error("Error updating escalation level:", error);
        this.$alert.error(
          error.response?.data?.message || "Failed to update escalation level"
        );
      }
    },

    async deleteEscalation(id) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this escalation level?"
        );
        if (confirmDelete.isConfirmed) {
          await this.escalationService.delete(id);
          await this.fetchEscalations();
          this.$alert.success("Escalation level deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete escalation level"
        );
      }
    },

    closeEscalationForm() {
      this.isEditing = false;
      this.showEscalationForm = false;

      // Default values
      let serviceID = "";
      let initialLevel = 1;

      // If a service ID was provided in the route query, pre-select it
      const serviceId = this.$route.query.serviceId;
      if (serviceId) {
        // Convert serviceId to the correct type (number) if needed
        const numericServiceId = parseInt(serviceId);
        serviceID = !isNaN(numericServiceId) ? numericServiceId : serviceId;

        // Get all levels already used for the selected service
        const usedLevels = this.escalations
          .filter(e => e.serviceID === serviceID)
          .map(e => e.level);

        // Find the first available level (not already used)
        for (let i = 1; i <= 10; i++) {
          if (!usedLevels.includes(i)) {
            initialLevel = i;
            break;
          }
        }
      }

      this.currentEscalation = {
        id: null,
        serviceID: serviceID,
        level: initialLevel,
        timeThresholdHours: 0,
        description: "",
        escalationLevel: initialLevel,
      };
    },

    resetFiltersAndPagination() {
      // Clear any search filters
      this.searchQuery = "";

      // Reset pagination to first page
      this.pagination.currentPage = 1;

      // Force a refresh of the data
      this.fetchEscalations();
    },
  },
  async mounted() {
    try {
      // First load services to ensure we have service names available
      await this.fetchServices();
      await this.fetchEscalations();

      // Check if serviceId is provided in the query parameters
      const serviceId = this.$route.query.serviceId;
      if (serviceId) {
        // Convert serviceId to the correct type (number) if needed
        const numericServiceId = parseInt(serviceId);
        const validServiceId = !isNaN(numericServiceId) ? numericServiceId : serviceId;

        // Pre-select the service when adding a new escalation
        this.currentEscalation.serviceID = validServiceId;

        // Get the service name for display
        const serviceName = this.getServiceById(validServiceId);

        // Filter escalations by service ID
        this.searchQuery = serviceName;

        // Update the page title to include the service name
        document.title = `Escalation Levels - ${serviceName}`;

        // Show add form if no escalations exist for this service
        const serviceEscalations = this.escalations.filter(e => e.serviceID === validServiceId);
        if (serviceEscalations.length === 0) {
          this.showAddEscalationForm();
        }
      }
    } catch (error) {
      console.error("Error in mounted hook:", error);
      this.loading = false;
    }
  },
};
</script>

<style>
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}
.loader {
  border: 5px solid #f3f3f3;
  border-radius: 50%;
  border-top: 5px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
