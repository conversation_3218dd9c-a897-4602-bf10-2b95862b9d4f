<template>
  <div class="transfer-issue">
    <div class="container-fluid">
      <!-- Back Button -->
      <button class="btn btn-primary mb-4" @click="goBack">
        <i class="fas fa-arrow-left"></i> Back
      </button>

      <card>
        <template slot="header">
          <h4 class="card-title">Transfer Issue #{{ issue?.issueRef }}</h4>
        </template>

        <!-- Issue Details Summary -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="issue-summary p-3 bg-light rounded">
              <h5>{{ issue?.title }}</h5>
              <p><strong>Status:</strong> {{ issue?.status }}</p>
              <p><strong>Priority:</strong> {{ issue?.priority }}</p>
              <p><strong>Current Department:</strong> {{ currentDepartmentName }}</p>
            </div>
          </div>
        </div>

        <!-- Transfer Form -->
        <form @submit.prevent="handleTransfer">
          <div class="row">
            <div class="col-md-6">
              <fg-input label="Transfer To Department">
                <el-select 
                  v-model="transferForm.departmentId" 
                  placeholder="Select department"
                  @change="loadTechnicians"
                  class="w-100"
                >
                  <el-option
                    v-for="dept in availableDepartments"
                    :key="dept.departmentId"
                    :label="dept.name"
                    :value="dept.departmentId"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row" v-if="transferForm.departmentId">
            <div class="col-md-6">
              <fg-input label="Assign To Technician">
                <el-select 
                  v-model="transferForm.technicianId" 
                  placeholder="Select technician (optional)"
                  :loading="loadingTechnicians"
                  class="w-100"
                >
                  <el-option
                    v-for="tech in departmentTechnicians"
                    :key="tech.id"
                    :label="tech.fullName"
                    :value="tech.id"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <fg-input label="Transfer Reason">
                <el-input
                  v-model="transferForm.reason"
                  type="textarea"
                  :rows="4"
                  placeholder="Enter reason for transfer"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row mt-4">
            <div class="col-md-12">
              <button 
                type="submit" 
                class="btn btn-info"
                :disabled="isTransferring || !transferForm.departmentId || !transferForm.reason"
              >
                <i class="fas fa-exchange-alt mr-1"></i>
                <span v-if="!isTransferring">Transfer Issue</span>
                <span v-else>
                  <i class="fas fa-spinner fa-spin mr-1"></i>
                  Transferring...
                </span>
              </button>
            </div>
          </div>
        </form>

        <!-- Transfer History -->
        <div class="row mt-5">
          <div class="col-md-12">
            <h5>Transfer History</h5>
            <el-table
              :data="transferHistory"
              style="width: 100%"
              v-if="transferHistory.length > 0"
            >
              <el-table-column
                prop="timestamp"
                label="Date"
                :formatter="formatDateTime"
                width="180"
              />
              <el-table-column
                prop="fromDepartment"
                label="From Department"
                width="200"
              />
              <el-table-column
                prop="toDepartment"
                label="To Department"
                width="200"
              />
              <el-table-column
                prop="reason"
                label="Reason"
              />
              <el-table-column
                prop="transferredBy"
                label="Transferred By"
                width="200"
              />
            </el-table>
            <div v-else class="text-center py-3">
              No transfer history available
            </div>
          </div>
        </div>
      </card>
    </div>
  </div>
</template>

<script>
import API from "@/services/api";
import { FormGroupInput as FgInput } from "src/components/index";
import {
  Select,
  Option,
  Table,
  TableColumn,
  Input
} from "element-ui";

export default {
  name: "TransferIssue",
  components: {
    FgInput,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input
  },
  data() {
    return {
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      userService: new API(process.env.VUE_APP_API_URL, "users"),
      transferService: new API(process.env.VUE_APP_API_URL, "transfers"),
      issue: null,
      departments: [],
      loadingTechnicians: false,
      departmentTechnicians: [],
      isTransferring: false,
      transferForm: {
        departmentId: null,
        technicianId: null,
        reason: ""
      },
      transferHistory: []
    };
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    currentDepartmentName() {
      const dept = this.departments.find(d => d.departmentId === this.issue?.assignedDepartmentId);
      return dept?.name || "Unassigned";
    },
    availableDepartments() {
      // Filter out current department and sort alphabetically
      return this.departments
        .filter(dept => dept.departmentId !== this.issue?.assignedDepartmentId)
        .sort((a, b) => a.name.localeCompare(b.name));
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    async loadTechnicians() {
      if (!this.transferForm.departmentId) return;
      
      this.loadingTechnicians = true;
      try {
        const response = await this.userService.getAll({
          departmentId: this.transferForm.departmentId,
          role: 'technical',
          status: 'active'
        });
        this.departmentTechnicians = response;
      } catch (error) {
        this.$alert.error('Failed to load technicians');
      } finally {
        this.loadingTechnicians = false;
      }
    },
    async handleTransfer() {
      if (!this.transferForm.departmentId || !this.transferForm.reason) {
        this.$alert.error('Please fill in all required fields');
        return;
      }

      this.isTransferring = true;
      try {
        // 1. Update issue department assignment
        const updatedIssue = await this.issueService.update(this.issue.issueId, {
          ...this.issue,
          assignedDepartmentId: this.transferForm.departmentId,
          assignedTechnicianId: this.transferForm.technicianId || null,
          assignedDate: new Date(),
          status: 'In Progress'
        });

        // 2. Create transfer record
        await this.transferService.create({
          issueId: this.issue.issueId,
          fromDepartmentId: this.issue.assignedDepartmentId,
          toDepartmentId: this.transferForm.departmentId,
          technicianId: this.transferForm.technicianId,
          reason: this.transferForm.reason,
          transferredBy: this.loggedInUser.id,
          transferredAt: new Date()
        });

        // 3. Create issue log
        await this.issueService.create({
          issueId: this.issue.issueId,
          actionTaken: 'Department Transfer',
          comment: `Transferred to ${this.getDepartmentName(this.transferForm.departmentId)}. Reason: ${this.transferForm.reason}`,
          status: 'In Progress',
          updatedBy: this.loggedInUser.id,
          updatedAt: new Date(),
          expose: false
        });

        this.$alert.success('Issue transferred successfully');
        this.$router.push('/technical/issues');
      } catch (error) {
        this.$alert.error(error.message || 'Failed to transfer issue');
      } finally {
        this.isTransferring = false;
      }
    },
    getDepartmentName(departmentId) {
      const dept = this.departments.find(d => d.departmentId === departmentId);
      return dept?.name || "Unknown Department";
    },
    formatDateTime(row, column, cellValue) {
      return new Date(cellValue).toLocaleString();
    },
    async loadTransferHistory() {
      try {
        const response = await this.transferService.getAll({
          issueId: this.$route.params.id
        });
        
        this.transferHistory = await Promise.all(response.map(async transfer => {
          const [fromDept, toDept, user] = await Promise.all([
            this.getDepartmentName(transfer.fromDepartmentId),
            this.getDepartmentName(transfer.toDepartmentId),
            this.userService.getById(transfer.transferredBy)
          ]);

          return {
            ...transfer,
            fromDepartment: fromDept,
            toDepartment: toDept,
            transferredBy: user.fullName,
            timestamp: transfer.transferredAt
          };
        }));
      } catch (error) {
        this.$alert.error('Failed to load transfer history');
      }
    }
  },
  async created() {
    try {
      const issueId = this.$route.params.id;
      if (!issueId) {
        this.$router.push('/technical/issues');
        return;
      }

      // Load initial data
      const [issue, departments] = await Promise.all([
        this.issueService.getById(issueId),
        this.departmentService.getAll()
      ]);

      this.issue = issue;
      this.departments = departments;

      // Load transfer history
      await this.loadTransferHistory();
    } catch (error) {
      this.$alert.error('Failed to load issue details');
      this.$router.push('/technical/issues');
    }
  }
};
</script>

<style scoped>
.transfer-issue {
  padding: 20px;
}

.issue-summary {
  border-left: 4px solid #17a2b8;
}

.issue-summary h5 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.issue-summary p {
  margin-bottom: 8px;
}

.issue-summary strong {
  color: #495057;
}
</style>