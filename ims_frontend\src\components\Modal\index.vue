<template>
    <div class="modal-overlay">
      <div class="modal-content">
        <button class="close-button" @click="$emit('close')">X</button>
        <h3>Logs for Issue #{{ issueId }}</h3>
        <ul>
          <li v-for="log in logs" :key="log.timestamp">
            <strong>{{ log.timestamp }}</strong>: {{ log.message }}
          </li>
        </ul>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: ['issueId'],
    data() {
      return {
        logs: [
          { timestamp: "2024-12-10 12:00", message: "Issue assigned to <PERSON> in Engineering" },
          { timestamp: "2024-12-11 10:30", message: "Issue being reviewed by team" },
          { timestamp: "2024-12-12 09:15", message: "In progress - awaiting feedback" },
        ],
      };
    },
  };
  </script>
  
  <style scoped>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 400px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 20px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
  </style>
  