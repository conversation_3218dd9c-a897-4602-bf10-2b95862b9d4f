<template>
  <div class="sidebar"
       :style="sidebarStyle"
       :data-color="backgroundColor"
       :data-active-color="activeColor">

    <div class="sidebar-wrapper" ref="sidebarScrollArea">
      <div class="logo">
        <a class="logo-mini" href="/">
          <div class="logo-img-container">
            <img :src="logo" alt="MTL Logo" class="mtl-logo">
          </div>
        </a>
        <div class="logo-content">
          <a href="/" class="company-name">
            MTL Issue
          </a>
          <a href="/" class="system-name">
            Management System
          </a>
        </div>
      </div>
      <slot></slot>
      <ul class="nav">
        <slot name="links">
          <sidebar-item v-for="(link, index) in sidebarLinks"
                        :key="link.name + index"
                        :link="link">
            <sidebar-item v-for="(subLink, index) in link.children"
                          :key="subLink.name + index"
                          :link="subLink">
            </sidebar-item>
          </sidebar-item>
        </slot>
      </ul>
    </div>
  </div>
</template>

<script>
import PerfectScrollbar from 'perfect-scrollbar';
import 'perfect-scrollbar/css/perfect-scrollbar.css';

function hasElement(className) {
  return document.getElementsByClassName(className).length > 0;
}

function initScrollbar(className) {
  if (hasElement(className)) {
    new PerfectScrollbar(`.${className}`);
  } else {
    setTimeout(() => {
      initScrollbar(className);
    }, 100);
  }
}

export default {
  name: 'sidebar',
  props: {
    title: {
      type: String,
      default: 'ISSUES MANAGEMENT SYSTEM'
    },
    backgroundColor: {
      type: String,
      default: 'blue',
      validator: (value) => {
        let acceptedValues = ['', 'blue', 'azure', 'green', 'orange', 'red', 'purple', 'black']
        return acceptedValues.indexOf(value) !== -1
      }
    },
    activeColor: {
      type: String,
      default: 'success',
      validator: (value) => {
        let acceptedValues = ['primary', 'info', 'success', 'warning', 'danger']
        return acceptedValues.indexOf(value) !== -1
      }
    },
    logo: {
      type: String,
      default: '/static/img/logo.png'
    },
    sidebarLinks: {
      type: Array,
      default: () => []
    },
    autoClose: {
      type: Boolean,
      default: true
    }
  },
  provide() {
    return {
      autoClose: this.autoClose
    }
  },
  computed: {
    sidebarStyle() {
      return {
        backgroundImage: `url(${this.backgroundImage})`
      }
    }
  },
  methods: {
    initScrollbar() {
      let docClasses = document.body.classList;
      let isWindows = navigator.platform.startsWith('Win');
      if (isWindows) {
        initScrollbar('sidebar-wrapper');
        docClasses.add('perfect-scrollbar-on');
      } else {
        docClasses.add('perfect-scrollbar-off');
      }
    }
  },
  mounted() {
    this.initScrollbar()
  }
}
</script>
<style scoped>
.logo {
  display: flex;
  align-items: center;
  height: 80px;
  margin-bottom: 10px;
  padding-left:10px;
}

.logo-mini {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.logo-img-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mtl-logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.logo-content {
  padding-left: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 80px;
  flex-grow: 1;
}

.company-name {
  color: white !important;
  font-size: 15px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  letter-spacing: 0.5px;
  text-decoration: none;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
  transition: color 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 5px;
}

.company-name:hover {
  color: #ffd54f !important;
}

.system-name {
  color: #ffffff !important;
  font-size: 10px;
  font-weight: 400;
  opacity: 0.9;
  margin: 3px 0 0;
  letter-spacing: 0.3px;
  text-decoration: none;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive adjustments */
@media (max-width: 1199px) {
  .company-name {
    font-size: 14px;
  }
  .system-name {
    font-size: 10px;
  }
}

@media (max-width: 991px) {
 
  .company-name {
    font-size: 14px;
  }
  .system-name {
    font-size: 10px;
  }
}

@media (max-width: 767px) {
 

  .company-name {
    font-size: 14px;
  }
  .system-name {
    font-size: 10px;
  }
}

/* Collapsed sidebar adjustments */
.sidebar-mini .logo-content {
  display: none;
}

.sidebar-mini .logo {
  justify-content: center;
}

.sidebar-mini .logo-mini {
  margin: 0;
  width: 200px;
  height: 100%;
}

.sidebar-mini .mtl-logo {
  height: 100%;
  width: auto;
}
</style>