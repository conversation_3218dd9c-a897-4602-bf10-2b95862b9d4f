<template>
  <div class="admin-management">
    <card>
      <template slot="header">
        <h4 class="card-title">Admin Management</h4>
      </template>

      <div class="row mb-4">
        <div class="col-md-6">
          <h5>Create New Admin</h5>
          <el-select
            v-model="selectedUser"
            placeholder="Select user to make admin"
            filterable
            class="w-100"
          >
            <el-option
              v-for="user in nonAdminUsers"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
          <button class="btn btn-fill btn-info mt-3" @click="makeAdmin" :disabled="!selectedUser">
            Make Admin
          </button>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <h5>Admin Assignment History</h5>
          <el-table :data="adminLogs" stripe style="width: 100%" v-loading="loading">
            <el-table-column prop="timestamp" label="Date/Time" :formatter="formatDateTime" />
            <el-table-column prop="details" label="Action" />
            <el-table-column prop="performedBy" label="Performed By" />
          </el-table>
        </div>
      </div>
    </card>
  </div>
</template>

<script>
import { Table, TableColumn, Select, Option } from "element-ui";
import API from "@/services/api";

export default {
  name: "AdminManagement",
  components: {
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-select": Select,
    "el-option": Option
  },
  data() {
    return {
      userService: new API(process.env.VUE_APP_API_URL, "users"),
      auditService: new API(process.env.VUE_APP_API_URL, "audit-logs"),
      users: [],
      adminLogs: [],
      selectedUser: null,
      loading: false
    };
  },
  computed: {
    nonAdminUsers() {
      return this.users.filter(user => !user.isAdmin);
    },
    currentUser() {
      return JSON.parse(sessionStorage.getItem("user"));
    }
  },
  methods: {
    async fetchUsers() {
      try {
        this.loading = true;
        const response = await this.userService.getAll();
        this.users = response;
      } catch (error) {
        this.$alert.error("Failed to fetch users");
      } finally {
        this.loading = false;
      }
    },
    async fetchAdminLogs() {
      try {
        this.loading = true;
        const response = await this.auditService.getAll({
          action: 'ADMIN_ROLE_ASSIGNED'
        });
        this.adminLogs = response;
      } catch (error) {
        this.$alert.error("Failed to fetch admin logs");
      } finally {
        this.loading = false;
      }
    },
    async makeAdmin() {
      if (!this.selectedUser) return;

      try {
        const user = this.users.find(u => u.id === this.selectedUser);
        user.isAdmin = true;
        
        await this.userService.update(user.id, user);
        
        // Log the admin creation
        await this.auditService.create({
          action: 'ADMIN_ROLE_ASSIGNED',
          userId: user.id,
          performedBy: this.currentUser.name,
          timestamp: new Date(),
          details: `${this.currentUser.name} made ${user.name} an Admin`
        });

        this.$alert.success(`${user.name} is now an Admin`);
        await this.fetchUsers();
        await this.fetchAdminLogs();
        this.selectedUser = null;
      } catch (error) {
        this.$alert.error("Failed to create admin user");
      }
    },
    formatDateTime(row, column, cellValue) {
      return new Date(cellValue).toLocaleString();
    }
  },
  async mounted() {
    await this.fetchUsers();
    await this.fetchAdminLogs();
  }
};
</script>