<template>
  <div class="department-management">
    <!-- Department Form -->
    <card v-if="showDepartmentForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{ isEditing ? "Edit Position" : "Add Position" }}
        </h4>
      </template>
      <form @submit.prevent="submitDepartment">
        <!-- Parent Position Selection -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="form-group">
              <label>Reports To</label>
              <el-select
                v-model="currentDepartment.reports_to"
                placeholder="Select Reporting Position"
                class="w-100"
                clearable
                filterable
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.departmentId"
                  :label="`${dept.position} - ${dept.name}`"
                  :value="dept.departmentId"
                />
              </el-select>
            </div>
          </div>
        </div>

        <!-- Position Details -->
        <div class="row">
          <div class="col-md-6">
            <fg-input
              label="Position Title"
              type="text"
              v-model="currentDepartment.position"
              :required="true"
              placeholder="Enter position title (e.g. Chief Executive Officer)"
            />
          </div>
          <div class="col-md-6">
            <fg-input
              label="Department/Unit name"
              type="text"
              v-model="currentDepartment.name"
              :required="true"
              placeholder="Enter department name (e.g. Executive Management)"
            />
          </div>
        </div>

        <!-- Location -->
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label>Location</label>
              <el-select
                v-model="currentDepartment.location"
                placeholder="Select Location"
                class="w-100"
              >
                <el-option
                  v-for="loc in locations"
                  :key="loc.id"
                  :label="loc.name"
                  :value="loc.name"
                />
              </el-select>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="row mt-4">
          <div class="col-md-12">
            <button
              type="submit"
              class="btn btn-fill btn-info"
              :disabled="loading || !isFormValid"
            >
              {{ isEditing ? "Update Position" : "Add Position" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeDepartmentForm"
            >
              Cancel
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Organization Chart -->
    <card class="mt-4" v-if="!showDepartmentForm">
      <template slot="header">
        <div class="d-flex justify-content-between align-items-center">
          <h4 class="card-title">Organization Structure</h4>
          <button class="btn btn-primary" @click="showAddForm">
            Add Position
          </button>
        </div>
      </template>
      <el-tree
        :data="organizationTree"
        :props="defaultProps"
        node-key="departmentId"
        :default-expanded-keys="expandedKeys"
        draggable
        @node-drop="handleDrop"
      >
        <span class="custom-tree-node" slot-scope="{ data }">
          <span class="position-title">{{ data.position }}</span>
          <span class="department-name ml-2 text-muted">({{ data.name }})</span>
          <span class="location-badge ml-2">{{ data.location }}</span>
          <div class="action-buttons ml-3">
            <el-button
              size="mini"
              type="primary"
              @click.stop="editDepartment(data)"
              icon="el-icon-edit"
            ></el-button>
            <el-button
              size="mini"
              type="danger"
              @click.stop="confirmDelete(data)"
              icon="el-icon-delete"
            ></el-button>
          </div>
        </span>
      </el-tree>
    </card>
  </div>
</template>

<script>
import { FormGroupInput as FgInput } from "src/components/index";
import { Select, Option, Tree, Button, MessageBox } from "element-ui";
import API from "../../../services/api";

export default {
  name: "DepartmentManagement",

  components: {
    FgInput,
    "el-select": Select,
    "el-option": Option,
    "el-tree": Tree,
    "el-button": Button,
  },

  data() {
    return {
      departService: new API(process.env.VUE_APP_API_URL, "departments"),
      loading: false,
      showDepartmentForm: false,
      isEditing: false,

      locations: [
        { id: 1, name: "Head Office" },
        { id: 2, name: "MTL Complex" },
        { id: 3, name: "Lilongwe TEC" },
        { id: 4, name: "Mzuzu TEC" },
        { id: 5, name: "Zomba TEC" },
      ],

      defaultProps: {
        children: "children",
        label: "position", // Adjusted to work with your data structure
      },

      currentDepartment: {
        departmentId: null,
        position: "",
        name: "",
        location: "",
        reports_to: null,
      },

      departments: [],
    };
  },

  computed: {
    organizationTree() {
      return this.buildOrganizationTree(null);
    },
    expandedKeys() {
      // Collect all level 2 DepartmentIDs
      return this.departments
        .filter((dept) => this.getLevel(dept.departmentId) === 1)
        .map((dept) => dept.departmentId);
    },
    isFormValid() {
      return (
        this.currentDepartment.position &&
        this.currentDepartment.name &&
        this.currentDepartment.location
      );
    },
  },

  methods: {
    async handleDrop(draggingNode, dropNode, dropType) {
      const draggedDept = draggingNode.data;
      const newParentDept = dropNode.data;

      if (dropType === "inner") {
        draggedDept.reports_to = newParentDept.departmentId;
      } else if (dropType === "before" || dropType === "after") {
        draggedDept.reports_to = newParentDept.reports_to; // Keep the same parent
      }

      await this.updateDepartmentParent(draggedDept);
    },

    async updateDepartmentParent(department) {
      try {
        await this.departService.update(department.departmentId, department);
        this.$alert.success("Position updated successfully");
        //await this.fetchDepartments(); // Refresh tree
      } catch (error) {
        this.$alert.error("Failed to update department position");
      }
    },
    buildOrganizationTree(parentId) {
      const nodes = this.departments.filter(
        (dept) => dept.reports_to === parentId
      );

      return nodes.map((node) => ({
        ...node,
        children: this.buildOrganizationTree(node.departmentId),
      }));
    },

    async fetchDepartments() {
      this.loading = true;
      try {
        const response = await this.departService.getAll();
        this.departments = response;
      } catch (error) {
        this.$alert.error("Failed to fetch organization data");
      } finally {
        this.loading = false;
      }
    },

    showAddForm() {
      this.isEditing = false;
      this.showDepartmentForm = true;
      this.resetForm();
    },

    resetForm() {
      this.currentDepartment = {
        departmentId: null,
        position: "",
        name: "",
        location: "",
        reports_to: null,
      };
    },
    getLevel(departmentId, level = 0) {
      const department = this.departments.find(
        (dept) => dept.departmentId === departmentId
      );
      return department && department.reports_to
        ? this.getLevel(department.reports_to, level + 1)
        : level + 1;
    },
    async submitDepartment() {
      this.loading = true;
      try {
        const departmentData = {
          position: this.currentDepartment.position,
          name: this.currentDepartment.name,
          location: this.currentDepartment.location,
          reports_to: this.currentDepartment.reports_to || null,
        };

        if (this.isEditing) {
          await this.departService.update(
            this.currentDepartment.departmentId,
            this.currentDepartment
          );
        } else {
          await this.departService.create(departmentData);
        }

        await this.fetchDepartments();
        this.showDepartmentForm = false;
        this.resetForm();
        this.$alert.success(
          `Position ${this.isEditing ? "updated" : "added"} successfully`
        );
      } catch (error) {
        this.$alert.error(
          `Failed to ${this.isEditing ? "update" : "add"} position`
        );
      } finally {
        this.loading = false;
      }
    },

    editDepartment(department) {
      this.isEditing = true;
      this.currentDepartment = { ...department };
      this.showDepartmentForm = true;
    },

    async confirmDelete(department) {
      const hasSubordinates = this.departments.some(
        (dept) => dept.reports_to === department.departmentId
      );

      if (hasSubordinates) {
        this.$alert.error(
          "Cannot delete position with subordinates. Please reassign or delete subordinates first."
        );
        return;
      }

      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this position? This action cannot be undone."
        );

        if (confirmDelete.isConfirmed) {
          await this.departService.delete(department.departmentId);
          await this.fetchDepartments();
          this.$alert.success("Position deleted successfully");
        }
      } catch (error) {
        console.log(error);
        if (error !== "cancel") {
          this.$alert.error("Failed to delete position");
        }
      }
    },

    closeDepartmentForm() {
      this.showDepartmentForm = false;
      this.resetForm();
    },
  },

  created() {
    this.fetchDepartments();
  },
};
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 8px 0;
}

.position-title {
  font-weight: 500;
}

.department-name {
  color: #666;
}

.location-badge {
  background: #f0f2f5;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.action-buttons {
  margin-left: auto;
  display: none;
}

.custom-tree-node:hover .action-buttons {
  display: block;
}
</style>
