<template>
  <auth-layout pageClass="forgot-password-page mt-4">
    <div class="row d-flex justify-content-center align-items-center">
      <div class="col-lg-4 col-md-6 col-sm-8">
        <ValidationObserver v-slot="{ handleSubmit }">
          <form @submit.prevent="handleSubmit(submit)">
            <fade-render-transition>
              <card>
                <div slot="header">
                  <h3 class="card-title text-center">Forgot Password</h3>
                </div>
                <div>
                  <p class="text-center">
                    Enter your email address, and we'll send you a link to reset your password.
                  </p>
                  <ValidationProvider
                    name="email"
                    rules="required|email"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="email"
                      :error="failed ? 'The Email field is required and must be valid' : null"
                      :hasSuccess="passed"
                      v-model="email"
                    >
                    </fg-input>
                  </ValidationProvider>
                </div>
                <div class="text-center mt-3">
                  <button
                    type="submit"
                    class="btn btn-fill btn-info btn-round btn-wd"
                    :disabled="isLoading"
                  >
                    {{ isLoading ? "Sending..." : "Send Reset Link" }}
                  </button>
                  <br />
                  <router-link to="/login" class="card-category mt-2">
                    Back to Login
                  </router-link>
                </div>
              </card>
            </fade-render-transition>
          </form>
        </ValidationObserver>
      </div>
    </div>
  </auth-layout>
</template>

<script>
import { FadeRenderTransition } from "src/components/index";
import AuthLayout from "./Layout/AuthLayout.vue";
import { extend } from "vee-validate";
import { required, email } from "vee-validate/dist/rules";

import API from "@/services/api";

extend("email", email);
extend("required", required);

export default {
  components: {
    FadeRenderTransition,
    AuthLayout,
  },
  data() {
    return {
      userService: new API(process.env.VUE_APP_API_URL, "auth/forgot-password"),
      email: "",
      isLoading: false, // Tracks loading state
    };
  },
  methods: {
    async submit() {
      this.isLoading = true; // Set loading state
      try {
        const response = await this.userService.create({email:this.email});

        console.log(response)
        if (response.token) {
          this.$alert.success(response.message+" to your email!");
        } else {
          this.$alert.success("Failed to send reset link: "+response.message);
        }
      } catch (error) {
        console.error("Error sending reset link:", error);
        this.$alert.error("An error occurred while processing your request. Please try again later.");
      } finally {
        this.isLoading = false; // Reset loading state
      }
    },
  },
};
</script>

<style>
.navbar-nav .nav-item p {
  line-height: inherit;
  margin-left: 5px;
}
</style>
