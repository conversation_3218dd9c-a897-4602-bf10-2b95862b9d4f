<template>
  <div class="assignments-management">
    <!-- Assignment Form -->
    <card v-if="showAssignmentForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{ isEditing ? "Edit Assignment" : "Add Assignment" }}
        </h4>
      </template>
      <form @submit.prevent="addAssignment">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Issue" required>
              <el-select
                v-model="currentAssignment.issueId"
                placeholder="Select issue"
                filterable
                class="w-100"
                disabled
              >
                <el-option
                  v-for="issue in issues"
                  :key="issue.issueId"
                  :label="`${issue.title} (ID: ${issue.issueId})`"
                  :value="issue.issueId"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Assigned To" required>
              <el-select
                v-model="currentAssignment.assignedDepartmentId"
                placeholder="Select assignee"
                filterable
                class="w-100"
              >
              <el-option
                  v-for="department in departments"
                  :key="department.departmentId"
                  :label="department.name"
                  :value="department.departmentId"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <button 
              type="submit" 
              class="btn btn-fill btn-info"
              :disabled="isSaving"
            >
              <span v-if="!isSaving">
                {{ isEditing ? "Update Assignment" : "Transfer" }}
              </span>
              <span v-else>
                <i class="fas fa-spinner fa-spin mr-1"></i>
                {{ isEditing ? "Updating..." : "Transferring..." }}
              </span>
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeAssignmentForm"
              :disabled="isSaving"
            >
              Cancel
            </button>
          </div>
        </div>
      </form>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import {
  Select,
  Option,
} from "element-ui";
import API from "@/services/api";
import moment from "moment";

export default {
  name: "AssignmentsManagement",
  components: {
    "el-select": Select,
    "el-option": Option,
    "fg-input": FgInput,
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    userId() {
      return this.loggedInUser?.id;
    },
    queriedData() {
      let filtered = this.assignments.filter((assignment) =>
        Object.values(assignment).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.assignments.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  data() {
    return {
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      userService: new API(process.env.VUE_APP_API_URL, "users"),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      slapriority: 0,
      sladuedate: "",
      assignments: [],
      issues: [],
      issue:{},
      users: [],
      departments:[],
      currentAssignment: {
        assignedDepartmentId:null,
        assignmentId: null,
        issueId: null,
        assignedTo: null,
        startDate: null,
        dueDate: null,
      },
      isSaving: false,
      showAssignmentForm: false,
      isEditing: false,
    };
  },

  methods: {
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
      } catch (error) {
        this.$alert.error("Failed to fetch Departments");
      }
    },
    async addAssignment() {
      if (this.isSaving) return; // Prevent double submission
      this.isSaving = true;
      try {
        const newObject = { ...this.issue };
        newObject.assignedDepartmentId=this.currentAssignment.assignedDepartmentId
        newObject.AssignedDate = new Date();
         //temporarilly carrying user id but its reverted at server
        newObject.description=this.userId
        await this.issueService.update(newObject.issueId, newObject);
        this.$alert.success("Assignment done successfully");
        this.$router.push("/technical/issues")
   
        this.closeAssignmentForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to save Assignment"
        );
      } finally {
        this.isSaving = false;
      }
    },

    async loadAll() {
      try {
        this.assignments = await this.assignmentService.getAll();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to load assignments"
        );
      }
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },

    editAssignment(assignment) {
      this.currentAssignment = {
        ...assignment,
        dueDate: new Date(assignment.dueDate).toISOString().slice(0, 16),
      };
      this.isEditing = true;
      this.showAssignmentForm = true;
    },

    async deleteAssignment(assignmentId) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this Assignment?"
        );
        if (confirmDelete.isConfirmed) {
          await this.assignmentService.delete(assignmentId);
          await this.loadAll();
          this.$alert.success("Assignment deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete Assignment"
        );
      }
    },

    closeAssignmentForm() {
      this.isEditing = false;
      this.showAssignmentForm = false;
      this.isSaving = false;
      this.currentAssignment = {
        assignmentId: null,
        issueId: null,
        assignedTo: null,
        dueDate: null,
      };
    },
  },

  async created() {
    const issueId = this.$route.params.id;
    this.issue = await this.issueService.getById(issueId);
    this.issues = await this.issueService.getAll();

    if (issueId) {
      if (this.issue) {
        this.currentAssignment = {
          ...this.currentAssignment,
          issueId: this.issue.issueId,
        };
        this.showAssignmentForm = true;
      } else {
        this.$alert.warning(`Issue with ID ${issueId} not found.`);
      }
    }

    await this.fetchDepartments();
    await this.loadAll();
  },
};
</script>