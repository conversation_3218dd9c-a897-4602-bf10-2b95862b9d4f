<template>
  <div class="department-management">
    <!-- Department Form -->
    <card v-if="showDepartmentForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{ isEditing ? "Edit Department" : "Add Department" }}
        </h4>
      </template>
      <form @submit.prevent="submitDepartment">
        <div class="row">
          <div class="col-md-6">
            <fg-input
              label="Department Name"
              type="text"
              v-model="currentDepartment.name"
              :required="true"
              placeholder="Enter department name"
            />
          </div>
          <div class="col-md-6">
            <fg-input
              label="Head of Department (HOD)"
              type="text"
              v-model="currentDepartment.hod"
              :required="true"
              placeholder="Enter HOD name"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <fg-input
              label="Description"
              type="textarea"
              v-model="currentDepartment.description"
              placeholder="Enter department description"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Department" : "Add Department" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeDepartmentForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Department List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-8"><h4 class="card-title">Departments List</h4></div>
          <div class="col-4 text-right">
            <button class="btn btn-primary mx-2" @click="showDepartmentForm = true">
              Add Department
            </button>
            <router-link to="/customer/users" class="btn btn-success mx-2">
              Users
            </router-link>
          </div>
        </div>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            class="select-default mb-3"
            style="width: 200px"
            v-model="pagination.perPage"
            placeholder="Per page"
          >
            <el-option
              class="select-default"
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <el-input
            type="search"
            class="mb-3"
            style="width: 200px"
            placeholder="Search records"
            v-model="searchQuery"
            aria-controls="datatables"
          />
        </div>
        <div class="col-sm-12">
          <el-table :data="queriedData" style="width: 100%" stripe border>
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
            />
            <el-table-column label="Actions" width="180">
              <template v-slot="scope">
                <button
                  class="btn btn-info btn-sm mr-2"
                  @click="editDepartment(scope.row)"
                >
                  <i class="fa fa-pencil"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm"
                  @click="deleteDepartment(scope.row.departmentId)"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <div class="">
          <p class="card-category">
            Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
          </p>
        </div>
        <l-pagination
          class="pagination-no-border"
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>
  
  <script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input } from "element-ui";

import API from "../../../services/api";

export default {
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
  },

  data() {
    return {
      departService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "departments" // Endpoint
      ),
      searchQuery: "",
      loading: false,
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      tableColumns: [
        { prop: "name", label: "Name", minWidth: 150 },
        { prop: "hod", label: "HOD", minWidth: 200 },
        { prop: "description", label: "Description", minWidth: 250 },
        { prop: "createdAt", label: "Created At", minWidth: 200 },
      ],
      departments: [], // List of departments
      currentDepartment: {
        departmentId: null,
        name: "",
        hod: "",
        description: "",
        createdAt: null,
      },
      showDepartmentForm: false,
      isEditing: false,
    };
  },

  computed: {
    queriedData() {
      let filtered = this.departments?.filter((department) =>
        Object.values(department).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered?.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    total() {
      return this.departments?.length;
    },
  },

  methods: {
    async fetchDepartments() {
      this.loading = true;
      try {
        // Fetch departments from the API
        const response = await this.departService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          search: this.searchQuery,
        });


        this.departments = response;
        this.pagination.total = response?.length;

        // // Success alert (optional)
        // this.$alert.success("Departments fetched successfully");
      } catch (error) {
        // Error handling
        this.$alert.error(
          error.response?.data?.message || "Failed to fetch departments"
        );

        // Fallback to default departments if needed
        this.departments = [
          {
            departmentId: 1,
            name: "Engineering",
            hod: "Alice Johnson",
            description: "Handles all engineering tasks.",
            createdAt: "2024-01-10",
          },
        ];
      } finally {
        this.loading = false;
      }
    },

    async submitDepartment() {
      this.loading = true;
      try {
        if (this.isEditing) {
          await this.updateDepartment();
        } else {
          await this.addDepartment();
        }

        // Refresh departments after successful operation
        this.fetchDepartments();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to submit department"
        );
      } finally {
        this.loading = false;
      }
    },

    async addDepartment() {
      try {
        // Create department via API

        let newObject={...this.currentDepartment}
        delete newObject.departmentId
        const newDepartment = await this.departService.create(
          newObject
        );

        this.$alert.success("Department added successfully");
        this.closeDepartmentForm();
      } catch (error) {
        throw error;
      }
    },

    async updateDepartment() {
      try {
        // Update department via API
        const updatedDepartment = await this.departService.update(
          this.currentDepartment.departmentId,
          this.currentDepartment
        );

        this.$alert.success("Department updated successfully");
        this.closeDepartmentForm();
      } catch (error) {
        throw error;
      }
    },

    editDepartment(department) {
      this.currentDepartment = { ...department };
      this.isEditing = true;
      this.showDepartmentForm = true;
    },
    async deleteService(userID) {
      try {
        // Confirm before deletion
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this department?"
        );

        if (confirmDelete.isConfirmed) {
          await this.departmentService.delete(userID);

          // Remove from local state
          this.services = this.departments?.filter(
            (s) => s.departmentId !== userID
          );

          this.$alert.success("Department deleted successfully");
          this.fetchDepartments();
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete department"
        );
      }
    },

    closeDepartmentForm() {
      this.isEditing = false;
      this.showDepartmentForm = false;
      this.currentDepartment = {
        departmentId: null,
        name: "",
        hod: "",
        description: "",
        createdAt: null,
      };
    },

    // Pagination handler
    handlePageChange(page) {
      this.pagination.currentPage = page;
      this.fetchDepartments();
    },
  },

  created() {
    console.log(this.departService);
    // Fetch departments when component is created
    this.fetchDepartments();
  },

  watch: {
    // Trigger search when query changes
    searchQuery() {
      this.fetchDepartments();
    },
  },
};
</script>
  