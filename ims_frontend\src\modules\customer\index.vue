<template>
  <div class="py-3">
    <!-- Quick Action Buttons -->
    <div class="row mb-4">
      <div class="col-12 text-start">
        <router-link to="/customer/issues" class="btn btn-primary me-2 mr-3">
          <i class="fas fa-plus-circle me-1"></i> Report New Issue
        </router-link>
      </div>
    </div>

    <!-- Filters Row -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Status Filter</label>
                <select v-model="statusFilter" class="form-control">
                  <option value="">All Statuses</option>
                  <option
                    v-for="status in uniqueStatuses"
                    :key="status"
                    :value="status"
                  >
                    {{ status }}
                  </option>
                </select>
              </div>

              <div class="col-md-4">
                <label class="form-label">Date Range</label>
                <select v-model="trendsPeriod" class="form-control">
                  <option value="7">Last 7 days</option>
                  <option value="30">Last 30 days</option>
                  <option value="90">Last 90 days</option>
                  <option value="365">Last year</option>
                </select>
              </div>
              <div class="col-md-2 d-flex align-items-end">
                <button class="btn btn-secondary w-100" @click="resetFilters">
                  <i class="fas fa-undo me-1"></i> Reset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards Row -->
    <div class="row">
      <div
        class="col-12 col-sm-6 col-xl-3 mb-1"
        v-for="(stat, index) in statsList"
        :key="index"
      >
        <stats-card
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :color="stat.color"
          :loading="loading"
        >
          <template #footer>
            <div v-if="stat.footer" class="d-flex justify-content-between">
              <small v-for="(footerText, idx) in stat.footer" :key="idx">{{
                footerText
              }}</small>
            </div>
          </template>
        </stats-card>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
      <div class="col-12 col-lg-8 mb-3">
        <div class="card h-100">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h5 class="card-title mb-0">Issue Trends</h5>
          </div>
          <div class="card-body">
            <div id="trends-chart"></div>
          </div>
        </div>
      </div>

      <div class="col-12 col-lg-4 mb-3">
        <div class="card h-100">
          <div class="card-header">
            <h5 class="card-title mb-0">Issues by Status</h5>
          </div>
          <div class="card-body">
            <div id="category-chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Issues Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h5 class="card-title mb-0">Recent Issues</h5>
            <div>
              <input
                v-model="searchQuery"
                class="form-control form-control-sm d-inline-block me-2"
                style="width: 200px"
                placeholder="Search issues..."
              />
              <button
                class="btn btn-sm btn-outline-primary"
                @click="refreshIssues"
              >
                <i class="fas fa-sync-alt"></i> Refresh
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                  <tr>
                    <th @click="sort('issueId')" class="sortable">
                      ID <i class="fas" :class="getSortIcon('issueId')"></i>
                    </th>
                    <th @click="sort('title')" class="sortable">
                      Title <i class="fas" :class="getSortIcon('title')"></i>
                    </th>
                    <th @click="sort('organization')" class="sortable">
                      Customer
                      <i class="fas" :class="getSortIcon('organization')"></i>
                    </th>
                    <th @click="sort('status')" class="sortable">
                      Status <i class="fas" :class="getSortIcon('status')"></i>
                    </th>
                    <th @click="sort('reportedAt')" class="sortable">
                      Created
                      <i class="fas" :class="getSortIcon('reportedAt')"></i>
                    </th>
                    <th @click="sort('reportedAt')" class="sortable">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody v-if="!loading">
                  <tr
                    v-for="issue in filteredAndSortedIssues"
                    :key="issue.issueId"
                  >
                    <td>#{{ issue.issueId }}</td>
                    <td>{{ issue.title }}</td>
                    <td>{{ issue.organization }}</td>
                    <td>
                      <span :class="getStatusBadgeClass(mapStatusForCustomer(issue.status))">{{
                        mapStatusForCustomer(issue.status)
                      }}</span>
                    </td>
                    <td>{{ formatDate(issue.reportedAt) }}</td>
                    <td>
                      <button
                        class="btn btn-sm btn-outline-primary me-1"
                        @click="viewIssue(issue.issueId)"
                      >
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <tr>
                    <td colspan="7" class="text-center">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="card-footer text-center">
            <router-link class="btn btn-outline-primary" to="/customer/issues">
              View All Issues
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as d3 from "d3";
import * as dc from "dc";
import * as crossfilter from "crossfilter2";
import StatsCard from "@/components/Cards/Tile.vue";
import API from "@/services/api"; // Import userService

export default {
  name: "Dashboard",
  components: {
    StatsCard,
  },


  data() {
    return {
      issuesService: new API(process.env.VUE_APP_API_URL, "Issues/all-issues"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      servicesService: new API(process.env.VUE_APP_API_URL, "services"), // Add services service
      issues: [],
      departments: [],
      services: [], // Add services data
      statsList: [],
      loading: true,
      trendsPeriod: "30",
      recentIssues: [],
      ndx: null,
      statusFilter: "",
      departmentFilter: "",
      searchQuery: "",
      sortKey: "reportedAt",
      sortOrder: "desc",
    };
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    customerId() {
      return this.loggedInUser?.account; // Assuming the user object has a `customerId` field
    },

    uniqueStatuses() {
      // Only show allowed statuses in filter dropdown
      const allowedStatuses = ['closed', 'in progress', 'resolved'];
      return allowedStatuses.map(status =>
        status.charAt(0).toUpperCase() + status.slice(1)
      );
    },

    filteredAndSortedIssues() {
      return this.recentIssues
        .filter((issue) => {
          const mappedStatus = this.mapStatusForCustomer(issue.status);
          const matchesStatus =
            !this.statusFilter || mappedStatus === this.statusFilter;
          const matchesDepartment =
            !this.departmentFilter ||
            issue.assignedDepartmentId === this.departmentFilter;
          const matchesSearch =
            !this.searchQuery ||
            issue.title
              .toLowerCase()
              .includes(this.searchQuery.toLowerCase()) ||
            issue.organization
              .toLowerCase()
              .includes(this.searchQuery.toLowerCase());
          return matchesStatus && matchesDepartment && matchesSearch;
        })
        .sort((a, b) => {
          const aValue = a[this.sortKey];
          const bValue = b[this.sortKey];
          const modifier = this.sortOrder === "asc" ? 1 : -1;

          if (this.sortKey === "reportedAt") {
            return modifier * (new Date(aValue) - new Date(bValue));
          }
          return modifier * String(aValue).localeCompare(String(bValue));
        });
    },
  },
  methods: {
    // Map issue status for customer display
    mapStatusForCustomer(status) {
      const allowedStatuses = ['closed', 'in progress', 'resolved'];
      const normalizedStatus = status.toLowerCase();

      if (allowedStatuses.includes(normalizedStatus)) {
        return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
      }

      // Map any other status to 'In progress'
      return 'In progress';
    },

    async fetchDashboardData() {
      this.loading = true;
      try {
        const [issuesResponse, departmentsResponse, servicesResponse] =
          await Promise.all([
            this.issuesService.getAll(),
            this.departmentService.getAll(),
            this.servicesService.getAll(), // Fetch services data
          ]);



        this.issues = issuesResponse.filter((x) => x.account == this.customerId);

        //console.log(this.loggedInUser)
        this.departments = departmentsResponse;
        this.services = servicesResponse; // Store services data
        this.recentIssues = this.issues.slice(0, 10); // Show only recent 10 issues
        this.statsList = this.calculateStats();
        this.ndx = crossfilter.default(this.issues);
        this.renderCharts();
      } catch (error) {
        //console.error("Error fetching dashboard data:", error);
      } finally {
        this.loading = false;
      }
    },
    calculateStats() {
      const totalIssues = this.issues.length;
      const inProgressIssues = this.issues.filter(
        (issue) => this.mapStatusForCustomer(issue.status) === "In progress"
      ).length;
      const closedIssues = this.issues.filter(
        (issue) => this.mapStatusForCustomer(issue.status) === "Closed"
      ).length;
      const resolvedIssues = this.issues.filter(
        (issue) => this.mapStatusForCustomer(issue.status) === "Resolved"
      ).length;

      return [
        {
          title: "Total Issues",
          value: totalIssues,
          icon: "fas fa-tasks",
          color: "primary",
        },
        {
          title: "In Progress",
          value: inProgressIssues,
          icon: "fas fa-exclamation-circle",
          color: "warning",
        },
        {
          title: "Resolved",
          value: resolvedIssues,
          icon: "fas fa-check-circle",
          color: "success",
        },
        {
          title: "Closed",
          value: closedIssues,
          icon: "fas fa-times-circle",
          color: "info",
        }
      ];
    },
    navigateToServices() {
      this.$router.push("/customer/services"); // Navigate to services page
    },

    sort(key) {
      if (this.sortKey === key) {
        this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
      } else {
        this.sortKey = key;
        this.sortOrder = "asc";
      }
    },
    getSortIcon(key) {
      if (this.sortKey === key) {
        return this.sortOrder === "asc" ? "fa-sort-up" : "fa-sort-down";
      }
      return "fa-sort";
    },
    resetFilters() {
      this.statusFilter = "";
      this.departmentFilter = "";
      this.searchQuery = "";
      this.trendsPeriod = "30";
      this.fetchDashboardData();
    },
    renderCharts() {
      const themeColors = {
        primary: "#302e77",
        secondary: "#87CB16",
        success: "#28A745",
        warning: "#FFA534",
        danger: "#DC3545",
        info: "#17A2B8",
      };
      const colorScale = d3.scaleOrdinal().range(Object.values(themeColors));

      // Trends Chart
      const dateFormat = d3.timeFormat("%Y-%m");
      const dateDim = this.ndx.dimension((d) =>
        dateFormat(new Date(d.reportedAt))
      );
      const dateGroup = dateDim.group().reduceCount();

      this.trendsChart = dc.lineChart("#trends-chart");
      this.trendsChart
        .width(600)
        .height(300)
        .dimension(dateDim)
        .group(dateGroup)
        .x(d3.scaleBand().domain(dateGroup.all().map((d) => d.key)))
        .elasticY(true)
        .colors(colorScale)
        .renderArea(true)
        .brushOn(false)
        .renderDataPoints(true);

      // Category Chart - use mapped statuses for customer display
      const statusDim = this.ndx.dimension((d) => this.mapStatusForCustomer(d.status));
      const statusGroup = statusDim.group();

      this.categoryChart = dc.pieChart("#category-chart");
      this.categoryChart
        .width(250)
        .height(250)
        .dimension(statusDim)
        .group(statusGroup)
        .colors(colorScale)
        .innerRadius(40);
      // .legend(dc.legend().horizontal(true).itemWidth(70).x(10).y(200));

      dc.renderAll();
    },
    getDepartment(departmentId) {
      const dept = this.departments.find(
        (d) => d.departmentId === departmentId
      );
      return dept ? dept.name : "Unknown";
    },
    getStatusBadgeClass(status) {
      const statusMap = {
        "In progress": "warning",
        "Resolved": "success",
        "Closed": "info"
      };
      return `badge bg-${statusMap[status] || "secondary"}`;
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString();
    },
    viewIssue(issueId) {
      this.$router.push(`/customer/issue-view/${issueId}`);
    },
    refreshIssues() {
      this.fetchDashboardData();
    },
  },
  created() {
    this.fetchDashboardData();
  },
};
</script>

<style scoped>
.sortable {
  cursor: pointer;
  user-select: none;
}
.sortable:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
.stats-card {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}
.stats-card:hover {
  transform: translateY(-5px);
}

.sortable {
  cursor: pointer;
  user-select: none;
}
.sortable:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>