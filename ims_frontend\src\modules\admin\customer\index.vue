<template>
  <div class="customer-management">
    <!-- Customer Form -->
    <card v-if="showCustomerForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{ isEditing ? "Edit Customer" : "Add Customer" }}
        </h4>
      </template>
      <form @submit.prevent="submitCustomer">
        <div class="row">
          <div class="col-md-6">
            <fg-input
              label="Customer Name"
              type="text"
              v-model="currentCustomer.name"
              :required="true"
              placeholder="Enter customer name"
            />
          </div>
          <div class="col-md-6">
            <fg-input
              label="Customer Account"
              type="number"
              v-model="currentCustomer.account"
              :required="true"
              placeholder="Enter customer account"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <fg-input
              label="Email"
              type="email"
              v-model="currentCustomer.email"
              :required="true"
              placeholder="Enter email address"
            />
          </div>
          <div class="col-md-4">
            <fg-input
              label="Phone"
              type="tel"
              v-model="currentCustomer.phone"
              placeholder="Enter phone number"
            />
          </div>
          <div class="col-md-4">
            <fg-input label="Customer Type">
              <el-select
                v-model="currentCustomer.customerType"
                placeholder="Select customer type"
                class="w-100"
              >
                <el-option
                  v-for="type in customerTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <fg-input label="Address">
              <el-input
                v-model="currentCustomer.address"
                placeholder="Enter customer address"
                type="textarea"
                rows="3"
              />
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Customer" : "Add Customer" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeCustomerForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Customer List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">Customer List</h4></div>
          <div class="col-6 text-right">
            <router-link to="/admin/users" class="btn btn-warning mx-2">
             Users
            </router-link>
          </div>
        </div>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search records"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table :data="queriedData" stripe border style="width: 100%">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
            ></el-table-column>

          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input } from "element-ui";
import API from "@/services/api"; // Importing API service

export default {
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
  },
  data() {
    return {
      customerService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "customers" // Endpoint
      ),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      customerTypes: ["Individual", "Corporate"],
      tableColumns: [
        { prop: "name", label: "Name", minWidth: 150 },
        { prop: "account", label: "Account", minWidth: 100 },
        { prop: "email", label: "Email", minWidth: 150 },
        { prop: "phone", label: "Phone", minWidth: 90 },
        { prop: "customerType", label: "Type", minWidth: 90 },
        { prop: "address", label: "Address", minWidth: 90 },
      ],
      customers: [],
      currentCustomer: {
        customerId: null,
        name: "",
        email: "",
        phone: "",
        account: "",
        address: "",
        customerType: null,
      },
      showCustomerForm: false,
      isEditing: false,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.customers?.filter((customer) =>
        Object.values(customer).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered?.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.customers?.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    async fetchCustomers() {
      try {
        const response = await this.customerService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          searchQuery: this.searchQuery,
        });

        this.customers = response; // Assuming response contains the data array
        this.pagination.total = response.total; // Assuming response contains total count
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to fetch customers"
        );
        console.error("Error fetching customers:", error);
      }
    },

    async submitCustomer() {
      if (this.isEditing) {
        await this.updateCustomer();
      } else {
        await this.addCustomer();
      }
    },

    async addCustomer() {
      try {
        const newObject = { ...this.currentCustomer };
        delete newObject.customerId; // Remove ID for new customer

        await this.customerService.create(newObject); // Call API to create a new customer
        this.$alert.success("Customer added successfully");
        await this.fetchCustomers(); // Refresh the list after adding
        this.closeCustomerForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to add customer"
        );
      }
    },

    async updateCustomer() {
      try {
        await this.customerService.update(
          this.currentCustomer.customerId,
          this.currentCustomer
        ); // Call API to update existing customer
        await this.fetchCustomers(); // Refresh the list after updating
        this.closeCustomerForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update customer"
        );
      }
    },

    editCustomer(customer) {
      this.currentCustomer = { ...customer };
      this.isEditing = true;
      this.showCustomerForm = true;
    },

    async deleteCustomer(customerId) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this customer?"
        );
        if (confirmDelete.isConfirmed) {
          await this.customerService.deleteCustomer(customerId); // Call API to delete the customer
          await this.fetchCustomers(); // Refresh the list after deletion
          this.$alert.success("Customer deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete customer"
        );
      }
    },

    closeCustomerForm() {
      this.isEditing = false;
      this.showCustomerForm = false;
      // Reset current customer object
      this.currentCustomer = {
        customerId: null,
        name: "",
        email: "",
        phone: "",
        account: "",
        address: "",
        customerType: null,
      };
    },
  },
  created() {
    // Fetch customers and types when component is created
    this.fetchCustomers();
  },
};
</script>

