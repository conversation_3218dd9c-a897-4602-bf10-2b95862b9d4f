<template>
    <div class="issue-log-management">
      <!-- Issue Log Form -->
      <card v-if="showIssueLogForm" class="mt-4">
        <template slot="header">
          <h4 class="card-title">{{ isEditing ? "Edit Log" : "Add Log" }}</h4>
        </template>
        <form @submit.prevent="submitIssueLog">
          <div class="row">
            <div class="col-md-12">
              <fg-input
                label="Issue ID"
                type="number"
                v-model="currentIssueLog.issueID"
                :required="true"
                placeholder="Enter Issue ID"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <fg-input
                label="Assignment ID"
                type="number"
                v-model="currentIssueLog.assignmentID"
                :required="true"
                placeholder="Enter Assignment ID"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <fg-input
                label="Action Taken"
                type="textarea"
                v-model="currentIssueLog.actionTaken"
                :required="true"
                placeholder="Describe action taken"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <button type="submit" class="btn btn-fill btn-info">
                {{ isEditing ? "Update Log" : "Add Log" }}
              </button>
              <button
                type="button"
                class="btn btn-fill btn-secondary ml-2"
                @click="closeIssueLogForm"
              >
                Close
              </button>
            </div>
          </div>
        </form>
      </card>
  
      <!-- Issue Log List -->
      <card>
        <template slot="header">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="card-title">Issue Log List</h4>
            <button class="btn btn-primary" @click="showIssueLogForm = true">
              Add Log
            </button>
          </div>
        </template>
        <div>
          <div class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap">
            <el-select
              class="select-default mb-3"
              style="width: 200px"
              v-model="pagination.perPage"
              placeholder="Per page"
            >
              <el-option
                class="select-default"
                v-for="item in pagination.perPageOptions"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
            <el-input
              type="search"
              class="mb-3"
              style="width: 200px"
              placeholder="Search records"
              v-model="searchQuery"
              aria-controls="datatables"
            />
          </div>
          <div class="col-sm-12">
            <el-table :data="queriedData" style="width: 100%" stripe border>
              <el-table-column
                v-for="column in tableColumns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                :min-width="column.minWidth"
              />
              <el-table-column label="Actions" width="180">
                <template v-slot="scope">
                  <button
                    class="btn btn-info btn-sm mr-2"
                    @click="editIssueLog(scope.row)"
                  >
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button
                    class="btn btn-danger btn-sm"
                    @click="deleteIssueLog(scope.row.LogID)"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
  
        <div
          slot="footer"
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <div class="">
            <p class="card-category">
              Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
            </p>
          </div>
          <l-pagination
            class="pagination-no-border"
            v-model="pagination.currentPage"
            :per-page="pagination.perPage"
            :total="pagination.total"
          >
          </l-pagination>
        </div>
      </card>
    </div>
  </template>
  
  <script>
  import {
    FormGroupInput as FgInput,
    Pagination as LPagination,
  } from "src/components/index";
  import { Select, Option, Table, TableColumn, Input } from "element-ui";
  
  export default {
    components: {
      FgInput,
      LPagination,
      "el-select": Select,
      "el-option": Option,
      "el-table": Table,
      "el-table-column": TableColumn,
      "el-input": Input,
    },
    data() {
      return {
        searchQuery: "",
        pagination: {
          perPage: 5,
          currentPage: 1,
          perPageOptions: [5, 10, 15],
          total: 0,
        },
        tableColumns: [
          { prop: "issueID", label: "Issue ID", minWidth: 120 },
          { prop: "assignmentID", label: "Assignment ID", minWidth: 120 },
          { prop: "actionTaken", label: "Action Taken", minWidth: 120 },
          { prop: "updatedAt", label: "Updated At", minWidth: 160 },
        ],
        issueLogs: [],
        currentIssueLog: {
          LogID: null,
          IssueID: null,
          AssignmentID: null,
          UpdatedBy: null,
          ActionTaken: "",
          UpdatedAt: null,
        },
        showIssueLogForm: false,
        isEditing: false,
      };
    },
    computed: {
      queriedData() {
        let filtered = this.issueLogs.filter((log) =>
          Object.values(log).some((value) =>
            value
              ?.toString()
              .toLowerCase()
              .includes(this.searchQuery.toLowerCase())
          )
        );
        return filtered.slice(
          (this.pagination.currentPage - 1) * this.pagination.perPage,
          this.pagination.currentPage * this.pagination.perPage
        );
      },
  
      to() {
        let highBound = this.from + this.pagination.perPage;
        if (this.total < highBound) {
          highBound = this.total;
        }
        return highBound;
      },
      from() {
        return this.pagination.perPage * (this.pagination.currentPage - 1);
      },
      total() {
        this.paginationTotal(this.issueLogs.length);
        return this.issueLogs.length;
      },
    },
  
    methods: {
      paginationTotal(value) {
        this.pagination.total = value;
      },
      fetchIssueLogs() {
        this.issueLogs = [
          {
            LogID: 1,
            IssueID: 1,
            AssignmentID: 101,
            ActionTaken: "Resolved network downtime issue.",
            UpdatedAt: "2024-12-01 08:00:00",
          },
          {
            LogID: 2,
            IssueID: 2,
            AssignmentID: 102,
            ActionTaken: "Investigating customer email access issue.",
            UpdatedAt: "2024-12-02 10:00:00",
          },
        ];
      },
      submitIssueLog() {
        if (this.isEditing) this.updateIssueLog();
        else this.addIssueLog();
      },
      addIssueLog() {
        this.issueLogs.push({
          ...this.currentIssueLog,
          LogID: this.issueLogs.length + 1,
          UpdatedAt: new Date(),
        });
        this.closeIssueLogForm();
      },
      updateIssueLog() {
        const index = this.issueLogs.findIndex(
          (log) => log.LogID === this.currentIssueLog.LogID
        );
        if (index >= 0) this.issueLogs.splice(index, 1, this.currentIssueLog);
        this.closeIssueLogForm();
      },
      editIssueLog(log) {
        this.currentIssueLog = { ...log };
        this.isEditing = true;
        this.showIssueLogForm = true;
      },
      deleteIssueLog(LogID) {
        this.issueLogs = this.issueLogs.filter((log) => log.LogID !== LogID);
      },
      closeIssueLogForm() {
        this.isEditing = false;
        this.showIssueLogForm = false;
        this.currentIssueLog = {
          LogID: null,
          IssueID: null,
          AssignmentID: null,
          UpdatedBy: null,
          ActionTaken: "",
          UpdatedAt: null,
        };
      },
    },
    created() {
      // Fetch issue logs when component is created
      this.fetchIssueLogs();
    },
  };
  </script>
  