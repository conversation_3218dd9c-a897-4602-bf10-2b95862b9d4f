<template>
    <div class="sla-management">
      <!-- SLA Form -->
      <card v-if="showSLAForm" class="mt-4">
        <template slot="header">
          <h4 class="card-title">{{ isEditing ? "Edit SLA" : "Add SLA" }}</h4>
        </template>
        <form @submit.prevent="submitSLA">
          <div class="row">
            <div class="col-md-6">
              <fg-input
                label="Customer ID"
                type="number"
                v-model="currentSLA.customerID"
                :required="true"
                readonly
                placeholder="Enter customer ID"
              />
            </div>
            <div class="col-md-6">
              <fg-input
                label="Service ID"
                type="number"
                v-model="currentSLA.serviceID"
                :required="true"
                placeholder="Enter service ID"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <fg-input label="Priority">
                <el-select
                  v-model="currentSLA.priority"
                  placeholder="Select priority"
                  class="w-100"
                >
                  <el-option
                    v-for="priority in slaPriorities"
                    :key="priority"
                    :label="priority"
                    :value="priority"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input
                label="Resolution Time (Hours)"
                type="number"
                v-model="currentSLA.resolutionTimeHours"
                :required="true"
                placeholder="Enter resolution time"
              />
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <button type="submit" class="btn btn-fill btn-info">
                {{ isEditing ? "Update SLA" : "Add SLA" }}
              </button>
              <button
                type="button"
                class="btn btn-fill btn-secondary ml-2"
                @click="closeSLAForm"
              >
                Close
              </button>
            </div>
          </div>
        </form>
      </card>
  
      <!-- SLA List -->
      <card>
        <template slot="header">
          <div class="d-flex justify-content-between align-items-center">
            <h4 class="card-title">SLA List</h4>
            <button class="btn btn-primary" @click="showSLAForm = true">
              Add SLA
            </button>
          </div>
        </template>
        <div>
          <div
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <el-select
              class="select-default mb-3"
              style="width: 200px"
              v-model="pagination.perPage"
              placeholder="Per page"
            >
              <el-option
                class="select-default"
                v-for="item in pagination.perPageOptions"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
            <el-input
              type="search"
              class="mb-3"
              style="width: 200px"
              placeholder="Search records"
              v-model="searchQuery"
              aria-controls="datatables"
            />
          </div>
          <div class="col-sm-12">
            <el-table :data="queriedData" style="width: 100%" stripe border>
              <el-table-column
                v-for="column in tableColumns"
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                :min-width="column.minWidth"
              />
  
              <el-table-column label="Actions" width="180">
                <template v-slot="scope">
                  <button
                    class="btn btn-info btn-sm mr-2"
                    @click="editSLA(scope.row)"
                  >
                    <i class="fa fa-pencil"></i>
                  </button>
                  <button
                    class="btn btn-danger btn-sm"
                    @click="deleteSLA(scope.row.slaID)"
                  >
                    <i class="fa fa-trash"></i>
                  </button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
  
        <div
          slot="footer"
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <div class="">
            <p class="card-category">
              Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
            </p>
          </div>
          <l-pagination
            class="pagination-no-border"
            v-model="pagination.currentPage"
            :per-page="pagination.perPage"
            :total="pagination.total"
          >
          </l-pagination>
        </div>
      </card>
    </div>
  </template>
  
  <script>
  import {
    FormGroupInput as FgInput,
    Pagination as LPagination,
  } from "src/components/index";
  import { Select, Option, Table, TableColumn, Input } from "element-ui";
  
  export default {
    components: {
      FgInput,
      LPagination,
      "el-select": Select,
      "el-option": Option,
      "el-table": Table,
      "el-table-column": TableColumn,
      "el-input": Input,
    },
    data() {
      return {
        searchQuery: "",
        pagination: {
          perPage: 5,
          currentPage: 1,
          perPageOptions: [5, 10, 15],
          total: 0,
        },
        tableColumns: [
          { prop: "customerID", label: "Customer ID", minWidth: 120 },
          { prop: "serviceID", label: "Service ID", minWidth: 120 },
          { prop: "priority", label: "Priority", minWidth: 120 },
          { prop: "resolutionTimeHours", label: "Resolution Time (Hours)", minWidth: 160 },
        ],
        slaPriorities: ["Low", "Medium", "High", "Critical"],
        slas: [], // Data to display
        currentSLA: {
          slaID: null,
          customerID: "",
          serviceID: "",
          priority: "Medium",
          resolutionTimeHours: 0,
        },
        showSLAForm: false,
        isEditing: false,
      };
    },
    computed: {
      queriedData() {
        let filtered = this.slas.filter((sla) =>
          Object.values(sla).some((value) =>
            value
              ?.toString()
              .toLowerCase()
              .includes(this.searchQuery.toLowerCase())
          )
        );
        return filtered.slice(
          (this.pagination.currentPage - 1) * this.pagination.perPage,
          this.pagination.currentPage * this.pagination.perPage
        );
      },
  
      to() {
        let highBound = this.from + this.pagination.perPage;
        if (this.total < highBound) {
          highBound = this.total;
        }
        return highBound;
      },
      from() {
        return this.pagination.perPage * (this.pagination.currentPage - 1);
      },
      total() {
        this.paginationTotal(this.slas.length);
        return this.slas.length;
      },
    },
  
    methods: {
      paginationTotal(value) {
        this.pagination.total = value;
      },
      fetchSLAs() {
        this.slas = [
          {
            slaID: 1,
            customerID: 101,
            serviceID: 1,
            priority: "High",
            resolutionTimeHours: 48,
          },
          {
            slaID: 2,
            customerID: 102,
            serviceID: 2,
            priority: "Medium",
            resolutionTimeHours: 72,
          },
        ];
      },
      submitSLA() {
        if (this.isEditing) this.updateSLA();
        else this.addSLA();
      },
      addSLA() {
        this.slas.push({ ...this.currentSLA, slaID: this.slas.length + 1 });
        this.closeSLAForm();
      },
      updateSLA() {
        const index = this.slas.findIndex((s) => s.slaID === this.currentSLA.slaID);
        if (index >= 0) this.slas.splice(index, 1, this.currentSLA);
        this.closeSLAForm();
      },
      editSLA(sla) {
        this.currentSLA = { ...sla };
        this.isEditing = true;
        this.showSLAForm = true;
      },
      deleteSLA(slaID) {
        this.slas = this.slas.filter((s) => s.slaID !== slaID);
      },
      closeSLAForm() {
        this.isEditing = false;
        this.showSLAForm = false;
        this.currentSLA = {
          slaID: null,
          customerID: "",
          serviceID: "",
          priority: "Medium",
          resolutionTimeHours: 0,
        };
      },
    },
    created() {
      // Fetch SLAs when component is created
      this.fetchSLAs();
    },
  };
  </script>
  