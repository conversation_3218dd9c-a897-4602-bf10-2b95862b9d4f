<template>
    <div class="email-confirmation">
      <div v-if="loading" class="loading">
        <p>Confirming your email, please wait...</p>
      </div>
      
      <div v-if="success" class="success">
        <h1>Email Confirmed!</h1>
        <p>Your email has been successfully verified. You can now proceed to login or explore the application.</p>
        <router-link to="/login">
          <button class="btn btn-primary">Go to Login</button>
        </router-link>
      </div>
      
      <div v-if="error" class="error">
        <h1>Confirmation Failed</h1>
        <p>{{ errorMessage }}</p>
        <button class="btn btn-secondary" @click="retryConfirmation">Retry</button>
      </div>
    </div>
  </template>
  
  <script>
  import axios from "axios";
  import API from "@/services/api";
  
  export default {
    data() {
      return {
        userService: new API(process.env.VUE_APP_API_URL, "auth/verify-email"),
        loading: true,
        success: false,
        error: false,
        errorMessage: "",
      };
    },
    mounted() {
      this.confirmEmail();
    },
    methods: {
      async confirmEmail() {
        try {
          const token = this.$route.query.token;
          const email = this.$route.query.email; // Assume token is passed via query params
          if (!token||!email) throw new Error("Invalid or missing confirmation token.");
          await this.userService.create({ email, token });
          this.$alert.success("Email has been confirmed");
          this.$router.push('/login');
        } catch (err) {
          this.$alert.error(error?.response?.data[0]?.description || "Failed to confirm");
        } finally {
          this.loading = false;
        }
      },
      retryConfirmation() {
        this.loading = true;
        this.error = false;
        this.errorMessage = "";
        this.confirmEmail();
      },
    },
  };
  </script>
  
  <style scoped>
  .email-confirmation {
    text-align: center;
    padding: 20px;
  }
  
  .loading {
    font-size: 1.2em;
    color: #007bff;
  }
  
  .success h1 {
    color: #28a745;
  }
  
  .error h1 {
    color: #dc3545;
  }
  
  .btn {
    margin-top: 20px;
    padding: 10px 20px;
    font-size: 1em;
    border: none;
    cursor: pointer;
  }
  
  .btn-primary {
    background-color: #007bff;
    color: white;
  }
  
  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }
  </style>
  