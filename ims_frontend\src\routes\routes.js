import DashboardLayout from 'src/Layout/DashboardLayout.vue'

import DepartmentModule from 'src/modules/admin/department/index.vue'

import UserModule from 'src/modules/admin/user/index.vue'

import CustomerModule from 'src/modules/admin/customer/index.vue'

import ServiceModule from 'src/modules/admin/service/index.vue'

import SlaModule from 'src/modules/admin/sla/index.vue'

import EscalationModule from 'src/modules/admin/escalation/index.vue'

import IssueModule from 'src/modules/admin/issue/index.vue'

import IssuelogsModule from 'src/modules/admin/issue/logs/index.vue'

import IssuetimelineModule from 'src/modules/admin/issue/logs/issue-timeline.vue'

import TimelineModule from 'src/modules/admin/issue/logs/timeline.vue'

import AssignmentModule from 'src/modules/admin/assignment/index.vue'

import ReportsModule from 'src/modules/reports/index.vue'

import InternalDashboardLayout from 'src/Layout/InternalDashboardLayout.vue'


import AuditDashboardLayout from 'src/Layout/AuditDashboardLayout.vue'

import CustomerUser from 'src/modules/customer/index.vue'

import CustomerDashboardLayout from 'src/Layout/CustomerDashboardLayout.vue'

import CustomerDepartmentModule from 'src/modules/customer/department/index.vue'

import CustomerUserModule from 'src/modules/customer/user/index.vue'

import CustomerCustomerModule from 'src/modules/customer/customer/index.vue'

import CustomerServiceModule from 'src/modules/customer/service/index.vue'

import CustomerSlaModule from 'src/modules/customer/sla/index.vue'

import CustomerIssueModule from 'src/modules/customer/issue/index.vue'

import CustomerIssuelogsModule from 'src/modules/customer/issue/logs/index.vue'

import CustomerIssuetimelineModule from 'src/modules/customer/issue/logs/issue-timeline.vue'

import CustomerTimelineModule from 'src/modules/customer/issue/logs/timeline.vue'

import CustomerLogTimelineModule from 'src/modules/customer/issue/logs/new-issue-log.vue'

import CustomerAssignmentModule from 'src/modules/customer/assignment/index.vue'

import CustomerViewIssueModule from 'src/modules/customer/issue/view-issue.vue'


import CustomerServiceDepartmentModule from 'src/modules/call-center/department/index.vue'

import FrontIssuesModule from 'src/modules/call-center/issue/front-issues.vue'

import CustomerServiceUserModule from 'src/modules/call-center/user/index.vue'

import CustomerServiceCustomerModule from 'src/modules/call-center/customer/index.vue'

import CustomerServiceServiceModule from 'src/modules/call-center/service/index.vue'

import CustomerServiceSlaModule from 'src/modules/call-center/sla/index.vue'

import CustomerServiceEscalationModule from 'src/modules/call-center/escalation/index.vue'

import CustomerServiceIssueModule from 'src/modules/call-center/issue/index.vue'

import CustomerServiceIssuelogsModule from 'src/modules/call-center/issue/logs/index.vue'

import CustomerServiceTimelineModule from 'src/modules/call-center/issue/logs/timeline.vue'

import CustomerServiceLogTimelineModule from 'src/modules/call-center/issue/logs/new-issue-log.vue'

import CustomerServiceAssignmentModule from 'src/modules/call-center/assignment/index.vue'

import CustomerServiceAssignmentEemployeeModule from 'src/modules/call-center/assignment/assign-employee.vue'

import CallCenterTransferModule from 'src/modules/call-center/assignment/transfer-issue.vue'

import CustomerServiceUser from 'src/modules/call-center/index.vue'

import CustomerServiceViewIssueModule from 'src/modules/call-center/issue/view-issue.vue'


import AdminDepartmentModule from 'src/modules/admin/department/index.vue'

import AdminUserModule from 'src/modules/admin/user/index.vue'

import AdminCustomerModule from 'src/modules/admin/customer/index.vue'

import AdminServiceModule from 'src/modules/admin/service/index.vue'

import AdminSlaModule from 'src/modules/admin/sla/index.vue'

import AdminIssueModule from 'src/modules/admin/issue/index.vue'

import AdminIssuelogsModule from 'src/modules/admin/issue/logs/index.vue'

import AdminIssuetimelineModule from 'src/modules/admin/issue/logs/issue-timeline.vue'

import AdminTimelineModule from 'src/modules/admin/issue/logs/timeline.vue'

import AdminLogTimelineModule from 'src/modules/admin/issue/logs/new-issue-log.vue'

import AdminAssignmentModule from 'src/modules/admin/assignment/index.vue'

import AdminUser from 'src/modules/admin/index.vue'

import AdminTransferModule from 'src/modules/admin/assignment/transfer-issue.vue'

import AdminViewIssueModule from 'src/modules/admin/issue/view-issue.vue'


import TechnicalDepartmentModule from 'src/modules/technical/department/index.vue'

import TechnicalUserModule from 'src/modules/technical/user/index.vue'

import TechnicalCustomerModule from 'src/modules/technical/customer/index.vue'

import TechnicalServiceModule from 'src/modules/technical/service/index.vue'

import TechnicalSlaModule from 'src/modules/technical/sla/index.vue'

import TechnicalEscalationModule from 'src/modules/technical/escalation/index.vue'

import TechnicalIssueModule from 'src/modules/technical/issue/index.vue'

import TechnicalIssuelogsModule from 'src/modules/technical/issue/logs/index.vue'

import TechnicalTimelineModule from 'src/modules/technical/issue/logs/timeline.vue'

import TechnicalLogTimelineModule from 'src/modules/technical/issue/logs/new-issue-log.vue'

import TechnicalAssignmentModule from 'src/modules/technical/assignment/index.vue'

import TechnicalTransferModule from 'src/modules/technical/assignment/transfer-issue.vue'

import TechnicalUser from 'src/modules/technical/index.vue'

import TechnicalViewIssueModule from 'src/modules/technical/issue/view-issue.vue'



import AuditDepartmentModule from 'src/modules/audit/department/index.vue'

import AuditUserModule from 'src/modules/audit/user/index.vue'

import AuditCustomerModule from 'src/modules/audit/customer/index.vue'

import AuditServiceModule from 'src/modules/audit/service/index.vue'

import AuditSlaModule from 'src/modules/audit/sla/index.vue'

import AuditIssueModule from 'src/modules/audit/issue/index.vue'

import AuditLogsModule from 'src/modules/audit/logs/index.vue'

import AuditLogsViewModule from 'src/modules/audit/logs/view-log.vue'

import AuditIssuelogsModule from 'src/modules/audit/issue/logs/index.vue'

import AuditTimelineModule from 'src/modules/audit/issue/logs/timeline.vue'

import AuditLogTimelineModule from 'src/modules/audit/issue/logs/new-issue-log.vue'

import AuditAssignmentModule from 'src/modules/audit/assignment/index.vue'

import AuditUser from 'src/modules/audit/index.vue'

import AuditTransferModule from 'src/modules/audit/assignment/transfer-issue.vue'

import AuditViewIssueModule from 'src/modules/audit/issue/view-issue.vue'




import ExecutiveDashboardLayout from 'src/Layout/ExecutiveDashboardLayout.vue'

import ExecutiveDepartmentModule from 'src/modules/executive/department/index.vue'

import ExecutiveUserModule from 'src/modules/executive/user/index.vue'

import ExecutiveCustomerModule from 'src/modules/executive/customer/index.vue'

import ExecutiveServiceModule from 'src/modules/executive/service/index.vue'

import ExecutiveSlaModule from 'src/modules/executive/sla/index.vue'

import ExecutiveEscalationModule from 'src/modules/executive/escalation/index.vue'

import ExecutiveIssueModule from 'src/modules/executive/issue/index.vue'

import ExecutiveTransferModule from 'src/modules/executive/assignment/transfer-issue.vue'


import ExecutiveLogsModule from 'src/modules/executive/logs/index.vue'

import ExecutiveLogsViewModule from 'src/modules/executive/logs/view-log.vue'

import ExecutiveIssuelogsModule from 'src/modules/executive/issue/logs/index.vue'

import ExecutiveTimelineModule from 'src/modules/executive/issue/logs/timeline.vue'

import ExecutiveLogTimelineModule from 'src/modules/executive/issue/logs/new-issue-log.vue'

import ExecutiveAssignmentModule from 'src/modules/executive/assignment/index.vue'

import ExecutiveUser from 'src/modules/executive/index.vue'

import ExecutiveViewIssueModule from 'src/modules/executive/issue/view-issue.vue'



import RoleSelection from 'src/RoleSelection.vue'

import Signup from 'src/Signup.vue'

import SubmitIssue from 'src/SubmitIssue.vue'

import Login from 'src/Login.vue'

import Forgot from 'src/ForgotPassword.vue'

import ResetPassword from 'src/ResetPassword.vue'

import ConfirmEmail from 'src/ConfirmEmail.vue'

import NotFound from './../NotFoundPage.vue'

import internalUser from '../roles/internal.vue'



// Dashboard pages
import Overview from 'src/pages/Dashboard/Dashboard/Overview.vue'
import Stats from 'src/pages/Dashboard/Dashboard/Stats.vue'

// Pages
import User from 'src/pages/Dashboard/Pages/UserProfile.vue'
import TimeLine from 'src/pages/Dashboard/Pages/TimeLinePage.vue'

import Register from 'src/pages/Dashboard/Pages/Register.vue'
import Lock from 'src/pages/Dashboard/Pages/Lock.vue'

// Components pages
import Buttons from 'src/pages/Dashboard/Components/Buttons.vue'
import GridSystem from 'src/pages/Dashboard/Components/GridSystem.vue'
import Panels from 'src/pages/Dashboard/Components/Panels.vue'
const SweetAlert = () => import('src/pages/Dashboard/Components/SweetAlert.vue')
import Notifications from 'src/pages/Dashboard/Components/Notifications.vue'
import Icons from 'src/pages/Dashboard/Components/Icons.vue'
import Typography from 'src/pages/Dashboard/Components/Typography.vue'
import { layout } from 'd3'



// Forms pages
const RegularForms = () => import('src/pages/Dashboard/Forms/RegularForms.vue')
const ExtendedForms = () => import('src/pages/Dashboard/Forms/ExtendedForms.vue')
const ValidationForms = () => import('src/pages/Dashboard/Forms/ValidationForms.vue')
const Wizard = () => import('src/pages/Dashboard/Forms/Wizard.vue')

// TableList pages
const RegularTables = () => import('src/pages/Dashboard/Tables/RegularTables.vue')
const ExtendedTables = () => import('src/pages/Dashboard/Tables/ExtendedTables.vue')
const PaginatedTables = () => import('src/pages/Dashboard/Tables/PaginatedTables.vue')
// Maps pages
const GoogleMaps = () => import('src/pages/Dashboard/Maps/GoogleMaps.vue')
const FullScreenMap = () => import('src/pages/Dashboard/Maps/FullScreenMap.vue')
const VectorMaps = () => import('src/pages/Dashboard/Maps/VectorMapsPage.vue')

// Calendar
const Calendar = () => import('src/pages/Dashboard/Calendar/CalendarRoute.vue')
// Charts
const Charts = () => import('src/pages/Dashboard/Charts.vue')

let componentsMenu = {
  path: '/components',
  component: DashboardLayout,
  redirect: '/components/buttons',
  children: [
    {
      path: 'buttons',
      name: 'Buttons',
      component: Buttons
    },
    {
      path: 'grid-system',
      name: 'Grid System',
      component: GridSystem
    },
    {
      path: 'panels',
      name: 'Panels',
      component: Panels
    },
    {
      path: 'sweet-alert',
      name: 'Sweet Alert',
      component: SweetAlert
    },
    {
      path: 'notifications',
      name: 'Notifications',
      component: Notifications
    },
    {
      path: 'icons',
      name: 'Icons',
      component: Icons
    },
    {
      path: 'typography',
      name: 'Typography',
      component: Typography
    }

  ]
}


let formsMenu = {
  path: '/forms',
  component: DashboardLayout,
  redirect: '/forms/regular',
  children: [
    {
      path: 'regular',
      name: 'Regular Forms',
      component: RegularForms
    },
    {
      path: 'extended',
      name: 'Extended Forms',
      component: ExtendedForms
    },
    {
      path: 'validation',
      name: 'Validation Forms',
      component: ValidationForms
    },
    {
      path: 'wizard',
      name: 'Wizard',
      component: Wizard
    }
  ]
}

let tablesMenu = {
  path: '/table-list',
  component: DashboardLayout,
  redirect: '/table-list/regular',
  children: [
    {
      path: 'regular',
      name: 'Regular Tables',
      component: RegularTables
    },
    {
      path: 'extended',
      name: 'Extended Tables',
      component: ExtendedTables
    },
    {
      path: 'paginated',
      name: 'Paginated Tables',
      component: PaginatedTables
    }]
}

let mapsMenu = {
  path: '/maps',
  component: DashboardLayout,
  redirect: '/maps/google',
  children: [
    {
      path: 'google',
      name: 'Google Maps',
      component: GoogleMaps
    },
    {
      path: 'full-screen',
      name: 'Full Screen Map',
      component: FullScreenMap
    },
    {
      path: 'vector-map',
      name: 'Vector Map',
      component: VectorMaps
    }
  ]
}

let pagesMenu = {
  path: '/pages',
  component: DashboardLayout,
  redirect: '/pages/user',
  children: [
    {
      path: 'user',
      name: 'User Page',
      component: User
    },
    {
      path: 'timeline',
      name: 'Timeline Page',
      component: TimeLine
    }
  ]
}

let loginPage = {
  path: '/login',
  name: 'Login',
  component: Login
  , meta: { public: true }
}

let SignupPage = {
  path: '/signup',
  name: 'Signup',
  component: Signup
  , meta: { public: true }
}

let RoleSelectionPage = {
  path: '/role-selection',
  name: 'RoleSelection',
  component: RoleSelection
}

let SubmitIssuePage = {
  path: '/submitissue',
  name: 'SubmitIssue',
  component: SubmitIssue
  , meta: { public: true }
}



let ConfirmEmailPage = {
  path: '/confirmemail',
  name: 'ConfirmEmail',
  component: ConfirmEmail
  , meta: { public: true }
}

let ForgotPasswordPage = {
  path: '/forgotpassword',
  name: 'Forgotpassword',
  component: Forgot
  , meta: { public: true }
}

let ResetPasswordPage = {
  path: '/resetpassword',
  name: 'Resetpassword',
  component: ResetPassword
  , meta: { public: true }
}



let registerPage = {
  path: '/register',
  name: 'Register',
  component: Register
  , meta: { public: true }
}

let lockPage = {
  path: '/lock',
  name: 'Lock',
  component: Lock
}


const routes = [
  {
    path: '/',
    redirect: loginPage
  },
  componentsMenu,
  formsMenu,
  tablesMenu,
  mapsMenu,
  pagesMenu,
  loginPage,
  SignupPage,
  RoleSelectionPage,
  ForgotPasswordPage,
  ResetPasswordPage,
  ConfirmEmailPage,
  SubmitIssuePage,
  registerPage,
  lockPage,
  {
    path: '/admin',
    component: DashboardLayout,
    children: [
      {
        path: 'overview',
        name: 'Overview',
        component: Overview
      },
      {
        path: 'departments',
        component: DepartmentModule,
      },
      {
        path: 'users',
        component: UserModule,
      },
      {
        path: 'customers',
        component: CustomerModule,
      },
      {
        path: 'services',
        component: ServiceModule,
      },
      {
        path: 'slas',
        component: SlaModule,
      }, {
        path: 'issues',
        component: IssueModule,
      }, {
        path: 'issuelogs',
        component: IssuelogsModule,
      }, {
        path: 'assignments',
        component: AssignmentModule,
      }, {
        path: 'timeline',
        component: TimelineModule,
      }, {
        path: 'issue-timeline/:id',
        component: IssuetimelineModule,
      },

      {
        path: 'stats',
        name: 'Stats',
        component: Stats
      },
      {
        path: 'calendar',
        name: 'Calendar',
        component: Calendar
      },
      {
        path: 'charts',
        name: 'Charts',
        component: Charts
      }
    ]
  },
  {
    path: '/customer',
    component: CustomerDashboardLayout,
    children: [
      {
        path: '',
        component: CustomerUser,
      },
      {
        path: 'dashboard',
        component: CustomerUser,
      },
      {
        path: 'issue-view/:id',
        component: CustomerViewIssueModule ,
      },
      {
        path: 'departments',
        component: CustomerDepartmentModule,
      },
      {
        path: 'users',
        component: CustomerUserModule,
      },
      {
        path: 'customers',
        component: CustomerCustomerModule,
      },
      {
        path: 'services',
        component: CustomerServiceModule,
      },
      {
        path: 'slas',
        component: CustomerSlaModule,
      }, {
        path: 'issues',
        component: CustomerIssueModule,
      }, {
        path: 'issuelogs',
        component: CustomerIssuelogsModule,
      }, {
        path: 'assignments',
        component: CustomerAssignmentModule,
      }, {
        path: 'assignments/:id',
        component: CustomerAssignmentModule,
      }, {
        path: 'timeline',
        component: CustomerTimelineModule,
      }, {
        path: 'issue-timeline/:id',
        component: CustomerIssuetimelineModule,
      }, {
        path: 'issue-logs/:id',
        component: CustomerLogTimelineModule,
      }, {
        path: 'reports',
        component: ReportsModule,
      },
    ]
  },
  {
    path: '/call-center',
    component: InternalDashboardLayout,
    children: [
      {
        path: '',
        component: CustomerServiceUser,
      },
      {
        path: 'dashboard',
        component: CustomerServiceUser,
      },
      {
        path: 'departments',
        component: CustomerServiceDepartmentModule,
      },
      {
        path: 'users',
        component: CustomerServiceUserModule,
      },
      {
        path: 'customers',
        component: CustomerServiceCustomerModule,
      },
      {
        path: 'services',
        component: CustomerServiceServiceModule,
      },
      {
        path: 'slas',
        component: CustomerServiceSlaModule,
      }, {
        path: 'escalations',
        component: CustomerServiceEscalationModule,
      }, {
        path: 'issues',
        component: CustomerServiceIssueModule,
      }, {
        path: 'front-issues',
        component: FrontIssuesModule,
      }, {
        path: 'issuelogs',
        component: CustomerServiceIssuelogsModule,
      }, {
        path: 'assignments',
        component: CustomerServiceAssignmentModule,
      },
      {
        path: 'assign-employee/:id',
        component: CustomerServiceAssignmentEemployeeModule,
      },
      {
        path: 'issue-view/:id',
        component: CustomerServiceViewIssueModule,
      },
      {
        path: 'assignments/:id',
        component: CustomerServiceAssignmentModule,
      },
      {
        path: 'transfer-issue/:id',
        component: CallCenterTransferModule,
      },
      {
        path: 'timeline',
        component: CustomerServiceTimelineModule,
      }, {
        path: 'issue-logs/:id',
        component: CustomerServiceLogTimelineModule,
      }, {
        path: 'reports',
        component: ReportsModule,
      },
    ]
  },

  {
    path: '/technical',
    component: InternalDashboardLayout,
    children: [
      {
        path: '',
        component: TechnicalUser,
      },
      {
        path: 'dashboard',
        component: TechnicalUser,
      },
      {
        path: 'departments',
        component: TechnicalDepartmentModule,
      },
      {
        path: 'users',
        component: TechnicalUserModule,
      },
      {
        path: 'customers',
        component: TechnicalCustomerModule,
      },
      {
        path: 'services',
        component: TechnicalServiceModule,
      },
      {
        path: 'slas',
        component: TechnicalSlaModule,
      }, {
        path: 'escalations',
        component: TechnicalEscalationModule,
      }, {
        path: 'issues',
        component: TechnicalIssueModule,
      }, {
        path: 'front-issues',
        component: FrontIssuesModule,
      }, {
        path: 'issuelogs',
        component: TechnicalIssuelogsModule,
      }, {
        path: 'assignments',
        component: TechnicalAssignmentModule,
      }, {
        path: 'reports',
        component: ReportsModule,
      }, {
        path: 'transfer-issue/:id',
        component: TechnicalTransferModule,
      },
      {
        path: 'issue-view/:id',
        component: TechnicalViewIssueModule,
      },
      {
        path: 'assignments/:id',
        component: TechnicalAssignmentModule,
      }, {
        path: 'timeline',
        component: TechnicalTimelineModule,
      }, {
        path: 'issue-logs/:id',
        component: TechnicalLogTimelineModule,
      },
    ]
  },
  {
    path: '/audit',
    component:AuditDashboardLayout ,
    children: [
      {
        path: '',
        component: AuditUser,
      },
      {
        path: 'dashboard',
        component: AuditUser,
      },
      {
        path: 'departments',
        component: AuditDepartmentModule,
      },
      {
        path: 'users',
        component: AuditUserModule,
      },
      {
        path: 'customers',
        component: AuditCustomerModule,
      },
      {
        path: 'services',
        component: AuditServiceModule,
      },
      {
        path: 'slas',
        component: AuditSlaModule,
      }, {
        path: 'issues',
        component: AuditIssueModule,
      }, {
        path: 'logs',
        component: AuditLogsModule,
      },
      {
        path: 'view-log/:id',
        component: AuditLogsViewModule,
      },

      {
        path: 'front-issues',
        component: FrontIssuesModule,
      }, {
        path: 'issuelogs',
        component: AuditIssuelogsModule,
      }, {
        path: 'assignments',
        component: AuditAssignmentModule,
      },
      {
        path: 'issue-view/:id',
        component: AuditViewIssueModule,
      },
      {
        path: 'assignments/:id',
        component: AuditAssignmentModule,
      }, {
        path: 'timeline',
        component: AuditTimelineModule,
      }, {
        path: 'issue-logs/:id',
        component: AuditLogTimelineModule,
      }, {
        path: 'reports',
        component: ReportsModule,
      },
    ]
  },
  {
    path: '/executive',
    component:ExecutiveDashboardLayout ,
    children: [
      {
        path: '',
        component: ExecutiveUser,
      },
      {
        path: 'dashboard',
        component: ExecutiveUser,
      },
      {
        path: 'departments',
        component: ExecutiveDepartmentModule,
      },
      {
        path: 'transfer-issue/:id',
        component: ExecutiveTransferModule,
      },
      {
        path: 'users',
        component: ExecutiveUserModule,
      },
      {
        path: 'customers',
        component: ExecutiveCustomerModule,
      },
      {
        path: 'services',
        component: ExecutiveServiceModule,
      },
      {
        path: 'slas',
        component: ExecutiveSlaModule,
      }, {
        path: 'escalations',
        component: ExecutiveEscalationModule,
      }, {
        path: 'issues',
        component: ExecutiveIssueModule,
      }, {
        path: 'logs',
        component: ExecutiveLogsModule,
      },
      {
        path: 'view-log/:id',
        component: ExecutiveLogsViewModule,
      },

      {
        path: 'front-issues',
        component: FrontIssuesModule,
      }, {
        path: 'issuelogs',
        component: ExecutiveIssuelogsModule,
      }, {
        path: 'assignments',
        component: ExecutiveAssignmentModule,
      },
      {
        path: 'issue-view/:id',
        component: ExecutiveViewIssueModule,
      },
      {
        path: 'assignments/:id',
        component: ExecutiveAssignmentModule,
      }, {
        path: 'timeline',
        component: ExecutiveTimelineModule,
      }, {
        path: 'issue-logs/:id',
        component: ExecutiveLogTimelineModule,
      }, {
        path: 'reports',
        component: ReportsModule,
      },
    ]
  },
  {
    path: '/admin',
    component: InternalDashboardLayout,
    children: [
      {
        path: '',
        component: AdminUser,
      },
      {
        path: 'dashboard',
        component: AdminUser,
      },
      {
        path: 'departments',
        component: AdminDepartmentModule,
      },
      {
        path: 'transfer-issue/:id',
        component: AdminTransferModule,
      },
      {
        path: 'users',
        component: AdminUserModule,
      },
      {
        path: 'customers',
        component: AdminCustomerModule,
      },
      {
        path: 'services',
        component: AdminServiceModule,
      },
      {
        path: 'slas',
        component: AdminSlaModule,
      }, {
        path: 'escalations',
        component: EscalationModule,
      }, {
        path: 'issues',
        component: AdminIssueModule,
      }, {
        path: 'front-issues',
        component: FrontIssuesModule,
      }, {
        path: 'issuelogs',
        component: AdminIssuelogsModule,
      }, {
        path: 'assignments',
        component: AdminAssignmentModule,
      },

      {
        path: 'issue-view/:id',
        component: AdminViewIssueModule,
      },
      {
        path: 'assignments/:id',
        component: AdminAssignmentModule,
      }, {
        path: 'timeline',
        component: AdminTimelineModule,
      }, {
        path: 'issue-timeline/:id',
        component: AdminIssuetimelineModule,
      }, {
        path: 'issue-logs/:id',
        component: AdminLogTimelineModule,
      }, {
        path: 'reports',
        component: ReportsModule,
      },
    ]
  },
  { path: '*', component: NotFound }
]


export default routes
