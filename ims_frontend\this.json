{"account_info": {"cust_bill_suspension_delayed": 0, "balance": 0.0, "did_number": "************", "i_role": 6, "product_name": "SIP Trunk postpaid local voice", "i_customer": 2008, "out_date_time_format": "YYYY-MM-DD HH24:MI:SS", "in_time_format": "HH24:MI:SS", "i_acl": 155, "assigned_addons": [], "customer_name": "3G Consulting (Testing Account)", "product_visible_name": "SIP Trunk postpaid local voice", "opening_balance": 0.0, "i_time_zone": 226, "credit_limit": null, "activation_date": "2024-04-25", "iso_4217": "MWK", "i_account_role": 1, "out_date_format": "YYYY-MM-DD", "customer_blocked": "N", "blocked": "N", "billing_model": 2, "in_date_format": "YYYY-MM-DD", "expiration_date": null, "i_lang": "en", "is_active": 1, "i_product": 183, "has_custom_fields": 1, "inactivity_expire_time": null, "ecommerce_enabled": "N", "service_flags": "", "out_time_format": "HH24:MI:SS", "i_did_number": 322306, "service_features": [], "customer_bill_suspended": 0, "i_master_account": 2165673, "issue_date": "2024-04-25", "time_zone_name": "Africa/Blantyre", "life_time": null, "i_account": 2165674, "bill_status": "O", "customer_bill_status": "O", "id": "************"}, "customer_info": {"credit_exceed": 0, "balance_transfer_allowed": "N", "i_ui_time_zone": 226, "i_customer": 2008, "phone2": "", "batch_balance_update": "N", "balance": 0.0, "baddr1": "", "opening_balance": 0.0, "deactivate_on": null, "cont1": "<PERSON><PERSON><PERSON>", "note": "", "login": "3Gconsulting", "invoice_generation_enabled": 1, "suspension_delay_date": null, "i_balance_control_type": 1, "has_custom_fields": 1, "i_lang": "en", "estimate_taxes": 0, "i_template": null, "out_date_format": "YYYY-MM-DD", "credit_limit": 10000.0, "iso_4217": "MWK", "override_tariffs_enabled": "N", "address_line_2": "", "bill_status": "O", "out_time_format": "HH24:MI:SS", "i_role": 5, "name": "3G Consulting (Testing Account)", "i_billing_period": 4, "i_customer_type": 1, "bill_suspension_delayed": 0, "midinit": "", "cont2": "", "creation_date": "2024-04-25 09:58:22", "city": "", "i_time_zone": 226, "i_acl": 136, "in_time_format": "HH24:MI:SS", "out_date_time_format": "YYYY-MM-DD HH24:MI:SS", "i_office_type": 1, "email": "<EMAIL>", "perm_credit_limit": 10000.0, "billed_to": "2025-04-30 22:00:00", "firstname": "<PERSON><PERSON><PERSON>", "generate_invoice_earlier": "N", "restore_on": null, "i_customer_class": 6, "blocked": "N", "salutation": "", "state": "", "password": "95/w.l<u", "callshop_enabled": "N", "in_date_format": "YYYY-MM-DD", "i_commission_plan": null, "inclusive_taxation": "N", "billing_lock": "N", "phone1": "", "lastname": "Govender", "faxnum": "", "unallocated_payments": 0.0, "suspend_on_insuff_funds": null, "country": "", "service_features": [{"effective_flag_value": "N", "flag_value": "N", "name": "bundle_discount", "attributes": [{"values": [null], "name": "i_bd_plan", "effective_values": [null]}]}, {"effective_flag_value": "", "flag_value": "~", "name": "call_barring", "attributes": [{"values": ["1001", "1002", "1008"], "effective_values": ["1001", "1002", "1008"], "name": "call_barring_rules"}]}, {"effective_flag_value": "N", "attributes": [], "name": "call_center_activity", "flag_value": "N"}, {"effective_flag_value": "N", "attributes": [{"name": "ringback_tone", "effective_values": ["0"], "values": ["0"]}, {"name": "retrieval_timeout", "effective_values": ["0"], "values": ["0"]}, {"values": ["*70"], "effective_values": ["*70"], "name": "park_prefix"}, {"effective_values": ["*71"], "name": "release_prefix", "values": ["*71"]}], "flag_value": "N", "name": "call_parking"}, {"attributes": [{"values": ["*90"], "effective_values": ["*90"], "name": "spy"}, {"values": ["0"], "name": "spy_dtmf", "effective_values": ["0"]}, {"effective_values": ["*91"], "name": "whisper", "values": ["*91"]}, {"effective_values": ["1"], "name": "whisper_dtmf", "values": ["1"]}, {"values": ["*92"], "effective_values": ["*92"], "name": "barge_in"}, {"values": ["2"], "name": "barge_in_dtmf", "effective_values": ["2"]}], "flag_value": "N", "name": "call_supervision", "effective_flag_value": "N"}, {"attributes": [{"values": [null], "effective_values": [null], "name": "account_group"}, {"name": "centrex", "effective_values": [null], "values": [null]}, {"values": ["N"], "name": "display_name_override", "effective_values": ["N"]}, {"values": [null], "name": "display_number", "effective_values": [null]}, {"name": "display_number_check", "effective_values": ["Y"], "values": ["Y"]}, {"values": ["A"], "effective_values": ["A"], "name": "attest"}], "flag_value": "N", "name": "cli", "effective_flag_value": "N"}, {"effective_flag_value": "N", "name": "cli_trust", "flag_value": "N", "attributes": [{"values": ["N"], "name": "accept_caller", "effective_values": ["N"]}, {"effective_values": ["N"], "name": "supply_caller", "values": ["N"]}]}, {"attributes": [], "flag_value": "N", "name": "distinctive_ring_vpn", "effective_flag_value": "N"}, {"effective_flag_value": "N", "flag_value": "N", "name": "endpoint_redirect", "attributes": []}, {"effective_flag_value": "N", "name": "first_login_greeting", "flag_value": "N", "attributes": []}, {"effective_flag_value": "Y", "name": "group_pickup", "flag_value": "Y", "attributes": [{"values": [null], "name": "group_pickup_note", "effective_values": [null]}]}, {"effective_flag_value": "N", "flag_value": "N", "name": "legal_intercept", "attributes": []}, {"effective_flag_value": "Y", "attributes": [{"effective_values": ["1"], "name": "i_moh", "values": ["1"]}], "name": "music_on_hold", "flag_value": "Y"}, {"flag_value": "N", "name": "paging", "attributes": [{"values": ["*33"], "effective_values": ["*33"], "name": "paging_prefix"}], "effective_flag_value": "N"}, {"name": "permitted_sip_proxies", "flag_value": "N", "attributes": [{"effective_values": [], "name": "proxies", "values": []}], "effective_flag_value": "N"}, {"effective_flag_value": "N", "attributes": [], "flag_value": "N", "name": "rtpp_level"}, {"effective_flag_value": "N", "flag_value": "N", "name": "sim_calls_limit", "attributes": [{"values": [null], "name": "i_network_connectivity", "effective_values": [null]}, {"effective_values": [null], "name": "max_bandwidth_in", "values": [null]}, {"values": ["Y"], "effective_values": ["Y"], "name": "restrict_on_net"}, {"effective_values": [null], "name": "max_bandwidth_out", "values": [null]}, {"effective_values": [null], "name": "max_calls_out", "values": [null]}, {"values": [null], "effective_values": [null], "name": "max_calls_fwd"}, {"name": "max_calls", "effective_values": [null], "values": [null]}, {"name": "max_bandwidth", "effective_values": [null], "values": [null]}, {"effective_values": [null], "name": "max_calls_in", "values": [null]}]}, {"attributes": [{"name": "host", "effective_values": [null], "values": [null]}, {"values": ["N"], "name": "use_tcp", "effective_values": ["N"]}, {"values": [null], "name": "port", "effective_values": [null]}, {"effective_values": [null], "name": "user", "values": [null]}], "flag_value": "N", "name": "sip_static_contact", "effective_flag_value": "N"}, {"flag_value": "N", "name": "voice_dialing", "attributes": [{"name": "translate_cli_out", "effective_values": ["N"], "values": ["N"]}, {"effective_values": ["N"], "name": "translate_cli_in", "values": ["N"]}, {"effective_values": [null], "name": "i_dial_rule", "values": [null]}, {"values": ["N"], "effective_values": ["N"], "name": "translate_cld_in"}], "effective_flag_value": "N"}, {"effective_flag_value": "N", "name": "voice_location", "flag_value": "N", "attributes": [{"values": [null], "effective_values": [null], "name": "primary_location"}, {"values": [null], "name": "allow_roaming", "effective_values": [null]}, {"effective_values": [null], "name": "primary_location_data", "values": [null]}]}, {"effective_flag_value": "N", "attributes": [{"effective_values": [null], "name": "i_vq_profile", "values": [null]}], "flag_value": "N", "name": "voice_quality"}], "next_billed_to": "2025-05-31 22:00:00", "terminate_on": null, "zip": "", "is_used": 1}}