<template>
  <div class="issues-management">
    <!-- Issue Form -->
    <card v-if="showIssueForm" class="mt-0">
      <template slot="header">
        <h4 class="card-title">
          {{
            isEditing
              ? `Edit Issue (${currentIssue.issueRef}) [ source: ${currentIssue.source} ]`
              : "Add Issue"
          }}
        </h4>
      </template>
      <el-dialog
        title="Create Issue"
        :visible.sync="showIssueForm"
        width="80%"
        append-to-body="true"
        top="10px"
        
        draggable
        @close="resetCurrentIssue"
      >
        <form @submit.prevent="submitIssue" :model="currentIssue">
          <div class="row">
            <div class="col-md-4">
              <fg-input label="Issue Type">
                <el-select
                  v-model="currentIssue.issueType"
                  placeholder="Select"
                  @change="setDetails"
                >
                  <el-option
                    key="customer"
                    label="Customer related"
                    value="customer"
                  />

                  <el-option
                    key="noncustomer"
                    label="Non customer related"
                    value="noncustomer"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-3">
              <fg-input label="Search customer by">
                <el-select
                  v-model="searchBy"
                  placeholder="Select"
                  filterable
                  @change="onCustomerSelect"
                >
                  <el-option
                    key="customerid"
                    label="Customer Id"
                    value="customerid"
                  />
                  <el-option key="account" label="Account" value="account" />
                  <!-- Static options -->
                  <el-option key="name" label="Name" value="name" />
                  <el-option key="email" label="Email" value="email" />
                  <el-option key="phone" label="Phone" value="phone" />
                  <el-option
                    key="companyname"
                    label="Company"
                    value="companyname"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="searchBy">
                <el-input
                  v-model="searchInput"
                  :placeholder="'Enter ' + searchBy"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-2">
              <button
                class="btn btn-fill btn-warning btn-round btn-wd mb-4"
                @click="searchCustomer"
                :disabled="btnloading"
              >
                <span v-if="!btnloading">Search</span>
                <span v-else>
                  <i class="fas fa-spinner fa-spin"></i> Searching...
                </span>
              </button>
            </div>
          </div>

          <!-- Dropdown for search results (when searching by name) -->

          <div
            class="row"
            v-if="
              searchResults.length > 0 && currentIssue.issueType == 'customer'
            "
          >
            <div class="col-md-6">
              <fg-input label="Select Customer">
                <el-select
                  v-model="selectedCustomer"
                  placeholder="Select customer"
                  filterable
                  class="w-100"
                  @change="onCustomerSelect"
                >
                  <el-option
                    v-for="customer in searchResults"
                    :key="customer.i_customer"
                    :label="`${customer.name} (${customer.email})`"
                    :value="customer.i_customer"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div
            class="row"
            v-if="accounts.length > 0 && currentIssue.issueType == 'customer'"
          >
            <div class="col-md-6">
              <fg-input label="Select Account">
                <el-select
                  v-model="currentIssue.account"
                  placeholder="Select account"
                  filterable
                  class="w-100"
                >
                  <el-option
                    v-for="account in accounts"
                    :key="account.i_account"
                    :label="`${account.product_visible_name} (${account.i_account})`"
                    :value="account.i_account"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-4">
              <fg-input :label="`Customer ID`">
                <el-input
                  v-model="currentIssue.customerId"
                  placeholder="Select customer"
                  filterable
                  class="w-100"
                  readonly
                  disabled
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="`Customer`">
                <el-input
                  v-model="currentIssue.customer"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="`Company`">
                <el-input
                  v-model="currentIssue.company"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="Subject" required>
                <input
                  v-model="currentIssue.title"
                  type="text"
                  class="form-control"
                  placeholder="Enter issue title"
                  required
                />
              </fg-input>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-6">
              <fg-input label="Service">
                <el-select
                  v-model="currentIssue.serviceId"
                  placeholder="Select service"
                  filterable
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="service in services"
                    :key="service.i_service_type"
                    :label="`${service.name} - ${service.i_service_type}`"
                    :value="service.i_service_type"
                  />
                </el-select>
              </fg-input>
            </div>

            <div class="col-md-6">
              <fg-input label="Category">
                <el-select
                  v-model="currentIssue.category"
                  placeholder="Select category"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="cat in issueCategories"
                    :key="cat"
                    :label="cat"
                    :value="cat"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-6">
              <fg-input label="Priority">
                <el-select
                  v-model="currentIssue.priority"
                  placeholder="Select priority"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="priority in issuePriorities"
                    :key="priority"
                    :label="priority"
                    :value="priority"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Status">
                <el-select
                  v-model="currentIssue.status"
                  placeholder="Select status"
                  class="w-100"
                  :disabled="true"
                >
                  <el-option
                    v-for="status in issueStatuses"
                    :key="status"
                    :label="status"
                    :value="status"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="Phone" required>
                <input
                  v-model="currentIssue.phone"
                  type="tel"
                  class="form-control"
                  placeholder="Enter phone"
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Email">
                <input
                  v-model="currentIssue.email"
                  type="email"
                  :readonly="currentIssue.issueType == 'noncustomer'"
                  :disabled="currentIssue.issueType == 'noncustomer'"
                  class="form-control"
                  placeholder="Enter email"
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="District" required>
                <el-select
                  v-model="currentIssue.district"
                  placeholder="Select district"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="district in malawiDistricts"
                    :key="district"
                    :label="district"
                    :value="district"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Location" required>
                <input
                  v-model="currentIssue.location"
                  type="text"
                  class="form-control"
                  placeholder="Enter location"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <fg-input label="Description">
                <textarea
                  v-model="currentIssue.description"
                  class="form-control"
                  rows="4"
                  placeholder="Enter issue description"
                ></textarea>
              </fg-input>
            </div>
          </div>

          <!-- Department Assignment -->
          <div class="row">
            <div class="col-md-6">
              <fg-input label="Assign Department">
                <el-select
                  v-model="currentIssue.assignedDepartmentId"
                  placeholder="Select department"
                  class="w-100"
                >
                  <el-option
                    v-for="department in departments"
                    :key="department.departmentId"
                    :label="department.name"
                    :value="department.departmentId"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <button 
                type="submit" 
                class="btn btn-fill btn-info"
                :disabled="isSaving"
              >
                <span v-if="!isSaving">
                  {{ isEditing ? "Update Issue" : "Save Issue" }}
                </span>
                <span v-else>
                  <i class="fas fa-spinner fa-spin"></i> {{ isEditing ? "Updating..." : "Saving..." }}
                </span>
              </button>
              <button
                type="button"
                class="btn btn-fill btn-secondary ml-2"
                @click="closeIssueForm"
                :disabled="isSaving"
              >
                Close
              </button>
            </div>
          </div>
        </form>
      </el-dialog>
    </card>

    <!-- Issues List -->
    <card>
      <template slot="header">
        <div class="col-sm-12 d-flex align-items-center row">
          <div class="col-4"><h4 class="card-title">Issues List</h4></div>
          <div class="col-8 text-right">
            <button class="btn btn-primary mx-2" @click="showIssueForm = true">
              Add Issue
            </button>
            <router-link
              to="/technical/timeline"
              class="btn btn-success ml-2"
            >
              Issues Timeline
            </router-link>
          </div>
        </div>
      </template>

      <div>
        <div class="row col-sm-12">
          <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search issues"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>
        </div>
        <!-- Add this before your table -->
        <div class="row mb-3 col-sm-12">
          <!-- Department Filter -->
          <div class="col-md-3">
            <label>Department</label>
            <div class="d-flex">
              <el-select 
                v-model="filters.departmentId" 
                :placeholder="defaultDepartmentName"
                class="w-100"
              >
                <el-option
                  key="all"
                  label="All Departments"
                  :value="null"
                />
                <el-option
                  v-for="dept in filteredDepartments"
                  :key="dept.departmentId"
                  :label="dept.name"
                  :value="dept.departmentId"
                  :selected="dept.departmentId === loggedInUser?.departmentId"
                />
              </el-select>
              <button 
                v-if="filters.departmentId" 
                class="btn btn-link text-danger ml-2"
                @click="resetDepartmentFilter"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Status Filter -->
          <div class="col-md-2">
            <label>Status</label>
            <div class="d-flex">
              <el-select 
                v-model="filters.status" 
                placeholder="All Statuses"
                class="w-100"
              >
                <el-option
                  key="all"
                  label="All Statuses"
                  :value="null"
                />
                <el-option
                  v-for="status in issueStatuses"
                  :key="status"
                  :label="status"
                  :value="status"
                />
              </el-select>
              <button 
                v-if="filters.status" 
                class="btn btn-link text-danger ml-2"
                @click="filters.status = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Customer Filter -->
          <div class="col-md-3">
            <label>Customer</label>
            <div class="d-flex">
              <el-select
                v-model="filters.customerId"
                placeholder="All Customers"
                filterable
                class="w-100"
              >
                <el-option
                  key="all"
                  label="All Customers"
                  :value="null"
                />
                <el-option
                  v-for="customer in filteredCustomers"
                  :key="customer.i_customer"
                  :label="customer.name"
                  :value="customer.i_customer"
                />
              </el-select>
              <button 
                v-if="filters.customerId" 
                class="btn btn-link text-danger ml-2"
                @click="filters.customerId = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Date Range Filter -->
          <div class="col-md-4">
            <label>Date Range</label>
            <div class="d-flex">
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="-"
                start-placeholder="All Dates"
                end-placeholder="End date"
                class="w-100"
              />
              <button 
                v-if="filters.dateRange" 
                class="btn btn-link text-danger ml-2"
                @click="filters.dateRange = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Add Reset All Filters button -->
        <div class="row mb-3 col-sm-12" v-if="hasActiveFilters">
          <div class="col-12">
            <button 
              class="btn btn-secondary btn-sm"
              @click="resetFilters"
            >
              Reset All Filters
            </button>
          </div>
        </div>

        <div class="col-sm-12">
          <el-table
            :data="queriedData"
            @row-click="handleRowClick"
            stripe
            border
            style="width: 100%"
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            ></el-table-column>

            <!-- Assignment Column -->
            <el-table-column label="Assigned To" min-width="150">
              <template v-slot="{ row }">
                <div v-if="getAssignment(row.issueId)">
                  {{ formatAssignee(getAssignment(row.issueId)) }}
                </div>
                <router-link
                  v-if="!getAssignment(row.issueId)"
                  class="btn btn-warning btn-sm"
                  :to="`/technical/assignments/${row.issueId}`"
                >
                  Assign Issue
                </router-link>
              </template>
            </el-table-column>

            <!-- Actions Column -->
            <el-table-column label="Actions" width="90">
              <template v-slot="{ row }">            
                <router-link
                  class="btn btn-info btn-sm mx-1"
                  :to="`/technical/issue-view/${row.issueId}`"
                >
                  <i class="fa fa-eye"></i>
                </router-link>
              </template>
            </el-table-column>
          </el-table>

        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        />
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import {
  Select,
  Option,
  Button,
  Table,
  TableColumn,
  Dialog,
  Form,
  FormItem,
  Input,
  DatePicker
} from "element-ui";
import API from "@/services/api";

export default {
  name: "IssuesManagement",
  components: {
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    ElDialog: Dialog,
    ElSelect: Select,
    ElOption: Option,
    'el-date-picker': DatePicker
  },
  data() {
    return {
      // Services for APIs
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      teamMemberService: new API(process.env.VUE_APP_API_URL, "users"),
      reportService: new API(process.env.VUE_APP_API_URL, "report"),
      validateUserService: new API(
        process.env.VUE_APP_API_URL,
        "auth/validate-customer"
      ),
      searchBy: "name",
      selectedCustomer: null,
      searchByValueCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/search"
      ),
      searchByIdCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers"
      ),
      searchByAccountsByCustomerIdService: new API(
        process.env.VUE_APP_API_URL,
        "accounts"
      ),
      searchByAccountCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/account"
      ),
      btnloading: false,
      searchResults: [],
      searchQuery: "",
      newFaults: 0,
      pagination: {
        perPage: 20,
        currentPage: 1,
        perPageOptions: [20, 40, 60],
        total: 0,
      },
      loading: false,
      // Status Filter
      selectedStatus: "New", // Default to show only "New" issues
      issueStatuses: ["New", "In Progress", "Resolved", "On Hold", "Closed"], // All statuses
      visibleStatuses: ["New", "In Progress", "Unassigned"], // Statuses visible in the dropdown by default

      // Table Columns
      tableColumns: [
        { prop: "issueRef", label: "Issue ID", minWidth: 120, sortable: true },
        { prop: "title", label: "Subject", minWidth: 200 },
        { prop: "customer", label: "Customer", minWidth: 140 },
        { prop: "status", label: "Status", minWidth: 100, sortable: true },
        { prop: "phone", label: "Phone", minWidth: 150 },
      ],

      // Dropdown Options
      issueCategories: [
        "Service Requests",
        "Incidents",
        "Problems",
        "Changes",
        "Billing Issues",
        "technical Support",
        "Customer Feedback",
        "Account Management",
      ],
      issuePriorities: ["Lowest", "Low", "Medium", "High", "Highest"],
      // Data Arrays
      issues: [],
      customers: [],
      accounts: [],
      services: [],
      departments: [],
      assignments: [],
      searchInput: "",
      // Current Issue for Form
      currentIssue: {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "",
        priority: "Medium",
        status: "New",
        district: "",
        assignedDepartmentId: null,
        issueType: "customer",
      },

      // Form Control
      showIssueForm: false,
      isEditing: false,

      // Malawi Districts
      malawiDistricts: [
        "Balaka",
        "Blantyre",
        "Chikwawa",
        "Chiradzulu",
        "Chitipa",
        "Dedza",
        "Dowa",
        "Karonga",
        "Kasungu",
        "Likoma",
        "Lilongwe",
        "Machinga",
        "Mangochi",
        "Mchinji",
        "Mulanje",
        "Mwanza",
        "Mzimba",
        "Neno",
        "Nkhata Bay",
        "Nkhotakota",
        "Nsanje",
        "Ntcheu",
        "Ntchisi",
        "Phalombe",
        "Rumphi",
        "Salima",
        "Thyolo",
        "Zomba",
      ],
      isSaving: false,
      filters: {
        departmentId: this.loggedInUser?.departmentId, // Set initial value to user's department
        status: null,
        customerId: null,
        dateRange: null
      }
    };
  },
  computed: {
    isSupervisor() {
      return JSON.parse(localStorage.getItem("isSupervisor"));
    },
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    customerId() {
      return this.loggedInUser?.account;
    },
    email() {
      return this.loggedInUser?.email;
    },
    phone() {
      return this.loggedInUser?.phoneNumber;
    },
    company() {
      return this.loggedInUser?.department;
    },
    filteredDepartments() {
      const userDepartment = this.loggedInUser?.departmentId;
      // Create a new array instead of mutating the original
      return [...this.departments].sort((a, b) => {
        if (a.departmentId === userDepartment) return -1;
        if (b.departmentId === userDepartment) return 1;
        return a.name.localeCompare(b.name);
      });
    },

    filteredIssues() {
      let filtered = [...this.issues];
      const userDepartmentId = this.loggedInUser?.departmentId;

      // Apply department filter logic
      if (this.filters.departmentId !== null) {
        // If specific department is selected
        filtered = filtered.filter(issue => 
          issue.assignedDepartmentId === this.filters.departmentId
        );
      } else {
        // If "All Departments" is selected (null), show user's department by default
        const userDepartmentIssues = filtered.filter(issue => 
          issue.assignedDepartmentId === userDepartmentId
        );
        
        // If user's department has no issues or "All Departments" is explicitly selected
        if (userDepartmentIssues.length === 0 || this.filters.departmentId === null) {
          filtered = this.issues; // Show all issues
        } else {
          filtered = userDepartmentIssues; // Show user's department issues
        }
      }

      // ...rest of your existing filter logic remains the same
      if (this.filters.status) {
        filtered = filtered.filter(issue => 
          issue.status === this.filters.status
        );
      }

      if (this.filters.customerId) {
        filtered = filtered.filter(issue => 
          issue.customerId === this.filters.customerId
        );
      }

      if (this.filters.dateRange && this.filters.dateRange.length === 2) {
        const [start, end] = this.filters.dateRange;
        filtered = filtered.filter(issue => {
          const issueDate = new Date(issue.occuredAt);
          return issueDate >= start && issueDate <= end;
        });
      }

      if (this.searchQuery) {
        filtered = filtered.filter(issue =>
          Object.values(issue).some(value =>
            value?.toString().toLowerCase().includes(this.searchQuery.toLowerCase())
          )
        );
      }

      return filtered;
    },

    queriedData() {
      return this.filteredIssues.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },

    total() {
      const totalItems = this.filteredIssues.length;
      // Update pagination total through a watcher instead
      return totalItems;
    },

    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    hasActiveFilters() {
      return (
        this.filters.departmentId ||
        this.filters.status ||
        this.filters.customerId ||
        this.filters.dateRange
      );
    },
    defaultDepartmentName() {
      const userDept = this.departments.find(d => 
        d.departmentId === this.loggedInUser?.departmentId
      );
      return userDept?.name || 'All Departments';
    },

    hasUserDepartmentIssues() {
      if (this.isSupervisor) return true;
      const userDepartmentIssues = this.issues.filter(issue => 
        issue.assignedDepartmentId === this.loggedInUser?.departmentId
      );
      return userDepartmentIssues.length === 0;
    },
    filteredCustomers() {
      // Get unique customers directly from filtered issues
      const uniqueCustomers = this.issues.reduce((acc, issue) => {
        if (issue.customer && issue.customerId && 
            !acc.some(c => c.i_customer === issue.customerId)) {
          acc.push({
            i_customer: issue.customerId,
            name: issue.customer
          });
        }
        return acc;
      }, []);

      // Sort customers by name
      return uniqueCustomers.sort((a, b) => 
        a.name.localeCompare(b.name)
      );
    }
  },
  watch: {
    'filteredIssues.length'(newLength) {
      this.pagination.total = newLength;
    }
  },
  methods: {
    async fetchReports() {
      try {
        let newFaults = await this.reportService.getAll();
        this.newFaults = newFaults.filter((x) => x.status == "New")?.length | 0;
      } catch (error) {
        this.$alert.error("Failed to fetch Team Members");
      }
    },
    async fetchTeamMembers() {
      try {
        this.teamMembers = await this.teamMemberService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Team Members");
      }
    },
    formatAssignee(assignment) {
      const member = this.teamMembers.find(
        (m) => m.id === assignment.assignedTo
      );
      return member ? member.fullName : "Unknown";
    },
    async assignIssue() {
      //console.log(this.selectedAssignee);
      try {
        let startDate = new Date();
        await this.GetSLAHrsPriorty(
          this.currentIssueForAssignment.issueId,
          startDate
        );
        await this.assignmentService.create({
          issueId: this.currentIssueForAssignment.issueId,
          AssignedTo: this.selectedAssignee,
          assignedAt: new Date(),
          startDate,
          dueDate: this.dueDate,
        });

        await this.fetchAssignments();
        this.showAssignmentModal = false;
        this.$alert.success("Issue assigned successfully");
      } catch (error) {
        //console.log(error);
        this.$alert.error("Failed to assign issue");
      }
    },
    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },
    async GetSLAHrsPriorty(issueId, start) {
      let issue = await this.getIssue(issueId);

      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.customerId,
        issue.serviceId,
        start
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.dueDate = result.dueDate;
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },
    async fetchAssignments() {
      try {
        this.assignments = await this.assignmentService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Assignments");
      }
    },
    getAssignment(issueId) {
      return this.assignments.find((a) => a.issueId === issueId);
    },
    async fetchAccountByCustomerId(_customer) {
      this.btnloading = true;
      try {
        let customer = await _customer;
        //console.log(customer.i_customer)
        const input = customer.i_customer;
        if (!input) {
          this.$alert.warning("Please enter a customer Id");
          return;
        }

        //console.log(input)
        const response = await this.searchByAccountsByCustomerIdService.getById(
          input
        );
        this.accounts = response;
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to find accounts"
        );
      } finally {
        this.btnloading = false;
      }
    },
    async searchCustomer() {
      this.btnloading = true;
      try {
        const input = this.searchInput.trim();
        if (!input) {
          this.$alert.warning("Please enter a customer " + searchBy);
          return;
        }

        // Check if input is a number (ID or Account)
        if (this.searchBy == "customerid") {
          // Search by ID or Account
          const response = await this.searchByIdCustomerService.getById(input);
          this.populateCustomerDetails(response);
          this.fetchAccountByCustomerId(response)
        } else if (this.searchBy == "account") {
          // Search by ID or Account
          const response = await this.searchByAccountCustomerService.getById(
            input
          );
          this.populateCustomerDetails(response);
        } else {
          // Search by Name
          const response = await this.searchByValueCustomerService.getById(
            input + "_" + this.searchBy
          );
          this.searchResults = response;

          if(response&&response.length>0){
            this.$alert.success("Customers found");
          }
          
        }
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to search for customer"
        );
      } finally {
        this.btnloading = false;
      }
    },
    onCustomerSelect(customerId) {
      const customer = this.searchResults.find(
        (c) => c.i_customer === customerId
      );
      if (customer) {
        this.populateCustomerDetails(customer);
        this.fetchAccountByCustomerId(customer);
      }
    },
    setDetails() {
      if (this.currentIssue.issueType == "noncustomer") {
        let user = this.loggedInUser;
        this.currentIssue.customerId = "0001";
        this.currentIssue.customer = user.fullName;
        this.currentIssue.company = "MTL";
        this.currentIssue.serviceId = 2;
        this.currentIssue.email = user.email;

        this.currentIssue.phone = user.phoneNumber;
      } else {
        this.currentIssue.customerId = "";
        this.currentIssue.customer = "";
        this.currentIssue.company = "";

        this.currentIssue.email = "";

        this.currentIssue.phone = "";
      }
    },
    populateCustomerDetails(customer) {
      if(customer){
        this.$alert.success("Customer found");
      }
      this.currentIssue.customerId = customer.i_customer;
      this.currentIssue.customer = customer.firstname + " " + customer.lastname;
      this.currentIssue.company = customer.name;

      this.currentIssue.email = customer.email;

      this.currentIssue.phone = customer.phone1 || customer.phone2;

      this.currentIssue.location = customer.city;     

      // Clear search results after selection
      this.searchResults = [];
    },
    async validateUser() {
      if (!this.currentIssue.Id) {
        this.$alert.error("Please fill in all required fields.");
        return;
      }
      this.btnloading = true; // Start loading
      try {
        // Call the backend validation endpoint
        const response = await this.validateUserService.create({
          name: "",
          account: this.currentIssue.Id,
          userType: "customer",
        });

        if (response.message == "Validation successful.") {
          this.$alert.success("Client found");

          const customerData = response.customer;

          //console.log(customerData);

          // Bind the customer data to the form fields

          this.currentIssue.customerId = customerData.i_customer; // Bind customer ID
          this.currentIssue.company =
            customerData.name ||
            customerData?.firstname + " " + customerData?.lastname; // Bind customer name
          this.currentIssue.phone = customerData.phone1 || customerData.phone2; // Bind phone
          this.currentIssue.email = customerData.email; // Bind email
          this.currentIssue.district = customerData.city; // Bind location
          this.currentIssue.location = customerData.address_line_2; // Bind location

          this.currentIssue.customer =
            customerData?.firstname + " " + customerData?.lastname;
        } else {
          this.$alert.error("Client not found");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message ||
            "Error validating user. Please try again."
        );
      } finally {
        this.btnloading = false; // Stop loading
      }
    },
    handleRowClick(row) {
      this.$router.push(`/technical/issue-view/${row.issueId}`);
    },
    // Formatting Methods
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },
    formatCustomer(row, column, cellValue) {
      const customer = this.customers.find((c) => c.customerId === cellValue);
      return customer ? customer.name : "Unknown";
    },
    formatAccount(row, column, cellValue) {
      const customer = this.customers.find(
        (c) => c.customerId === row.customerId
      );
      return customer ? customer.account : "Unknown";
    },

    // Fetch Methods
    async fetchIssues() {
      this.loading = true;
      try {
        const response = await this.issueService.getAll();
        this.issues = response || [];
        this.pagination.total = this.issues.length || 0;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch Issues");
      } finally {
        this.loading = false;
      }
    },
    async fetchCustomers() {
      try {
        this.customers = await this.customerService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },
    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll())?.services ?? [];
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
        //console.log(this.departments);
      } catch (error) {
        this.$alert.error("Failed to fetch Departments");
      }
    },
    resetCurrentIssue() {
      this.currentIssue = {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
        district: "",
        assignedDepartmentId: null,
      };
    },
    editIssue(row) {
      this.currentIssue = { ...row };
      this.isEditing = true;
      this.showIssueForm = true;
    },
    closeIssueForm() {
      this.resetCurrentIssue();
      this.isEditing = false;
      this.showIssueForm = false;
    },
    async submitIssue() {
      if (this.isSaving) return; // Prevent double submission
      this.isSaving = true;
      try {
        if (this.isEditing) {
          await this.updateIssue();
        } else {
          await this.addIssue();
        }
      } finally {
        this.isSaving = false;
      }
    },
    async addIssue() {
      try {
        const newObject = { ...this.currentIssue };
        delete newObject.issueId;
        if (newObject.assignedDepartmentId) {
          newObject.assignedDate = new Date();
        } else {
          newObject.assignedDepartmentId = 0;
        }

        newObject.source = "Technical";
        await this.issueService.create(newObject);
        this.$alert.success("Issue added successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to add Issue"
        );
      }
    },
    async updateIssue() {
      try {
        await this.issueService.update(
          this.currentIssue.issueId,
          this.currentIssue
        );
        this.$alert.success("Issue updated successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update Issue"
        );
      }
    },
    async loadall() {
      await Promise.all([
        await this.fetchAssignments(),
        await this.fetchDepartments(),
        await this.fetchTeamMembers(),
        this.fetchIssues(),
        this.fetchCustomers(),
        this.fetchServices(),
        this.fetchDepartments(),
        this.fetchReports(),
      ]);
    },
    getDepartment(departmentId) {
      const department = this.departments.find(
        (d) => d.departmentId === departmentId
      );
      return department ? department.name : "Unknown Department";
    },
    resetFilters() {
      this.filters = {
        departmentId: null, // Reset to null to show user's department
        status: null,
        customerId: null,
        dateRange: null
      };
    },
    resetDepartmentFilter() {
      this.filters.departmentId = null; // Reset to null to show user's department
    }
  },
  created() {
    // Set initial department filter
    this.filters.departmentId = this.loggedInUser?.departmentId;
    this.loadall();
  },
};
</script>
