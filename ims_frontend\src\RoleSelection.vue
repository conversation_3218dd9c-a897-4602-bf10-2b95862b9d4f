<template>
  <auth-layout pageClass="role-selection-page">
    <div class="row d-flex justify-content-center align-items-center mt-5">
      <card class="col-lg-4 col-md-4 col-sm-8">
        <template slot="header">
          <h3 class="card-title">{{ totalUsers === 0 ? 'Admin Setup' : 'Role Selection' }}</h3>
        </template>
        <div>
          <!-- Show only Admin option if no users exist -->
          <template v-if="totalUsers === 0">
            <p class="text-muted mb-3">You are the first user. You will be set up as an administrator.</p>
            <button class="btn btn-fill btn-info w-100" @click="setAsAdmin">
              Continue as Admin
            </button>
          </template>

          <!-- Show call center toggle for subsequent users -->
          <template v-else>
            <div class="form-group">
              <p class="text-muted mb-3">Please indicate if you are part of the call center team:</p>
              <el-switch
                v-model="isCallCenter"
                active-text="Call Center"
                inactive-text="Other Department"
                class="w-100 mb-3"
              >
              </el-switch>
              <button class="btn btn-fill btn-info w-100 mt-3" @click="updateUser">
                Continue
              </button>
            </div>
          </template>
        </div>
      </card>
    </div>
  </auth-layout>
</template>

<script>
import AuthLayout from "./Layout/AuthLayout.vue";
import { Switch } from "element-ui";
import API from "@/services/api";

export default {
  name: "RoleSelection",
  components: {
    AuthLayout,
    "el-switch": Switch
  },
  data() {
    return {
      userService: new API(process.env.VUE_APP_API_URL, "users"),
      isCallCenter: false,
      user: {},
      totalUsers: 0,
      loading: false
    };
  },
  async created() {
    await this.getTotalUsers();
    await this.fetchUser();
  },
  methods: {
    async fetchUser() {
      this.loading = true;
      try {
        const response = await this.userService.getById(
          localStorage.getItem("userId")
        );
        this.user = response;
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to fetch user"
        );
      } finally {
        this.loading = false;
      }
    },
    async getTotalUsers() {
      try {
        const response = await this.userService.getAll();
        this.totalUsers = response.length;
      } catch (error) {
        this.$alert.error("Failed to fetch users count");
      }
    },
    async setAsAdmin() {
      try {
        await this.userService.update(this.user.id, {
          ...this.user,
          role: 'Admin'
        });
        localStorage.setItem("userRole", "admin");
        this.$router.push("/admin");
      } catch (error) {
        this.$alert.error("Failed to set admin role");
      }
    },
    async updateUser() {
      try {
        const role = this.isCallCenter ? 'Call center' : 'Technical';
        await this.userService.update(this.user.id, {
          ...this.user,
          role: role
        });
        localStorage.setItem("userRole", role.toLowerCase().replace(/\s+/g, "-"));
        this.$router.push(`/${role.toLowerCase().replace(/\s+/g, "-")}`);
      } catch (error) {
        this.$alert.error("Failed to update user role");
      }
    }
  }
};
</script>

<style scoped>
.role-selection-page {
  min-height: 100vh;
  background-color: #f4f3ef;
}
</style>
