<template>
  <auth-layout pageClass="signup-page">
    <div class="row d-flex justify-content-center align-items-center mt-4">
      <div class="col-lg-4 col-md-6 col-sm-8">
        <ValidationObserver v-slot="{ handleSubmit }">
          <form @submit.prevent="handleSubmit(submit)">
            <fade-render-transition>
              <card>
                <div slot="header">
                  <h3 class="card-title text-center">Sign Up</h3>
                </div>
                <div>
                  <!-- User Type Selector -->
                  <ValidationProvider
                    name="userType"
                    rules="required"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input label="User Type" required>
                      <el-select
                        v-model="formData.userType"
                        placeholder="Select User Type"
                        class="w-100"
                        :class="{ 'is-invalid': failed, 'is-valid': passed }"
                        :disabled="loading"
                        required
                      >
                        <el-option
                          v-for="option in userTypeOptions"
                          :key="option.value"
                          :label="option.label"
                          :value="option.value"
                        />
                      </el-select>
                      <div v-if="failed" class="invalid-feedback">
                        User type is required.
                      </div>
                    </fg-input>
                  </ValidationProvider>

                  <!-- Account Field (Conditional) -->
                  <ValidationProvider
                    v-if="formData.userType === 'customer'"
                    name="account"
                    rules="required"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      label="Account/Customer ID"
                      v-model="formData.account"
                      placeholder="Account"
                      type="text"
                      :error="failed ? 'Account is required' : null"
                      :hasSuccess="passed"
                      :disabled="loading"
                      @input="resetValidation"
                    >
                    </fg-input>
                  </ValidationProvider>

                  <!-- Validate User Button (Conditional) -->
                  <div
                    class="text-center mb-4"
                    v-if="formData.userType === 'customer'"
                  >
                    <button
                      type="button"
                      class="btn btn-fill btn-warning btn-round btn-wd"
                      @click="validateUser"
                      v-if="!isUserValid"
                      :disabled="loading"
                    >
                      <span v-if="!loading">Validate details</span>
                      <span v-else>
                        <i class="fas fa-spinner fa-spin"></i> Validating...
                      </span>
                    </button>
                  </div>

                  <!-- Full Name Field (Conditional) -->
                  <ValidationProvider
                    v-if="isUserValid && formData.userType == 'customer'"
                    name="fullname"
                    rules="required"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="text"
                      :error="failed ? 'The Name field is required' : null"
                      :hasSuccess="passed"
                      label="Full name"
                      name="fullname"
                      readonly
                      v-model="formData.fullname"
                      :disabled="loading"
                      @input="resetValidation"
                    >
                    </fg-input>
                  </ValidationProvider>

                  <!-- Username Field (Always Visible) -->
                  <ValidationProvider
                    v-if="isUserValid || formData.userType == 'internal'"
                    name="username"
                    :rules="{ required: true }"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="text"
                      :error="failed ? 'The Username field is required' : null"
                      :hasSuccess="passed"
                      label="Username"
                      name="username"
                      v-model="formData.username"
                      :disabled="loading"
                    >
                    </fg-input>
                  </ValidationProvider>

                  <!-- Email Field (Conditional) -->
                  <ValidationProvider
                    v-if="isUserValid && formData.userType == 'customer'"
                    name="email"
                    :rules="{
                      required: true,
                      email: true,
                      internalEmail: { userType: formData.userType },
                    }"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="email"
                      :error="
                        failed
                          ? 'The Email field is required and must meet the conditions'
                          : null
                      "
                      :hasSuccess="passed"
                      label="Email address"
                      name="email"
                      v-model="formData.email"
                      :disabled="loading"
                    >
                    </fg-input>
                  </ValidationProvider>
                  <!-- Email Field (Conditional) -->
                  <ValidationProvider
                    v-if="isUserValid && formData.userType == 'customer'"
                    name="phone"
                    :rules="{
                      required: true
                    }"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="phone"
                      :error="
                        failed
                          ? 'The Phone field is required and must meet the conditions'
                          : null
                      "
                      :hasSuccess="passed"
                      label="Phone"
                      name="Phone"
                      v-model="formData.phone"
                      :disabled="loading"
                    >
                    </fg-input>
                  </ValidationProvider>

                  <!-- Password Field (Conditional) -->
                  <ValidationProvider
                    v-if="isUserValid && formData.userType == 'customer'"
                    name="password"
                    rules="required|min:5"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="password"
                      :error="failed ? 'The Password field is required' : null"
                      :hasSuccess="passed"
                      name="password"
                      label="Password"
                      v-model="formData.password"
                      :disabled="loading"
                    >
                    </fg-input>
                  </ValidationProvider>

                  <!-- Confirm Password Field (Conditional) -->
                  <ValidationProvider
                    v-if="isUserValid && formData.userType == 'customer'"
                    name="confirmPassword"
                    rules="required|sameAs:password"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="password"
                      :error="failed ? 'Passwords must match' : null"
                      :hasSuccess="passed"
                      name="confirmPassword"
                      label="Confirm Password"
                      v-model="formData.confirmPassword"
                      :disabled="loading"
                    >
                    </fg-input>
                  </ValidationProvider>
                </div>

                <!-- Submit Button (Conditionally Rendered) -->
                <div
                  class="text-center mt-3"
                  v-if="isUserValid || formData.userType !== 'customer'"
                >
                  <button
                    type="submit"
                    class="btn btn-fill btn-info btn-round btn-wd"
                    :disabled="loading"
                  >
                    <span v-if="!loading">Sign Up</span>
                    <span v-else>
                      <i class="fas fa-spinner fa-spin"></i> Submitting...
                    </span>
                  </button>
                  <br />
                  <div class="forgot">
                    <router-link to="/login" class="card-category">
                      Already have an account? Log in
                    </router-link>
                  </div>
                </div>
              </card>
            </fade-render-transition>
          </form>
        </ValidationObserver>
      </div>
    </div>
  </auth-layout>
</template>
<script>
import { FadeRenderTransition } from "src/components/index";
import AuthLayout from "./Layout/AuthLayout.vue";
import { extend } from "vee-validate";
import { required, email, min, confirmed} from "vee-validate/dist/rules";
import API from "@/services/api";
import { Select, Option } from "element-ui";

// Extend validation rules
extend("email", email);
extend("required", required);
extend("min", min);
extend("sameAs", confirmed);

extend("internalEmail", {
  params: ["userType"],
  validate(value, { userType }) {
    if (userType == "internal") {
      return value.endsWith("@mtl.mw");
    }
    return true;
  },
  message: "Internal users must use an @mtl.mw email address.",
});

export default {
  name: "SignupForm",

  components: {
    FadeRenderTransition,
    AuthLayout,
    "el-select": Select,
    "el-option": Option,
  },

  data() {
    return {
      userService: new API(process.env.VUE_APP_API_URL, "auth/register"),
      validateUserService: new API(
        process.env.VUE_APP_API_URL,
        "auth/validate-customer"
      ),
      tempName: "",
      tempAccount: "",
      formData: {
        fullname: "",
        email: "",
        password: "",
        department:"",
        confirmPassword: "",
        userType: "customer",
      },
      userTypeOptions: [
        { label: "Customer", value: "customer" },
        { label: "Internal", value: "internal" },
      ],
      isUserValid: false, // Controls submit button visibility
      loading: false, // Tracks loading state
    };
  },

  methods: {
    resetValidation() {
      this.isUserValid = false;
    },
    async validateUser() {
      if (!this.formData.account) {
        this.validationMessage = "Please fill in all required fields.";
        this.isUserValid = false;
        return;
      }

      this.loading = true;
      try {
        const response = await this.validateUserService.create({
          name: this.formData.fullname,
          account: this.formData.account,
          userType: "customer",
        });

        if (response.message == "Validation successful.") {
          // Store original values for submission
          this.tempName = response?.customer?.firstname + " " + response?.customer?.lastname;
          this.tempAccount = response.account.toString();
          
          // Display masked values in form
          const maskedFirstName = this.maskName(response?.customer?.firstname);
          const maskedLastName = this.maskName(response?.customer?.lastname);
          this.formData.fullname = `${maskedFirstName} ${maskedLastName}`;
          this.formData.department = response?.customer?.name;
          
          this.$alert.success("Your details are valid");
          this.isUserValid = true;
        } else {
          this.$alert.error("Your details are invalid");
          this.isUserValid = false;
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message ||
            "Error validating user. Please try again."
        );
        this.isUserValid = false;
      } finally {
        this.loading = false;
      }
    },

    maskName(name) {
      if (!name || name.length <= 2) return name;
      return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
    },

    async submit() {
      this.loading = true;
      try {
        // Use original unmasked values for registration
        const registrationData = {
          fullname: this.tempName, // Using unmasked name stored earlier
          email: this.formData.email,
          username: this.formData.username,
          department: this.formData?.department,
          account: this.tempAccount,
          password: this.formData.password,
          userType: this.formData.userType,
        };

        await this.userService.create(registrationData);
        this.$alert.successPermanent(
          "Sign up form has been submitted! Verify your email to login."
        );

        this.resetForm();
        this.$router.push("/login");
      } catch (error) {
        this.$alert.error(
          error?.response?.data?.message ||
            error?.response?.data[0]?.description ||
            "Failed to register"
        );
      } finally {
        this.loading = false;
      }
    },

    resetForm() {
      this.formData = {
        fullname: "",
        email: "",
        password: "",
        confirmPassword: "",
        userType: "customer",
      };
      this.isUserValid = false;
      this.validationMessage = "";
    },
  },
};
</script>