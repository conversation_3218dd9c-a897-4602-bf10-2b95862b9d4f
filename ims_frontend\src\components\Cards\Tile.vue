<template>
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="flex-grow-1">
            <h5 class="card-title mb-0">{{ title }}</h5>
          </div>
          <div class="ms-3">
            <i :class="icon" :style="{ color: color }"></i>
          </div>
        </div>
        <h2 class="mt-3">{{ value }}</h2>
        <slot name="footer"></slot>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      title: String,
      value: [String, Number],
      subtitle: String,
      icon: String,
      color: String,
      loading: Boolean,
    },
  };
  </script>
