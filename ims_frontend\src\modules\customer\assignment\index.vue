<template>
  <div class="assignments-management">
    <!-- Assignment Form -->
    <card v-if="showAssignmentForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{ isEditing ? "Edit Assignment" : "Add Assignment" }}
        </h4>
      </template>
      <form @submit.prevent="submitAssignment">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Issue" required>
              <el-select
                v-model="currentAssignment.issueId"
                placeholder="Select issue"
                @change="
                  GetSLAHrsPriorty(
                    currentAssignment?.issueId,
                    currentAssignment?.startDate
                  )
                "
                filterable
                class="w-100"
              >
                <el-option
                  v-for="issue in issues"
                  :key="issue.issueId"
                  :label="`${issue.title} (ID: ${issue.issueId})`"
                  :value="issue.issueId"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Assigned To" required>
              <el-select
                v-model="currentAssignment.assignedTo"
                placeholder="Select assignee"
                filterable
                class="w-100"
              >
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="`${user.fullName} (${user.email})`"
                  :value="user.id"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Start Date" required>
              <el-date-picker
                type="datetime"
                format="dd-MM-yyyy HH:mm A"
                placeholder="Pick a day and time"
                v-model="currentAssignment.startDate"
                :readonly="isEditing"
                @focus="
                  GetSLAHrsPriorty(
                    currentAssignment?.issueId,
                    currentAssignment?.startDate
                  )
                "
                @input="
                  GetSLAHrsPriorty(
                    currentAssignment?.issueId,
                    currentAssignment?.startDate
                  )
                "
                required
              >
              </el-date-picker>
            </fg-input>
          </div>

          <div class="col-md-6">
            <fg-input label="Due Date">
              <el-date-picker
                type="datetime"
                format="dd-MM-yyyy HH:mm A"
                v-model="currentAssignment.dueDate"
                readonly
              >
              </el-date-picker>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Assignment" : "Add Assignment" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeAssignmentForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Assignments Table -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">Assignments List</h4></div>
          <div class="col-6 text-right">
            <button class="btn btn-primary" @click="showAssignmentForm = true">
              Add Assignment
            </button>
            <router-link to="/customer/issues" class="btn btn-success mx-2">
              Issues
            </router-link>
          </div>
        </div>
      </template>

      <!-- Search and Pagination -->
      <div
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <el-select
          v-model="pagination.perPage"
          placeholder="Per page"
          style="width: 200px"
          class="mb-3"
        >
          <el-option
            v-for="item in pagination.perPageOptions"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>

        <el-input
          type="search"
          v-model="searchQuery"
          placeholder="Search records"
          style="width: 200px"
          aria-controls="datatables"
        />
      </div>

      <el-table :data="queriedData" stripe border style="width: 100%">
        <el-table-column
          v-for="column in tableColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth"
          :formatter="column.formatter"
        ></el-table-column>

        <el-table-column label="Actions" width="180">
          <template v-slot="{ row }">
            <button
              class="btn btn-info btn-sm mr-2"
              @click="editAssignment(row)"
            >
              <i class="fa fa-pencil"></i>
            </button>
            <button
              class="btn btn-danger btn-sm"
              @click="deleteAssignment(row.assignmentId)"
            >
              <i class="fa fa-trash"></i>
            </button>
          </template>
        </el-table-column>
      </el-table>

      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import {
  Select,
  Option,
  Table,
  TableColumn,
  Input,
  DatePicker,
} from "element-ui";
import API from "@/services/api";
import { slaCalculator } from "../../../services/slacalculator";
import moment from "moment";

export default {
  name: "AssignmentsManagement",
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    "el-date-picker": DatePicker,
  },
  computed: {
    queriedData() {
      let filtered = this.assignments.filter((assignment) =>
        Object.values(assignment).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.assignments.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  data() {
    return {
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      userService: new API(process.env.VUE_APP_API_URL, "users"),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      slapriority: 0,
      sladuedate: "",
      tableColumns: [
        { prop: "assignmentId", label: "ID", minWidth: 80 },
        {
          prop: "issueId",
          label: "Issue",
          minWidth: 200,
          formatter: this.formatIssue,
        },
        {
          prop: "assignedTo",
          label: "Assigned To",
          minWidth: 200,
          formatter: this.formatUser,
        },
        {
          prop: "assignedAt",
          label: "Assigned At",
          minWidth: 180,
          formatter: this.formatDateTime,
        },
        {
          prop: "dueDate",
          label: "Due Date",
          minWidth: 180,
          formatter: this.formatDateTime,
        },
      ],
      assignments: [],
      issues: [],
      users: [],
      currentAssignment: {
        assignmentId: null,
        issueId: null,
        assignedTo: null,
        startDate: null,
        dueDate: null,
      },
      showAssignmentForm: false,
      isEditing: false,
    };
  },

  methods: {
    // Formatting Methods for Table
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },

    formatIssue(row, column, cellValue) {
      const issue = this.issues.find((i) => i.issueId === cellValue);
      return issue ? `${issue.title} (${issue.issueId})` : "Unknown Issue";
    },

    formatUser(row, column, cellValue) {
      const user = this.users.find((u) => u.id === cellValue);
      return user ? `${user.fullName}` : "Unknown User";
    },

    // Fetch Methods
    async fetchAssignments() {
      try {
        const response = await this.assignmentService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          searchQuery: this.searchQuery,
        });

        this.assignments = response;
        this.pagination.total = response.total;
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to fetch Assignments"
        );
        console.error("Error fetching Assignments:", error);
      }
    },

    async fetchIssues() {
      try {
        this.issues = await this.issueService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Issues");
      }
    },

    async GetSLAHrsPriorty(issueId, start) {
      let issue = await this.getIssue(issueId);

      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.customerId,
        issue.serviceId,
        start
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.currentAssignment.dueDate = result.dueDate;
    },

    getIssueDetails(issueId) {
      let issue = this.getIssue(issueId);
      return `${issue.title} \n${issue.description}`;
    },

    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },

    async fetchUsers() {
      try {
        this.users = await this.userService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Users");
      }
    },

    // CRUD Methods
    async submitAssignment() {
      if (this.isEditing) {
        await this.updateAssignment();
      } else {
        await this.addAssignment();
      }
    },

    async addAssignment() {
      try {
        const newObject = { ...this.currentAssignment };
        delete newObject.assignmentId;

        await this.assignmentService.create(newObject);
        this.$alert.success("Assignment added successfully");
        await this.loadAll();
        this.closeAssignmentForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to add Assignment"
        );
      }
    },

    async updateAssignment() {
      try {
        await this.assignmentService.update(
          this.currentAssignment.assignmentId,
          this.currentAssignment
        );
        await this.loadAll();
        this.$alert.success("Assignment updated successfully");
        this.closeAssignmentForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update Assignment"
        );
      }
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },

    editAssignment(assignment) {
      this.currentAssignment = {
        ...assignment,
        // Convert timestamp to datetime-local format
        dueDate: new Date(assignment.dueDate).toISOString().slice(0, 16),
      };
      this.isEditing = true;
      this.showAssignmentForm = true;
    },

    async deleteAssignment(assignmentId) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this Assignment?"
        );
        if (confirmDelete.isConfirmed) {
          await this.assignmentService.delete(assignmentId);
          await this.loadAll();
          this.$alert.success("Assignment deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete Assignment"
        );
      }
    },

    async loadAll() {
      await Promise.all([
        this.fetchAssignments(),
        this.fetchIssues(),
        this.fetchUsers(),
      ]);
    },

    closeAssignmentForm() {
      this.isEditing = false;
      this.showAssignmentForm = false;
      this.currentAssignment = {
        assignmentId: null,
        issueId: null,
        assignedTo: null,
        dueDate: null,
      };
    },
  },

  async created() {
    // Fetch initial data
    await this.loadAll();

    const issueId = this.$route.params.id;

    if (issueId) {
      const issueExists = this.issues.find((issue) => issue.issueId == issueId);
      if (issueExists) {
        this.currentAssignment = issueExists;
        this.showAssignmentForm = true; // Open the form
        this.assignments = this.assignments.filter((x) => x.issueId == issueId);
      } else {
        this.$alert.warning(`Issue with ID ${issueId} not found.`);
      }
    }
  },
  watch: {
    // Watch for changes in search query and pagination
    searchQuery() {
      this.pagination.currentPage = 1;
      this.fetchAssignments();
    },
    "pagination.currentPage"() {
      this.fetchAssignments();
    },
    "pagination.perPage"() {
      this.pagination.currentPage = 1;
      this.fetchAssignments();
    },
  },
};
</script>
