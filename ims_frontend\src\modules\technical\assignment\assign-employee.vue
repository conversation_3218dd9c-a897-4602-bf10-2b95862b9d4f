<template>
  <div class="assignments-management">
    <!-- Assignment Form -->
    <card v-if="showAssignmentForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{
            (isEditing ? "Edit Assignment" : "Add Assignment") +
      " (" + getRemainingHours(currentAssignment.startDate,  currentAssignment.dueDate)+"hrs remaining - this include offhours)"
          }}
        </h4>
      </template>
      <form @submit.prevent="submitAssignment">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Issue" required>
              <el-select
                v-model="currentAssignment.issueId"
                placeholder="Select issue"
                @change="
                  GetSLAHrsPriorty(
                    currentAssignment?.issueId
                  )
                "
                filterable
                class="w-100"
              >
                <el-option
                  v-for="issue in issues"
                  :key="issue.issueId"
                  :label="`${issue.title} (Ref: ${issue.issueRef})`"
                  :value="issue.issueId"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6" >
            <fg-input label="Assigned To" required>
              <el-select
                v-model="currentAssignment.assignedTo"
                placeholder="Select assignee"
                filterable
                class="w-100"
              >
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="`${user.fullName} (${user.email})`"
                  :value="user.id"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Start Date">
              <el-date-picker
                type="datetime"
                format="dd-MM-yyyy HH:mm A"
                v-model="currentAssignment.startDate"
                readonly
              >
              </el-date-picker>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Due Date">
              <el-date-picker
                type="datetime"
                format="dd-MM-yyyy HH:mm A"
                v-model="currentAssignment.dueDate"
                readonly
              >
              </el-date-picker>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Assignment" : "Save Assignment" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeAssignmentForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Assignments Table -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">Assignments List</h4></div>
          <div class="col-6 text-right">
            <button class="btn btn-primary" @click="showAssignmentForm = true">
              Add Assignment
            </button>
            <router-link to="/technical/issues" class="btn btn-success mx-2">
              Issues
            </router-link>
          </div>
        </div>
      </template>

      <!-- Search and Pagination -->
      <div
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <el-select
          v-model="pagination.perPage"
          placeholder="Per page"
          style="width: 200px"
          class="mb-3"
        >
          <el-option
            v-for="item in pagination.perPageOptions"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>

        <el-input
          type="search"
          v-model="searchQuery"
          placeholder="Search records"
          style="width: 200px"
          aria-controls="datatables"
        />
      </div>

      <el-table :data="queriedData" stripe border style="width: 100%">
        <el-table-column
          v-for="column in tableColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth"
          :formatter="column.formatter"
        ></el-table-column>

        <el-table-column label="Actions" width="180">
          <template v-slot="{ row }">
            <button
              class="btn btn-info btn-sm mr-2"
              @click="editAssignment(row)"
            >
              <i class="fa fa-pencil"></i>
            </button>
          </template>
        </el-table-column>
      </el-table>

      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import {
  Select,
  Option,
  Table,
  TableColumn,
  Input,
  DatePicker,
} from "element-ui";
import API from "@/services/api";
import { slaCalculator } from "../../../services/slacalculator";
import moment from "moment";

export default {
  name: "AssignmentsManagement",
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    "el-date-picker": DatePicker,
  },
  computed: {
    isSupervisor() {
      return JSON.parse(localStorage.getItem("isSupervisor"));
    },
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    queriedData() {
      let filtered = this.assignments.filter((assignment) =>
        Object.values(assignment).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.assignments.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  data() {
    return {
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      userService: new API(process.env.VUE_APP_API_URL, "users"),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      slapriority: 0,
      sladuedate: "",
      tableColumns: [
        { prop: "assignmentId", label: "ID", minWidth: 80 },
        {
          prop: "issueId",
          label: "Issue Ref",
          minWidth: 200,
          formatter: this.formatIssue,
        },
        {
          prop: "assignedTo",
          label: "Assigned To",
          minWidth: 200,
          formatter: this.formatUser,
        },
        {
          prop: "assignedAt",
          label: "Assigned At",
          minWidth: 180,
          formatter: this.formatDateTime,
        },
        {
          prop: "dueDate",
          label: "Due Date",
          minWidth: 180,
          formatter: this.formatDateTime,
        },
      ],
      assignments: [],
      issues: [],
      users: [],
      slas: [],
      slaService: new API(process.env.VUE_APP_API_URL, "slas"),
      currentAssignment: {
        assignmentId: null,
        issueId: null,
        assignedTo: null,
        startDate: null,
        dueDate: null,
      },
      showAssignmentForm: false,
      isEditing: false,
    };
  },

  methods: {
    getAssignment(issueId) {
      return this.assignments.find((a) => a.issueId === issueId);
    },
    getRemainingHours(startDate, endDate=new Date()) {
      // Convert both dates to milliseconds
      const startTime = (new Date(startDate)).getTime();
      const endTime = (new Date(endDate)).getTime();

      // Calculate the difference in milliseconds
      const differenceInMs = (endTime - startTime);

      // Convert the difference from milliseconds to hours
      const differenceInHours = differenceInMs / (1000 * 60 * 60);

      return differenceInHours.toFixed(0);
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },

    formatUser(row, column, cellValue) {
      const user = this.users.find((u) => u.id === cellValue);
      return user ? `${user.fullName}` : "Unknown User";
    },
    formatIssue(assignment) {
      const issue = this.issues.find((u) => u.issueId == assignment.issueId);
      return issue ? `${issue.issueRef}` : "Unknown Issue";
    },

    // Fetch Methods
    async fetchAssignments() {
      try {
        const res = await this.assignmentService.getAll();
        this.assignments = res;
        this.pagination.total = res.length;
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to fetch Assignments"
        );
        console.error("Error fetching Assignments:", error);
      }
    },

    async fetchIssues() {
      try {
        this.issues = await this.issueService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Issues");
      }
    },

    async GetSLAHrsPriorty(issueId) {
      let issue = await this.getIssue(issueId);
      this.currentAssignment.startDate = issue.reportedAt;
      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.serviceId,
        issue.reportedAt
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.currentAssignment.dueDate = result.dueDate;
    },

    getIssueDetails(issueId) {
      let issue = this.getIssue(issueId);
      return `${issue.title} \n${issue.description}`;
    },

    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },

    async fetchUsers() {
      try {
        this.users = (await this.userService.getAll())?.filter(
          (x) => x.department == this.loggedInUser.department
        );
      } catch (error) {
        this.$alert.error("Failed to fetch Users");
      }
    },

    // CRUD Methods
    async submitAssignment() {
      if (this.isEditing) {
        await this.updateAssignment();
      } else {
        await this.addAssignment();
      }
    },

    async addAssignment() {
      try {
        const newObject = { ...this.currentAssignment };
        delete newObject.assignmentId;

        this.slas;

        if (!newObject.dueDate) {
          newObject.dueDate = newObject.startDate;
        }

        await this.assignmentService.create(newObject);
        this.$alert.success("Assignment added successfully");
        await this.loadAll();
        this.closeAssignmentForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to add Assignment"
        );
      }
    },

    async updateAssignment() {
      try {
        await this.assignmentService.update(
          this.currentAssignment.assignmentId,
          this.currentAssignment
        );
        await this.loadAll();
        this.$alert.success("Assignment updated successfully");
        this.closeAssignmentForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update Assignment"
        );
      }
    },

    async fetchSLAs() {
      try {
        const response = await this.slaService.getAll();

        this.slas = response; // Assuming response contains the data array
        this.pagination.total = response.length; // Assuming response contains total count
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch SLAs");
        console.error("Error fetching SLAs:", error);
      }
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },

    editAssignment(assignment) {
      this.currentAssignment = {
        ...assignment,
        // Convert timestamp to datetime-local format
        dueDate: new Date(assignment.dueDate).toISOString().slice(0, 16),
      };
      this.isEditing = true;
      this.showAssignmentForm = true;
    },

    async deleteAssignment(assignmentId) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this Assignment?"
        );
        if (confirmDelete.isConfirmed) {
          await this.assignmentService.delete(assignmentId);
          await this.loadAll();
          this.$alert.success("Assignment deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete Assignment"
        );
      }
    },

    async loadAll() {
      await Promise.all([
        this.fetchSLAs(),
        this.fetchAssignments(),
        this.fetchIssues(),
        this.fetchUsers(),
      ]);
    },

    closeAssignmentForm() {
      this.isEditing = false;
      this.showAssignmentForm = false;
      this.currentAssignment = {
        assignmentId: null,
        issueId: null,
        assignedTo: null,
        dueDate: null,
        startDate: null,
      };
    },
  },

  async created() {
    // Fetch initial data
    await this.loadAll();

    const issueId = this.$route.params.id;

    if (issueId) {
      const issueExists = this.issues.find((issue) => issue.issueId == issueId);
      if (issueExists) {
        this.currentAssignment.issueId = issueId;
        this.showAssignmentForm = true; // Open the form
        this.assignments = this.assignments.filter((x) => x.issueId == issueId);
       
        this.GetSLAHrsPriorty(issueId);
        
      } else {
        this.$alert.warning(`Issue with ID ${issueId} not found.`);
      }
    }
  },
  watch: {
    // Watch for changes in search query and pagination
    searchQuery() {
      this.pagination.currentPage = 1;
      this.fetchAssignments();
    },
    "pagination.currentPage"() {
      this.fetchAssignments();
    },
    "pagination.perPage"() {
      this.pagination.currentPage = 1;
      this.fetchAssignments();
    },
  },
};
</script>
<style>
.el-date-table td.default span {
  color: #409eff;
  font-weight: 700;
}
</style>
