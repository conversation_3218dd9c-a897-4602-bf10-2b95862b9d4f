/*!

=========================================================
* Vue Light Bootstrap Dashboard - v1.4.2
=========================================================

* Product Page: http://www.creative-tim.com/product/vue-light-bootstrap-dashboard-pro
* Copyright 2022 Small World (http://www.creative-tim.com)
* Licensed under MIT (https://github.com/creativetimofficial/light-bootstrap-dashboard/blob/master/LICENSE.md)

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import LightBootstrap from './light-bootstrap-main'
import '@fortawesome/fontawesome-free/css/all.css';
import Alert from "./services/alert"

// Import Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// Plugins
import App from './App.vue'
// router setup
import routes from './routes/routes'

// plugin setup
Vue.use(VueRouter)
Vue.use(LightBootstrap)
Vue.use(ElementUI) // Add Element UI to Vue

// configure router
const router = new VueRouter({
  routes,
  linkActiveClass: 'active',
  mode: 'history',
  scrollBehavior (to, from, savedPosition) {
    return { x: 0, y: 0 }
  }
})


router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || 'null');
  const userRole = localStorage.getItem('userRole');

  // Define special routes
  const isPublicRoute = to.matched.some(record => record.meta.public);
  const isRoleSelectionPage = to.path === '/role-selection';

  // CASE 1: Not logged in user
  if (!token || !user) {
    // Allow access to all public routes when not logged in
    return next();
  }

  // CASE 2: Logged in user
  // Don't allow access to login page when logged in
  if (to.path === '/login') {
    // Redirect to role selection if no role
    if (!userRole || userRole.trim() === '') {
      return next('/role-selection');
    }
    // Otherwise redirect to their dashboard
    return next(getLandingPage(user, userRole));
  }

  // CASE 3: Logged in but no role defined
  if (!userRole || userRole.trim() === '') {
    // Allow access to role selection page
    if (isRoleSelectionPage) {
      return next();
    }
    // Redirect to role selection for all other pages
    return next('/role-selection');
  }

  // CASE 4: Logged in with role
  // Don't allow access to public routes (except those explicitly allowed)
  if (isPublicRoute && !isRoleSelectionPage) {
    return next(getLandingPage(user, userRole));
  }

  // Role-based path routing
  const basePath = to.path.split('/')[1];
  const userRolePath = userRole.toLowerCase();
  
  // Redirect to role-specific path if not already there
  if (basePath !== userRolePath && !isPublicRoute && !isRoleSelectionPage) {
    return next(`/${userRolePath}`);
  }

  // Allow navigation for all other cases
  return next();
});

// Simplified landing page helper
function getLandingPage(user, userRole) {
  if (user.userType === 'customer') {
    return '/customer-dashboard';
  }
  
  return `/${(userRole || user.role || 'default').toLowerCase().replace(/\s+/g, '-')}`;
}

Vue.prototype.$alert = Alert;

/* eslint-disable no-new */
new Vue({
  el: '#app',
  render: h => h(App),
  router
})
