<template>
  <div class="sla-management">
    <!-- SLA Form -->
    <card v-if="showSLAForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">{{ isEditing ? "Edit SLA" : "Add SLA" }}</h4>
      </template>
      <form @submit.prevent="submitSLA">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Service">
              <el-select
                v-model="currentSLA.serviceId"
                placeholder="Select service"
                filterable
                class="w-100"
                :loading="!services.length"
              >
              <el-option
                  v-for="service in services"
                  :key="service.i_service_type"
                  :label="service.name"
                  :value="service.i_service_type"
                />
              </el-select>
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Priority">
              <el-select
                v-model="currentSLA.priority"
                placeholder="Select priority"
                class="w-100"
              >
                <el-option
                  v-for="priority in slaPriorities"
                  :key="priority"
                  :label="priority"
                  :value="priority"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input
              label="Resolution Time (Hours)"
              type="number"
              v-model="currentSLA.resolutionTimeHours"
              :required="true"
              step=".01"
              min="0.01"
              placeholder="Enter resolution time"
            />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update SLA" : "Add SLA" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeSLAForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- SLA List -->
    <card>
      <template slot="header">
        <div class="d-flex justify-content-between align-items-center">
          <h4 class="card-title">Service Level Agreements</h4>
          <div>
            <button class="btn btn-sm btn-primary" @click="showAddSLAForm">
              <i class="fa fa-plus"></i> Add SLA
            </button>
            <button class="btn btn-sm btn-success ml-2" @click="navigateToEscalations">
              <i class="fa fa-cog"></i> Escalations
            </button>
          </div>
        </div>
        <p class="card-category">Manage service level agreements</p>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search records"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <div v-if="loading" class="loader-container">
            <div class="loader"></div>
            <p>Loading data...</p>
          </div>
          <div v-else-if="slas.length === 0" class="text-center py-5">
            <p>No SLAs found. Click "Add SLA" to create one.</p>
          </div>
          <el-table
            v-else
            :data="queriedData"
            stripe
            border
            style="width: 100%"
            v-loading="tableLoading"
            element-loading-text="Loading data..."
            :default-sort="{prop: 'serviceId', order: 'ascending'}"
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
              sortable
            ></el-table-column>


            <!-- Actions Column -->
            <el-table-column label="Actions" width="280" >
              <template v-slot="{ row }">
                <button class="btn btn-info btn-sm mr-1" @click="editSLA(row)" title="Edit SLA">
                  <i class="fa fa-pencil"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm mr-1"
                  @click="deleteSLA(row.slaid)"
                  title="Delete SLA"
                >
                  <i class="fa fa-trash"></i>
                </button>
                <button
                  class="btn btn-success btn-sm"
                  @click="navigateToServiceEscalations(row.serviceId)"
                  title="Manage Escalations"
                >
                  <i class="fa fa-cog"></i> 
                </button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input } from "element-ui";
import API from "@/services/api"; // Importing this.slaService service

export default {
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input
  },

  data() {
    return {
      loading: true,
      tableLoading: false,
      slaService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "slas" // Endpoint
      ),
      customerService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "customers" // Endpoint
      ),
      servsService: new API(
        process.env.VUE_APP_API_URL, // Base URL
        "services" // Endpoint
      ),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      tableColumns: [
        { prop: "serviceId", label: "Service", formatter: (row) => this.getServiceById(row.serviceId), minWidth: 120 },
        { prop: "priority", label: "Priority", minWidth: 120 },
        {
          prop: "resolutionTimeHours",
          label: "Resolution Time (Hours)",
          minWidth: 160,
        },
      ],
      slaPriorities: ["Low", "Medium", "High", "Critical"], // Priority options
      slas: [], // Data to display
      services: [],
      currentSLA: {
        slaid: null,
        serviceId: "",
        priority: "Medium",
        resolutionTimeHours: 0,
      },
      customers: [],
      showSLAForm: false,
      isEditing: false,
    };
  },
  computed: {
    filteredData() {
      if (!this.searchQuery) {
        return this.slas;
      }

      const query = this.searchQuery.toLowerCase();
      return this.slas.filter(sla => {
        // Check service name
        const serviceName = this.getServiceById(sla.serviceId).toLowerCase();
        if (serviceName.includes(query)) return true;

        // Check priority
        if (sla.priority && sla.priority.toLowerCase().includes(query)) return true;

        // Check resolution time
        if (sla.resolutionTimeHours && sla.resolutionTimeHours.toString().includes(query)) return true;

        return false;
      });
    },
    queriedData() {
      const startIndex = (this.pagination.currentPage - 1) * this.pagination.perPage;
      const endIndex = startIndex + this.pagination.perPage;
      return this.filteredData.slice(startIndex, endIndex);
    },
    total() {
      return this.filteredData.length;
    },
    to() {
      return Math.min(this.from + this.pagination.perPage, this.total);
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    getCustomerById(id) {
      const customer = this.customers.find((c) => c.customerId === id);
      return customer ? customer.name : "Unknown Customer"; // Default to "Unknown Customer" if not found
    },

    // Function to get service by ID
    getServiceById(id) {
      const service = this.services.find((s) => s.i_service_type === id);
      return service ? service.name : "Unknown Service"; // Default to "Unknown Service" if not found
    },

    async fetchSLAs() {
      try {
        this.tableLoading = true;
        const response = await this.slaService.getAll();

        if (Array.isArray(response)) {
          this.slas = response;
          this.pagination.total = response.length;
        } else {
          //console.error("Invalid SLA response format:", response);
          this.slas = [];
          this.pagination.total = 0;
        }
        return response;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch SLAs");
        //console.error("Error fetching SLAs:", error);
        this.slas = [];
        this.pagination.total = 0;
        throw error;
      } finally {
        this.tableLoading = false;
      }
    },

    async submitSLA() {
      if (this.isEditing) {
        await this.updateSLA();
      } else {
        await this.addSLA();
      }
    },

    async addSLA() {
      try {
        this.tableLoading = true;
        const newObject = { ...this.currentSLA };
        delete newObject.slaid; // Remove ID for new SLA

        await this.slaService.create(newObject); // Call this.slaService to create a new SLA
        this.$alert.success("SLA added successfully");
        await this.fetchSLAs(); // Refresh the list after adding
        this.closeSLAForm();
      } catch (error) {
        this.$alert.error(error.response?.data?.message || "Failed to add SLA");
      } finally {
        this.tableLoading = false;
      }
    },

    async updateSLA() {
      try {
        this.tableLoading = true;
        await this.slaService.update(this.currentSLA.slaid, this.currentSLA); // Call this.slaService to update existing SLA
        await this.fetchSLAs(); // Refresh the list after updating
        this.$alert.success("SLA updated successfully");
        this.closeSLAForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update SLA"
        );
      } finally {
        this.tableLoading = false;
      }
    },

    editSLA(sla) {
      this.currentSLA = { ...sla };
      this.isEditing = true;
      this.showSLAForm = true;
    },

    async deleteSLA(slaid) {
      try {
        const confirmDelete = await this.$alert.confirm(
          "Are you sure you want to delete this SLA?"
        );
        if (confirmDelete.isConfirmed) {
          this.tableLoading = true;
          await this.slaService.delete(slaid); // Call this.slaService to delete the SLA
          await this.fetchSLAs(); // Refresh the list after deletion
          this.$alert.success("SLA deleted successfully");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to delete SLA"
        );
      } finally {
        if (this.tableLoading) {
          this.tableLoading = false;
        }
      }
    },

    closeSLAForm() {
      this.isEditing = false;
      this.showSLAForm = false;
      // Reset current SLA object
      this.currentSLA = {
        slaid: null,
        customerId: "",
        serviceId: "",
        priority: "Medium",
        resolutionTimeHours: 0,
      };
    },

    showAddSLAForm() {
      this.isEditing = false;
      this.showSLAForm = true;
      this.currentSLA = {
        slaid: null,
        customerId: "",
        serviceId: "",
        priority: "Medium",
        resolutionTimeHours: 0,
      };
    },

    navigateToEscalations() {
      this.$router.push('/technical/escalations');
    },

    navigateToServiceEscalations(serviceId) {
      // Navigate to escalations page with service ID as query parameter
      this.$router.push({
        path: '/technical/escalations',
        query: { serviceId: serviceId }
      });
    },

    async loadCustomers() {
      try {
        this.customers = await this.customerService.getAll();
      } catch (error) {
        //console.error("Error fetching customers:", error);
        this.$alert.error("Failed to load customers");
      }
    },

    async loadServices() {
      try {
        const response = await this.servsService.getAll();
        this.services = response.services || [];
      } catch (error) {
        //console.error("Error fetching services:", error);
        this.$alert.error("Failed to load services");
      }
    },
  },
  async created() {
    this.loading = true;
    try {
      // Use Promise.all to fetch data in parallel
      await Promise.all([
        this.fetchSLAs(),
        //this.loadCustomers(),
        this.loadServices()
      ]);
    } catch (error) {
      //console.error("Error loading data:", error);
      this.$alert.error("Failed to load data. Please try again.");
    } finally {
      this.loading = false;
    }
  },
};
</script>

<style>
/* Loader Styles */
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loader {
  border: 6px solid #f3f3f3;
  border-top: 6px solid #302e77;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

