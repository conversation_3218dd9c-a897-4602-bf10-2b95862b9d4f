<template>
  <div class="issue-timeline-container">
    <el-card class="timeline-wrapper">
      <div slot="header" class="clearfix">
        <div>
          <span class="font-bold text-lg">
            <b>{{ issues.title }}</b> |
            <span class="text-danger">{{ duedate ?? "No due date" }}</span>
          </span>
          <div>
            <i>{{ issues.description }}</i>
          </div>
          <div>
            <i>{{ issues.phone }} - {{ issues.email }}</i>
          </div>
          <div v-if="issues.assignedDepartmentId">
            Assigned to: {{ issues.assignedDepartmentName }} |
            {{ formatDate(issues.assignedDate) }}
          </div>
          <router-link
            icon="el-icon-plus"
            size="small"
            class="btn btn-primary mt-3"
            :to="`/admin/issue-logs/${$route.params.id}`"
          >
            Update log
          </router-link>
          <router-link
            icon="el-icon-plus"
            size="small"
            class="float-right btn btn-success mt-n5"
            to="/admin/timeline"
          >
            Issues - timeline
          </router-link>
        </div>
      </div>
      <el-timeline>
        <el-timeline-item
          placement="top"
          v-for="issue in issueLogs"
          :key="issue.id"
          :timestamp="formatDate(issue.updatedAt)"
          :type="getIssueStatusColor(issue.status)"
        >
          <div class="card issue-card">
            <div
              class="card-body d-flex justify-content-between align-items-center"
            >
              <div>
                <p class="text-gray-500">
                  <b>Action Taken:</b> {{ issue.actionTaken }}
                </p>
                <p class="text-gray-500"><b>Comment:</b> {{ issue.comment }}</p>
                <p class="text-gray-500">
                  <b>Updated By:</b> {{ getUserName(issue) }}
                </p>
              </div>
              <el-tag
                :type="getIssueStatusColor(issue.status)"
                size="medium"
                class="float-right"
                bordered
              >
                {{ issue.status }}
              </el-tag>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script>
import { Card, Button, Timeline, TimelineItem, Tag } from "element-ui";
import API from "@/services/api";
import moment from "moment";

export default {
  name: "IssueTimeline",
  computed: {
    isLogValid() {
      return this.newLogType && this.newLogDescription.trim() !== "";
    },
  },
  components: {
    ElCard: Card,
    ElTimeline: Timeline,
    ElTimelineItem: TimelineItem,
    ElTag: Tag,
  },
  data() {
    return {
      timelineLogs: [],
      issuesService: new API(process.env.VUE_APP_API_URL, "issues"),
      issuesLogService: new API(process.env.VUE_APP_API_URL, "issuelogs"),
      usersLogService: new API(process.env.VUE_APP_API_URL, "users"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      issues: {},
      users: [],
      issueLogs: [],
      selectedIssue: null,
      selectedIssueLogs: [],
      showIssueDetailsDialog: false,
      duedate: null,
      newLogType: "",
      newLogDescription: "",
      departments: [],
      logTypes: ["Investigation", "Update", "Action", "Resolution"],
      issueStatuses: ["Open", "In Progress", "Resolved", "Closed"],
    };
  },
  methods: {
    async getDepartment(deptId) {
      let department = this.departments?.find((a) => a.departmentId == deptId);
      return department
        ? `${department.hod}(${department.name})`
        : "Unknown Department";
    },
    formatDate(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm A");
    },
    getUserName(row) {
      if (!row) return "Unknown";
      let user = this.users.find((x) => x.id == row.updatedBy);
      if (!user) return "Unknown User";
      let department = this.departments.find(
        (x) => x.departmentId == user.departmentId
      );
      return department
        ? `${user.fullName} [${department.name}]`
        : `${user.fullName} [Unknown Department]`;
    },
    async fetchIssuesLogs() {
      try {
        const response = await this.issuesLogService.getAll();
        const issueId = this.$route.params.id;
        this.issueLogs = response.filter((x) => x.issueId == issueId);
      } catch (error) {
        console.log(error);
        this.$alert.error(
          error.response?.message || "Failed to fetch Issues Logs"
        );
      }
    },
    async fetchAssignments() {
      try {
        const response = await this.assignmentService.getAll();
        const issueId = this.$route.params.id;
        const assignment = response.find((x) => x.issueId == issueId);
        if (assignment) {
          this.duedate = this.formatDate(assignment.duedate);
        } else {
          this.duedate = "No due date assigned";
        }
      } catch (error) {
        console.log(error, "jhdhjdhj");
        this.$alert.error(
          error.response?.message || "Failed to fetch Issues Logs"
        );
      }
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = response;
      } catch (error) {
        console.log(error);
        this.$alert.error(
          error.response?.message || "Failed to fetch departments"
        );
      }
    },
    async fetchUsers() {
      try {
        const response = await this.usersLogService.getAll();
        this.users = response;
      } catch (error) {
        console.log(error);
        this.$alert.error(error.response?.message || "Failed to fetch users");
      }
    },
    async fetchIssues() {
      try {
        const issueId = this.$route.params.id;
        const response = await this.issuesService.getById(issueId);
        this.issues = response;

        // Resolve the department name and add it to the issues object
        if (this.issues.assignedDepartmentId) {
          this.issues.assignedDepartmentName = await this.getDepartment(
            this.issues.assignedDepartmentId
          );
        }
      } catch (error) {
        console.log(error);
        this.$alert.error(error.response?.message || "Failed to fetch issues");
      }
    },
    getIssueStatusColor(status) {
      const colorMap = {
        Open: "warning",
        "In Progress": "primary",
        Resolved: "success",
        Closed: "info",
      };
      return colorMap[status] || "warning";
    },
    getLogStatusColor(type) {
      const colorMap = {
        Investigation: "warning",
        Update: "primary",
        Action: "danger",
        Resolution: "success",
      };
      return colorMap[type] || "info";
    },
    getActiveStep(issue) {
      if (!issue) return 0;
      const statuses = ["Open", "In Progress", "Resolved", "Closed"];
      return statuses.indexOf(issue.status);
    },
    updateIssueStatus() {
      if (!this.selectedIssue) return;
      const currentIndex = this.issueStatuses.indexOf(
        this.selectedIssue.status
      );
      const nextStatusIndex = (currentIndex + 1) % this.issueStatuses.length;
      this.selectedIssue.status = this.issueStatuses[nextStatusIndex];
    },
  },
  async mounted() {
    await this.fetchIssues();
    await this.fetchIssuesLogs();
    await this.fetchDepartments();
    await this.fetchUsers();
    await this.fetchAssignments();
  },
};
</script>

<style scoped>
.issue-timeline-container {
  margin: auto;
  padding: 1rem;
}
.issue-card {
  margin-bottom: 10px;
  transition: box-shadow 0.3s;
}
.issue-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
</style>