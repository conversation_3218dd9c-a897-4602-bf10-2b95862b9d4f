<template>
  <div class="issues-log-management">
    <!-- Issues Log Form -->
    <card v-if="showIssuesLogForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">Resolve Issue [{{ this.$route.params.id }}]</h4>
      </template>
      <form @submit.prevent="submitIssuesLog">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Issue Title" readonly>
              <input
                v-model="currentIssuesLog.issueTitle"
                type="text"
                class="form-control"
                placeholder="Enter issue title"
                readonly
              />
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Status" required>
              <el-select
                v-model="currentIssuesLog.status"
                placeholder="Select status"
                class="w-100"
              >
                <el-option
                  v-for="status in issueStatuses"
                  :key="status"
                  :label="status"
                  :value="status"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Action Taken" required>
              <textarea
                v-model="currentIssuesLog.actionTaken"
                class="form-control"
                rows="4"
                placeholder="Enter action taken"
              ></textarea>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Issue Description" readonly>
              <textarea
                v-model="currentIssuesLog.issueDescription"
                class="form-control"
                rows="4"
                placeholder="Enter issue description"
                readonly
              ></textarea>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <fg-input label="comment">
              <textarea
                v-model="currentIssuesLog.comment"
                class="form-control"
                rows="4"
                placeholder="Enter comment"
              ></textarea>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              Update action taken
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeIssuesLogForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Issues Log List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6">
            <h4 class="card-title">
              Issue Logs [{{ currentIssuesLog.issueTitle }}]
            </h4>
          </div>
          <div class="col-6 text-right">
            <button
              class="btn btn-primary mx-2"
              @click="showIssuesLogForm = true"
            >
              Update Issues Log
            </button>
            <router-link
              class="btn btn-success mx-2"
              to="/call-center/issues"
            >
              Issues
            </router-link>
          </div>
        </div>
      </template>

      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search records"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table :data="queriedData" stripe border style="width: 100%">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            ></el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input } from "element-ui";
import API from "@/services/api";

export default {
  name: "IssuesLogManagement",
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
  },
  data() {
    return {
      // Services for different APIs
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      issuesLogService: new API(process.env.VUE_APP_API_URL, "issuelogs"),
      usersLogService: new API(process.env.VUE_APP_API_URL, "users"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      issue: {},
      issueStatuses: ["In Progress", "On Hold", "Resolved", "Closed"],
      // Search and Pagination
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      users: [],
      departments: [],
      // Table Columns with Formatters
      tableColumns: [
        {
          prop: "logId",
          label: "Action ID",
          minWidth: 100,
        },
        {
          prop: "status",
          label: "Status",
          minWidth: 120,
        },

        {
          prop: "actionTaken",
          label: "Action Taken",
          minWidth: 200,
        },

        {
          prop: "comment",
          label: "Comment",
          minWidth: 200,
        },
        {
          prop: "updatedBy",
          label: "UpdatedBy",
          minWidth: 200,
          formatter: this.getUserName,
        },
        {
          prop: "updatedAt",
          label: "UpdatedAt",
          minWidth: 180,
          formatter: this.formatDateTime,
        },
      ],

      recentLogs:[],
      // Data Arrays
      issuesLogs: [],

      // Current Issues Log for Form
      currentIssuesLog: {
        issuesLogId: null,
        issueTitle: "",
        issueDescription: "",
        actionTaken: "",
        status: "",
        comment: "",
      },

      // Form Control
      showIssuesLogForm: false,
      isEditing: false,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.recentLogs?.filter((issuesLog) =>
        Object.values(issuesLog).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered?.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.recentLogs.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    // Formatting Methods for Table
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },
    getUserName(row, column, cellValue) {
      if (!cellValue) return "";
      let user = this.users.find((x) => x.id == row.updatedBy);
      let department = this.departments.find((x) => x.departmentId  == user.departmentId );
      return `${user.fullName} [${department.name}]`;
    },
    async loadIssueDetails() {
      // Extract the issue ID from the URL
      const issueId = this.$route.params.id; // Assuming you are using Vue Router

      if (!issueId) return;

      try {
        // Load the selected issue details
        const issueResponse = await this.issueService.getById(issueId);

        this.currentIssuesLog.issueTitle = issueResponse.title;
        this.currentIssuesLog.issueDescription = issueResponse.description;
        this.currentIssuesLog.issueId = issueId;
        this.currentIssuesLog.updatedBy = localStorage.getItem("userId");

        // Load related logs for the selected issue
       this.fetchIssuesLogs();

        // Reset form fields for action taken and comment
        this.currentIssuesLog.actionTaken = "";
        this.currentIssuesLog.status = "";
        this.currentIssuesLog.comment = "";
      } catch (error) {
        this.$alert.error("Failed to load issue details or logs");
      }
    },

    // Fetch Methods
    async fetchIssuesLogs() {
      try {
        const issueId = this.$route.params.id; 
        const logsResponse = await this.issuesLogService.getAll();
        this.recentLogs =
          logsResponse?.filter((x) => x.issueId == issueId) ?? [];
        this.pagination.total = this.recentLogs.length;
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to fetch Issues Logs"
        );
      }
    },

    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          searchQuery: this.searchQuery,
        });
        this.departments = response;
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to fetch departments"
        );
      }
    },

    async fetchUsers() {
      try {
        const response = await this.usersLogService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          searchQuery: this.searchQuery,
        });
        this.users = response;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch users");
      }
    },
    // Form Methods
    async submitIssuesLog() {
      try {
        await this.issuesLogService.create(this.currentIssuesLog);
        await this.fetchIssuesLogs();
        this.closeIssuesLogForm();
        this.$alert.success("Issues Log saved successfully");
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to save Issues Log"
        );
      }
    },

    closeIssuesLogForm() {
      this.isEditing = false;
      this.showIssuesLogForm = false;
      this.currentIssuesLog = {
        issuesLogId: null,
        issueId: null,
        issueTitle: "",
        issueDescription: "",
        actionTaken: "",
        status: "",
        comment: "",
      };
    },
  },
  mounted() {
    this.fetchIssuesLogs();
    this.loadIssueDetails();
    this.fetchDepartments()
    this.fetchUsers();
  },
};
</script>

<style scoped>
/* Add your scoped styles here */
</style>