<template>
  <div class="timeline-container">
    <card class="mr-3">
      <div class="card-title">
        <div class="d-flex align-items-center row">
          <div class="col-8"><h4 class="card-title">Issues Timeline</h4></div>
          <div class="col-4 text-right">
            <router-link class="btn btn-primary mx-2" to="/technical/issues">
              Add Issue
            </router-link>

            <router-link class="btn btn-success mx-2" to="/technical/issues">
              Issues
            </router-link>
          </div>
        </div>
      </div>
      <div class="filters container mb-4 p-3 bg-light rounded">
        <div class="row gy-3">
          <div class="col-md-3 col-6">
            <label for="department-filter" class="form-label"
              >Department:</label
            >
            <el-select
              v-model="filters.department"
              id="department-filter"
              placeholder="Select Department"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option
                v-for="dept in availableDepartments"
                :key="dept"
                :value="dept"
                :label="dept"
              ></el-option>
            </el-select>
          </div>
          <div class="col-md-3 col-6">
            <label for="status-filter" class="form-label">Status:</label>
            <el-select
              v-model="filters.status"
              id="status-filter"
              placeholder="Select Status"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option
                v-for="status in availableStatuses"
                :key="status"
                :value="status"
                :label="status"
              ></el-option>
            </el-select>
          </div>

          <!-- Organization Filter -->
          <div class="col-md-3 col-6">
            <label for="organization-filter" class="form-label">Client:</label>
            <el-select
              v-model="filters.organization"
              id="organization-filter"
              placeholder="Select Organization"
              class="w-100"
              filterable
            >
              <el-option value="" label="All"></el-option>
              <el-option
                v-for="org in availableOrganizations"
                :key="org"
                :value="org"
                :label="org"
              ></el-option>
            </el-select>
          </div>

          <!-- Time Filter -->
          <div class="col-md-3 col-6">
            <label for="time-filter" class="form-label">Time:</label>
            <el-select
              v-model="filters.time"
              id="time-filter"
              placeholder="Select Time"
              class="w-100"
            >
              <el-option value="" label="All"></el-option>
              <el-option value="today" label="Today"></el-option>
              <el-option value="past-week" label="Past Week"></el-option>
              <el-option value="past-month" label="Past Month"></el-option>
            </el-select>
          </div>

          <!-- Search Filter -->
          <div class="col-12 pt-3">
            <label for="search-filter" class="form-label">Search:</label>
            <FgInput
              v-model="filters.search"
              id="search-filter"
              placeholder="Search by issue ID, assignee, or organization"
              class="w-100"
            />
          </div>
        </div>
      </div>

      <div class="issue-counter text-left mb-3 ml-3">
        <p>
          <b>{{ filteredIssues.length }} Issues Found</b>
        </p>
      </div>
    </card>

    <el-timeline class="ml-n5 mr-3">
      <el-timeline-item
        v-for="issue in issuesWithDowntime"
        :key="issue.issueId"
        :timestamp="formatDate(issue.reportedAt)"
        placement="top"
        :type="getIssueStatusColor(issue.status)"
      >
        <router-link :to="`/technical/issue-view/${issue.issueId}`">
          <div class="card mb-3">
            <div class="card-body">
              <!-- Header: Issue Ref and Status -->
              <div
                class="d-flex justify-content-between align-items-center mb-3"
              >
                <span class="fw-bold">
                  <strong>Ref:</strong> {{ issue.issueRef }}
                </span>
                <el-tag
                  :type="getIssueStatusColor(issue.status)"
                  size="medium"
                  class="float-right"
                  bordered
                >
                  {{ issue.status }}
                </el-tag>
              </div>

              <!-- Issue Details -->
              <div class="row">
                <!-- Left Column -->
                <div class="col-md-6">
                  <div class="mb-2">
                    <span class="text-muted"><strong>Issue:</strong></span>
                    <span class="ms-2">{{ issue.title }}</span>
                  </div>

                  <div class="mb-2">
                    <span class="text-muted"><strong>Client:</strong></span>
                    <span class="ms-2"
                      >{{ issue.organization }} [{{ issue.account }}]</span
                    >
                  </div>

                  <div class="mb-2">
                    <span class="text-muted"
                      ><strong>Assigned to:</strong></span
                    >
                    <span class="ms-2">
                      {{ getDepartment(issue.assignedDepartmentId) }} [{{
                        issue.assignedDate
                      }}]
                    </span>
                  </div>

                  <div class="mb-2">
                    <span class="text-muted"><strong>Downtime:</strong></span>
                    <span class="ms-2">{{ issue.downtime }}</span>
                  </div>

                  <div class="mb-2">
                    <span class="text-muted"
                      ><strong>Description:</strong></span
                    >
                    <span class="ms-2">{{ issue.description }}</span>
                  </div>
                </div>

                <!-- Right Column -->
                <div class="col-md-6">
                  <div class="mb-2">
                    <span class="text-muted"><strong>Location:</strong></span>
                    <span class="ms-2">{{ issue.location }}</span>
                  </div>

                  <div class="mb-2">
                    <span class="text-muted"><strong>Contact(s):</strong></span>
                    <span class="ms-2"
                      >{{ issue.phone }} - {{ issue.email }}</span
                    >
                  </div>

                  <div class="mb-2">
                    <span class="text-muted"><strong>SLA:</strong></span>
                    <span class="ms-2">{{ issue.sla }} hour(s)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </router-link>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>
<script>
import { FormGroupInput as FgInput } from "src/components/index";
import { Select, Option, Timeline, Tag, TimelineItem } from "element-ui";
import API from "@/services/api";
import moment from "moment";

export default {
  components: {
    FgInput,
    "el-select": Select,
    ElTimeline: Timeline,
    ElTimelineItem: TimelineItem,
    "el-option": Option,
    ElTag: Tag,
  },
  data() {
    return {
      issuesService: new API(process.env.VUE_APP_API_URL, "Issues/all-issues"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      issues: [],
      departments: [],
      department: "",
      filters: {
        status: "",
        organization: "",
        time: "",
        search: "",
      },
    };
  },
  computed: {
    issuesWithDowntime() {
      return this.filteredIssues.map((issue) => {
        if (issue.status !== "Closed" && issue.status !== "Resolved") {
          const reportedTime = new Date(issue.reportedAt);
          const now = new Date();
          const downtimeMs = now - reportedTime;
          const downtimeHours = Math.floor(downtimeMs / (1000 * 60 * 60));
          return {
            ...issue,
            downtime: downtimeHours + " hours",
          };
        }
        return { ...issue, downtime: "N/A" };
      });
    },
    availableStatuses() {
      return [...new Set(this.issues.map((issue) => issue.status))];
    },
    availableOrganizations() {
      return [...new Set(this.issues.map((issue) => issue.organization))];
    },
    availableDepartments() {
      return [
        ...new Set(
          this.issues
            .map((issue) => this.getDepartment(issue.assignedDepartmentId))
            .filter((dept) => dept)
        ),
      ];
    },

    filteredIssues() {
      return this.issues.filter((issue) => {
        const matchesStatus =
          !this.filters.status || issue.status === this.filters.status;
        const matchesOrganization =
          !this.filters.organization ||
          issue.organization === this.filters.organization;
        const matchesTime = this.applyTimeFilter(issue.reportedAt);
        const matchesSearch = this.applySearchFilter(issue);
        const matchesDepartment =
          !this.filters.department ||
          this.getDepartment(issue.assignedDepartmentId) ===
            this.filters.department;

        return (
          matchesStatus &&
          matchesOrganization &&
          matchesTime &&
          matchesSearch &&
          matchesDepartment
        );
      });
    },
  },
  methods: {
    getDepartment(departmentId) {
      const department = this.departments?.find(
        (d) => d.departmentId == departmentId
      );
      return department
        ? `${department.name}${department.hod ? "-" + department.hod : ""}`
        : "Unassigned";
    },
    async fetchDepartments() {
      try {
        this.departments = await this.departmentService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },
    getIssueStatusColor(status) {
      const colorMap = {
        Open: "warning",
        "In Progress": "primary",
        Resolved: "success",
        Closed: "info",
      };
      return colorMap[status] || "warning";
    },
    // Existing methods...

    async fetchIssues() {
      try {
        const response = await this.issuesService.getAll();

        const uniqueIssues = response.reduce((acc, issue) => {
          if (!acc.has(issue.issueRef)) {
            acc.set(issue.issueRef, issue); // Add to Map if issueRef is unique
          }
          return acc;
        }, new Map());

        // Convert Map values back to an array
        this.issues = Array.from(uniqueIssues.values());
      } catch (error) {
        console.error("Error fetching issues:", error);
      }
    },

    applyTimeFilter(createdAt) {
      if (!this.filters.time) return true;

      const now = new Date();
      const createdDate = new Date(createdAt);

      switch (this.filters.time) {
        case "today":
          const today = new Date(
            now.getFullYear(),
            now.getMonth(),
            now.getDate()
          );
          const createdDay = new Date(
            createdDate.getFullYear(),
            createdDate.getMonth(),
            createdDate.getDate()
          );
          return today.getTime() === createdDay.getTime();

        case "past-week": {
          const weekAgo = new Date();
          weekAgo.setDate(now.getDate() - 7);
          weekAgo.setHours(0, 0, 0, 0);
          return createdDate >= weekAgo;
        }

        case "past-month": {
          const monthAgo = new Date();
          monthAgo.setMonth(now.getMonth() - 1);
          monthAgo.setHours(0, 0, 0, 0);
          return createdDate >= monthAgo;
        }

        default:
          return true;
      }
    },

    applySearchFilter(issue) {
      if (!this.filters.search) return true;

      const searchTerm = this.filters.search.toLowerCase().trim();

      // Check all searchable fields
      const searchableValues = [
        issue.issueId.toString(),
        issue.details?.toLowerCase(),
        issue.organization?.toLowerCase(),
        issue.title?.toLowerCase(),
        issue.status?.toLowerCase(),
      ];

      // Return true if any field includes the search term
      return searchableValues.some(
        (value) => value && value.includes(searchTerm)
      );
    },

    formatDate(datestring) {
      console.log(datestring);
      return moment(datestring).format("YYYY-MM-DD hh:mm A");
    },

    getStatusClass(issue) {
      const now = new Date();
      const createdAt = new Date(issue.createdAt);
      const diffInMs = now - createdAt;
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      if (issue.status === "Resolved") return "status-resolved";
      if (diffInMinutes >= issue.sla) return "status-overdue";
      if (diffInMinutes >= issue.sla * 0.8) return "status-near-sla";
      return "status-in-progress";
    },

    getStatusTextClass(issue) {
      const statusClass = this.getStatusClass(issue);
      const classMap = {
        "status-resolved": "text-green-600",
        "status-overdue": "text-red-600",
        "status-near-sla": "text-yellow-600",
        "status-in-progress": "text-blue-600",
      };
      return classMap[statusClass];
    },

    // getDepartment(issueId) {
    //   let department = this.departments?.find((a) => a.departmentId == issueId);
    //   return `${department?.hod}(${department?.name})`;
    // },

    getStatusIcon(issue) {
      const now = new Date();
      const createdAt = new Date(issue.createdAt);
      const diffInMs = now - createdAt;
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      // Return Font Awesome icons based on status
      if (issue.status === "Resolved") {
        return '<i class="fas fa-check-circle fa-lg text-green-500"></i>';
      }
      if (diffInMinutes >= issue.sla) {
        return '<i class="fas fa-exclamation-triangle fa-lg text-white"></i>';
      }
      if (diffInMinutes >= issue.sla * 0.8) {
        return '<i class="fas fa-exclamation-circle fa-lg text-white"></i>';
      }
      return '<i class="fas fa-clock fa-lg text-white"></i>';
    },
  },
  mounted() {
    this.fetchIssues();
    this.fetchDepartments();
  },
};
</script>

<style scoped>
.timeline-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.timeline-line {
  width: 2px;
  height: 100%;
  background-color: #ddd;
  margin: auto;
}

.timeline-content {
  max-width: 100%;
}

.timeline-container {
  margin: 0 auto;
  font-family: "Arial", sans-serif;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  color: #333;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.timeline-item:hover {
  transform: scale(1.02);
}

.status-resolved {
  background-color: #10b981;
}
.status-overdue {
  background-color: #ef4444;
}
.status-near-sla {
  background-color: #fbbf24;
}
.status-in-progress {
  background-color: #3b82f6;
}
</style>