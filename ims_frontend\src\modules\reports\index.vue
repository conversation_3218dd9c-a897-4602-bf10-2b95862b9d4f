<template>
  <div class="reports-container py-3">
    <card>
      <template slot="header">
        <h4 class="card-title">Reports</h4>
        <p class="card-category">Generate and export reports for your organization</p>
      </template>

      <!-- Report Type Selection -->
      <div class="row mb-4">
        <div class="col-md-6">
          <label>Report Type</label>
          <el-select
            v-model="selectedReportType"
            placeholder="Select Report Type"
            class="w-100"
            @change="handleReportTypeChange">
            <el-option
              v-for="option in reportTypes"
              :key="option.value"
              :label="option.label"
              :value="option.value">
            </el-option>
          </el-select>
        </div>
        <div class="col-md-6">
          <label>Date Range</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            class="w-100"
            @change="handleDateRangeChange">
          </el-date-picker>
        </div>
      </div>

      <!-- Additional Filters -->
      <div class="row mb-4">
        <div class="col-md-4" v-if="showDepartmentFilter">
          <label>Department</label>
          <el-select
            v-model="selectedDepartment"
            placeholder="Select Department"
            class="w-100"
            clearable>
            <el-option
              v-for="dept in departments"
              :key="dept.departmentId"
              :label="dept.name"
              :value="dept.departmentId">
            </el-option>
          </el-select>
        </div>
        <div class="col-md-4" v-if="showServiceFilter">
          <label>Service</label>
          <el-select
            v-model="selectedService"
            placeholder="Select Service"
            class="w-100"
            clearable>
              <el-option
                    v-for="service in services"
                    :key="service.i_service_type"
                    :label="`${service.name} - ${service.i_service_type}`"
                    :value="service.i_service_type"
                  />
          </el-select>
        </div>
        <div class="col-md-4" v-if="showStatusFilter">
          <label>Status</label>
          <el-select
            v-model="selectedStatus"
            placeholder="Select Status"
            class="w-100"
            clearable>
            <el-option
              v-for="status in statuses"
              :key="status"
              :label="status"
              :value="status">
            </el-option>
          </el-select>
        </div>
        <div class="col-md-4" v-if="showUserFilter">
          <label>User</label>
          <el-select
            v-model="selectedUser"
            placeholder="Select User"
            class="w-100"
            clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.fullName"
              :value="user.id">
            </el-option>
          </el-select>
        </div>
      </div>

      <!-- Generate Report Button -->
      <div class="row mb-4">
        <div class="col-12">
          <button
            class="btn btn-primary"
            @click="generateReport"
            :disabled="loading">
            <i class="fas fa-sync fa-spin mr-2" v-if="loading"></i>
            Generate Report
          </button>
          <button
            class="btn btn-success ml-2"
            @click="exportReport"
            :disabled="loading || !reportData.length">
            <i class="fas fa-file-export mr-2"></i>
            Export
          </button>
        </div>
      </div>

      <!-- Report Content -->
      <div v-if="loading" class="text-center py-5">
        <i class="fas fa-spinner fa-spin fa-3x"></i>
        <p class="mt-3">Loading report data...</p>
      </div>

      <div v-else-if="reportData.length === 0 && reportGenerated" class="text-center py-5">
        <i class="fas fa-exclamation-circle fa-3x text-warning"></i>
        <p class="mt-3">No data available for the selected filters.</p>
      </div>

      <div v-else-if="reportData.length > 0">
        <!-- Performance Report -->
        <div v-show="selectedReportType === 'performance'" class="report-content">
          <h5 class="mb-4">Performance Metrics</h5>

          <!-- Performance Charts -->
          <div class="row mb-4">
            <div class="col-md-6">
              <card>
                <div id="resolution-time-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
            <div class="col-md-6">
              <card>
                <div id="sla-compliance-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
          </div>

          <!-- Performance Data Table -->
          <el-table :data="reportData" border style="width: 100%">
            <el-table-column
              v-for="column in performanceColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth">
            </el-table-column>
          </el-table>
        </div>

        <!-- Issue Report -->
        <div v-show="selectedReportType === 'issues'" class="report-content">
          <h5 class="mb-4">Issue Report</h5>

          <!-- Issue Charts -->
          <div class="row mb-4">
            <div class="col-md-6">
              <card>
                <div id="issues-by-status-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
            <div class="col-md-6">
              <card>
                <div id="issues-by-priority-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
          </div>

          <!-- Issues Data Table -->
          <el-table :data="reportData" border style="width: 100%">
            <el-table-column
              v-for="column in issueColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth">
            </el-table-column>
          </el-table>
        </div>

        <!-- SLA Compliance Report -->
        <div v-show="selectedReportType === 'sla'" class="report-content">
          <h5 class="mb-4">SLA Compliance Report</h5>

          <!-- SLA Charts -->
          <div class="row mb-4">
            <div class="col-md-6">
              <card>
                <div id="sla-compliance-by-service-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
            <div class="col-md-6">
              <card>
                <div id="resolution-vs-target-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
          </div>

          <!-- SLA Data Table -->
          <el-table :data="reportData" border style="width: 100%">
            <el-table-column
              v-for="column in slaColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth">
              <template slot-scope="scope" v-if="column.prop === 'complianceStatus'">
                <el-tag
                  :type="scope.row.complianceStatus === 'Good' ? 'success' :
                         scope.row.complianceStatus === 'Average' ? 'warning' : 'danger'">
                  {{ scope.row.complianceStatus }}
                </el-tag>
              </template>
              <template slot-scope="scope" v-else-if="column.prop === 'slaComplianceRate'">
                <span>{{ scope.row.slaComplianceRate }}%</span>
              </template>
              <template slot-scope="scope" v-else-if="column.prop === 'avgVsTargetPercentage'">
                <span>{{ scope.row.avgVsTargetPercentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- User Activity Report -->
        <div v-show="selectedReportType === 'activity'" class="report-content">
          <h5 class="mb-4">User Activity Report</h5>

          <!-- Activity Charts -->
          <div class="row mb-4">
            <div class="col-md-6">
              <card>
                <div id="activity-by-user-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
            <div class="col-md-6">
              <card>
                <div id="activity-by-type-chart" class="chart-container" style="height: 300px;">
                  <div v-if="loading" class="chart-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading chart...</p>
                  </div>
                </div>
              </card>
            </div>
          </div>

          <!-- Activity Data Table -->
          <el-table :data="reportData" border style="width: 100%">
            <el-table-column
              v-for="column in activityColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth">
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Export Options Modal -->
      <el-dialog
        title="Export Options"
        :visible.sync="showExportOptions"
        width="30%">
        <div class="export-options">
          <div class="form-group">
            <label>Export Format</label>
            <el-select v-model="exportFormat" placeholder="Select Format" class="w-100">
              <el-option label="CSV" value="csv"></el-option>
              <el-option label="Excel" value="excel"></el-option>
              <el-option label="PDF" value="pdf"></el-option>
            </el-select>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showExportOptions = false">Cancel</el-button>
          <el-button type="primary" @click="downloadReport">Download</el-button>
        </span>
      </el-dialog>
    </card>
  </div>
</template>

<script>
import API from "@/services/api";
import { Table, TableColumn, Select, Option, DatePicker, Dialog, Button } from "element-ui";
import moment from "moment";
import * as dc from "dc";
import * as crossfilter from "crossfilter2";
import * as d3 from "d3";

export default {
  name: "Reports",

  components: {
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Select.name]: Select,
    [Option.name]: Option,
    [DatePicker.name]: DatePicker,
    [Dialog.name]: Dialog,
    [Button.name]: Button
  },

  data() {
    return {
      loading: false,
      reportGenerated: false,
      selectedReportType: "performance",
      dateRange: [new Date(new Date().setMonth(new Date().getMonth() - 1)), new Date()],
      selectedDepartment: null,
      selectedService: null,
      selectedStatus: null,
      selectedUser: null,
      departments: [],
      services: [],
      users: [],
      statuses: ["New", "In Progress", "Resolved", "Closed"],
      reportData: [],
      showExportOptions: false,
      exportFormat: "csv",
      // Chart references
      charts: {
        performance: {
          resolutionTimeChart: null,
          slaComplianceChart: null
        },
        issues: {
          statusChart: null,
          priorityChart: null
        },
        sla: {
          complianceChart: null,
          resolutionVsTargetChart: null
        },
        activity: {
          userActivityChart: null,
          activityTypeChart: null
        }
      },

      reportTypes: [
        { label: "Performance Report", value: "performance" },
        { label: "Issue Report", value: "issues" },
        { label: "SLA Compliance Report", value: "sla" },
        { label: "User Activity Report", value: "activity" }
      ],

      performanceColumns: [
        { prop: "department", label: "Department", minWidth: 150 },
        { prop: "avgResolutionTime", label: "Avg. Resolution Time (hrs)", minWidth: 180 },
        { prop: "slaCompliance", label: "SLA Compliance %", minWidth: 150 },
        { prop: "totalIssues", label: "Total Issues", minWidth: 120 },
        { prop: "resolvedIssues", label: "Resolved Issues", minWidth: 150 }
      ],

      issueColumns: [
        { prop: "issueRef", label: "Reference", minWidth: 200 },
        { prop: "title", label: "Title", minWidth: 200 },
        { prop: "status", label: "Status", minWidth: 100 },
        { prop: "reportedAt", label: "Reported Date", minWidth: 150 },
        { prop: "resolvedAt", label: "Resolved Date", minWidth: 150 },
        { prop: "department", label: "Department", minWidth: 150 },
        { prop: "service", label: "Service", minWidth: 150 }
      ],

      slaColumns: [
        { prop: "service", label: "Service", minWidth: 150 },
        { prop: "priority", label: "Priority", minWidth: 100 },
        { prop: "targetHours", label: "Target Hours", minWidth: 120 },
        { prop: "avgResolutionHours", label: "Avg. Resolution (hrs)", minWidth: 180 },
        { prop: "slaComplianceRate", label: "SLA Compliance %", minWidth: 150 },
        { prop: "complianceStatus", label: "Status", minWidth: 100 },
        { prop: "totalIssues", label: "Total Issues", minWidth: 120 },
        { prop: "resolvedIssues", label: "Resolved Issues", minWidth: 150 },
        { prop: "resolvedWithinSLA", label: "Within SLA", minWidth: 120 },
        { prop: "avgVsTargetPercentage", label: "Avg/Target %", minWidth: 120 }
      ],

      activityColumns: [
        { prop: "userName", label: "User", minWidth: 150 },
        { prop: "department", label: "Department", minWidth: 150 },
        { prop: "totalActions", label: "Total Actions", minWidth: 120 },
        { prop: "issuesHandled", label: "Issues Handled", minWidth: 120 },
        { prop: "statusChanges", label: "Status Changes", minWidth: 120 },
        { prop: "comments", label: "Comments", minWidth: 120 },
        { prop: "resolutions", label: "Resolutions", minWidth: 120 },
        { prop: "avgResponseTime", label: "Avg. Response Time (hrs)", minWidth: 180 }
      ],

      // Services
      issueService: new API(process.env.VUE_APP_API_URL, "Issues/all-issues"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      userService: new API(process.env.VUE_APP_API_URL, "users")
    };
  },

  computed: {
    showDepartmentFilter() {
      return ["performance", "issues", "sla", "activity"].includes(this.selectedReportType);
    },

    showServiceFilter() {
      return ["issues", "sla"].includes(this.selectedReportType);
    },

    showStatusFilter() {
      return ["issues"].includes(this.selectedReportType);
    },

    showUserFilter() {
      return ["activity"].includes(this.selectedReportType);
    },

    formattedDateRange() {
      if (!this.dateRange || !this.dateRange[0] || !this.dateRange[1]) {
        return { start: null, end: null };
      }

      return {
        start: moment(this.dateRange[0]).format("YYYY-MM-DD"),
        end: moment(this.dateRange[1]).format("YYYY-MM-DD")
      };
    }
  },

  async mounted() {

    await this.fetchDepartments();
    await this.fetchServices();
    await this.fetchUsers();

    // Add resize event listener
    window.addEventListener("resize", this.handleResize);
  },

  beforeDestroy() {
    // Clean up charts when component is destroyed
    if (dc && dc.chartRegistry) {
      try {
        dc.chartRegistry.clear();
      } catch (error) {
        console.error("Error clearing chart registry:", error);
      }
    }
    window.removeEventListener("resize", this.handleResize);
  },

  methods: {
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();

        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
      } catch (error) {
        console.error("Error fetching departments:", error);
        //this.$toast.error("Failed to load departments");
      }
    },

    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll())?.services ?? [];
      } catch (error) {
        console.error("Error fetching services:", error);
        //this.$toast.error("Failed to load services");
      }
    },

    async fetchUsers() {
      try {
        const response = await this.userService.getAll();
        this.users = response || [];
      } catch (error) {
        console.error("Error fetching users:", error);
        //this.$toast.error("Failed to load users");
      }
    },

    handleReportTypeChange() {
      // Reset filters based on report type
      this.selectedDepartment = null;
      this.selectedService = null;
      this.selectedStatus = null;
      this.selectedUser = null;
      this.reportData = [];
      this.reportGenerated = false;
    },

    handleDateRangeChange() {
      this.reportData = [];
      this.reportGenerated = false;
    },

    async generateReport() {
      if (!this.selectedReportType || !this.dateRange) {
        //this.$toast.warning("Please select a report type and date range");
        return;
      }

      this.loading = true;
      this.reportData = [];


      try {
        // Fetch data based on report type
        if (this.selectedReportType === "performance") {
          await this.generatePerformanceReport();
        } else if (this.selectedReportType === "issues") {
          await this.generateIssuesReport();
        } else if (this.selectedReportType === "sla") {
          await this.generateSLAReport();
        } else if (this.selectedReportType === "activity") {
          await this.generateActivityReport();
        }

        this.reportGenerated = true;

        // Render charts if data is available
        if (this.reportData.length > 0) {
          // Force a complete DOM update cycle to ensure chart containers are rendered
          setTimeout(() => {
            this.$nextTick(() => {
              console.log("DOM should be updated now, checking for chart containers...");

              // Log the presence of chart containers based on report type
              if (this.selectedReportType === "performance") {
                console.log("Performance chart containers:", {
                  resolutionTimeChart: !!document.getElementById("resolution-time-chart"),
                  slaComplianceChart: !!document.getElementById("sla-compliance-chart")
                });
              } else if (this.selectedReportType === "issues") {
                console.log("Issues chart containers:", {
                  statusChart: !!document.getElementById("issues-by-status-chart"),
                  priorityChart: !!document.getElementById("issues-by-priority-chart")
                });
              } else if (this.selectedReportType === "sla") {
                console.log("SLA chart containers:", {
                  complianceChart: !!document.getElementById("sla-compliance-by-service-chart"),
                  resolutionChart: !!document.getElementById("resolution-vs-target-chart")
                });
              } else if (this.selectedReportType === "activity") {
                console.log("Activity chart containers:", {
                  userChart: !!document.getElementById("activity-by-user-chart"),
                  typeChart: !!document.getElementById("activity-by-type-chart")
                });
              }

              // Reset any existing charts
              if (dc && dc.chartRegistry) {
                try {
                  dc.chartRegistry.clear();
                  console.log("Chart registry cleared successfully");
                } catch (error) {
                  console.error("Error clearing chart registry:", error);
                }
              } else {
                console.warn("dc or dc.chartRegistry is not available");
              }

              // Render the appropriate charts
              this.renderCharts();
            });
          }, 100); // Small delay to ensure DOM is updated
        }
      } catch (error) {
        console.error("Error generating report:", error);
        //this.$toast.error("Failed to generate report");
      } finally {
        this.loading = false;
      }
    },

    async generatePerformanceReport() {
      // Fetch issues data
      const response = await this.issueService.getAll();
      const issues = response || [];

      // Filter by date range
      const filteredIssues = issues.filter(issue => {
        const reportedDate = new Date(issue.reportedAt);
        return reportedDate >= this.dateRange[0] && reportedDate <= this.dateRange[1];
      });

      // Filter by department if selected
      const departmentFilteredIssues = this.selectedDepartment
        ? filteredIssues.filter(issue => issue.assignedDepartmentId === this.selectedDepartment)
        : filteredIssues;

      // Group by department
      const departmentGroups = {};

      departmentFilteredIssues.forEach(issue => {
        const deptId = issue.assignedDepartmentId;
        if (!departmentGroups[deptId]) {
          departmentGroups[deptId] = {
            departmentId: deptId,
            department: this.getDepartmentName(deptId),
            totalIssues: 0,
            resolvedIssues: 0,
            totalResolutionTime: 0,
            withinSLA: 0
          };
        }

        departmentGroups[deptId].totalIssues++;

        if (issue.status === "Resolved" || issue.status === "Closed") {
          departmentGroups[deptId].resolvedIssues++;

          // Calculate resolution time
          if (issue.resolvedAt) {
            const reportedDate = new Date(issue.reportedAt);
            const resolvedDate = new Date(issue.resolvedAt);
            const resolutionTimeHours = (resolvedDate - reportedDate) / (1000 * 60 * 60);
            departmentGroups[deptId].totalResolutionTime += resolutionTimeHours;
          }

          // Check SLA compliance
          if (issue.isWithinSLA) {
            departmentGroups[deptId].withinSLA++;
          }
        }
      });

      // Calculate averages and format data
      this.reportData = Object.values(departmentGroups).map(dept => {
        const avgResolutionTime = dept.resolvedIssues > 0
          ? (dept.totalResolutionTime / dept.resolvedIssues).toFixed(2)
          : 0;

        const slaCompliance = dept.resolvedIssues > 0
          ? ((dept.withinSLA / dept.resolvedIssues) * 100).toFixed(2)
          : 0;

        return {
          department: dept.department,
          avgResolutionTime,
          slaCompliance,
          totalIssues: dept.totalIssues,
          resolvedIssues: dept.resolvedIssues
        };
      });
    },

    async generateIssuesReport() {
      // Fetch issues data
      const response = await this.issueService.getAll();
      const issues = response || [];

      // Apply filters
      let filteredIssues = issues.filter(issue => {
        const reportedDate = new Date(issue.reportedAt);
        return reportedDate >= this.dateRange[0] && reportedDate <= this.dateRange[1];
      });

      if (this.selectedDepartment) {
        filteredIssues = filteredIssues.filter(
          issue => issue.assignedDepartmentId === this.selectedDepartment
        );
      }

      if (this.selectedService) {
        filteredIssues = filteredIssues.filter(
          issue => issue.serviceId === this.selectedService
        );
      }

      if (this.selectedStatus) {
        filteredIssues = filteredIssues.filter(
          issue => issue.status === this.selectedStatus
        );
      }

      // Format data
      this.reportData = filteredIssues.map(issue => {
        return {
          issueRef: issue.issueRef,
          title: issue.title,
          status: issue.status,
          reportedAt: moment(issue.reportedAt).format("YYYY-MM-DD HH:mm"),
          resolvedAt: issue.resolvedAt ? moment(issue.resolvedAt).format("YYYY-MM-DD HH:mm") : "-",
          department: this.getDepartmentName(issue.assignedDepartmentId),
          service: this.getServiceName(issue.serviceId)
        };
      });
    },

    async generateSLAReport() {
      // Fetch SLA data
      const slaService = new API(process.env.VUE_APP_API_URL, "slas");
      const slaData = await slaService.getAll();

      // Fetch issues data
      const response = await this.issueService.getAll();
      const issues = response || [];

      // Filter issues by date range
      const filteredIssues = issues.filter(issue => {
        const reportedDate = new Date(issue.reportedAt);
        return reportedDate >= this.dateRange[0] && reportedDate <= this.dateRange[1];
      });

      // Apply additional filters
      let filteredSLAIssues = filteredIssues;

      if (this.selectedDepartment) {
        filteredSLAIssues = filteredSLAIssues.filter(
          issue => issue.assignedDepartmentId === this.selectedDepartment
        );
      }

      if (this.selectedService) {
        filteredSLAIssues = filteredSLAIssues.filter(
          issue => issue.serviceId === this.selectedService
        );
      }

      // Group by service
      const serviceGroups = {};

      filteredSLAIssues.forEach(issue => {
        const serviceId = issue.serviceId;
        if (!serviceId) return;

        if (!serviceGroups[serviceId]) {
          // Find SLA configuration for this service
          const slaConfig = slaData.find(sla => sla.serviceId === serviceId);

          serviceGroups[serviceId] = {
            serviceId,
            serviceName: this.getServiceName(serviceId),
            priority: slaConfig ? slaConfig.priority : 'Unknown',
            targetResolutionHours: slaConfig ? slaConfig.resolutionTimeHours : 0,
            totalIssues: 0,
            resolvedIssues: 0,
            resolvedWithinSLA: 0,
            totalResolutionTime: 0
          };
        }

        serviceGroups[serviceId].totalIssues++;

        if (issue.status === "Resolved" || issue.status === "Closed") {
          serviceGroups[serviceId].resolvedIssues++;

          // Calculate resolution time
          if (issue.resolvedAt && issue.reportedAt) {
            const reportedDate = new Date(issue.reportedAt);
            const resolvedDate = new Date(issue.resolvedAt);
            const resolutionTimeHours = (resolvedDate - reportedDate) / (1000 * 60 * 60);
            serviceGroups[serviceId].totalResolutionTime += resolutionTimeHours;

            // Check SLA compliance
            if (issue.isWithinSLA ||
                (serviceGroups[serviceId].targetResolutionHours > 0 &&
                 resolutionTimeHours <= serviceGroups[serviceId].targetResolutionHours)) {
              serviceGroups[serviceId].resolvedWithinSLA++;
            }
          }
        }
      });

      // Format data for display
      this.reportData = Object.values(serviceGroups).map(service => {
        const avgResolutionTime = service.resolvedIssues > 0
          ? (service.totalResolutionTime / service.resolvedIssues).toFixed(2)
          : 0;

        const slaComplianceRate = service.resolvedIssues > 0
          ? ((service.resolvedWithinSLA / service.resolvedIssues) * 100).toFixed(2)
          : 0;

        const complianceStatus = slaComplianceRate >= 90
          ? 'Good'
          : slaComplianceRate >= 75
            ? 'Average'
            : 'Poor';

        const avgVsTarget = service.targetResolutionHours > 0 && avgResolutionTime > 0
          ? ((avgResolutionTime / service.targetResolutionHours) * 100).toFixed(2)
          : 0;

        return {
          service: service.serviceName,
          priority: service.priority,
          targetHours: service.targetResolutionHours,
          avgResolutionHours: avgResolutionTime,
          slaComplianceRate: slaComplianceRate,
          complianceStatus: complianceStatus,
          totalIssues: service.totalIssues,
          resolvedIssues: service.resolvedIssues,
          resolvedWithinSLA: service.resolvedWithinSLA,
          avgVsTargetPercentage: avgVsTarget
        };
      });
    },

    async generateActivityReport() {
      try {
        console.log("Generating activity report...");

        // Fetch issue logs data
        const issueLogsService = new API(process.env.VUE_APP_API_URL, "issuelogs");
        console.log("Fetching issue logs...");
        const logsResponse = await issueLogsService.getAll();
        const issueLogs = logsResponse || [];
        console.log(`Retrieved ${issueLogs.length} issue logs`);

        // Fetch users data
        console.log("Fetching users...");
        const usersResponse = await this.userService.getAll();
        const users = usersResponse || [];
        console.log(`Retrieved ${users.length} users`);

        // Fetch issues data for reference
        console.log("Fetching issues...");
        const issuesResponse = await this.issueService.getAll();
        const issues = issuesResponse || [];
        console.log(`Retrieved ${issues.length} issues`);

        // Filter logs by date range
        let filteredLogs = issueLogs.filter(log => {
          const logDate = new Date(log.timestamp || log.createdAt);
          return logDate >= this.dateRange[0] && logDate <= this.dateRange[1];
        });
        console.log(`Filtered to ${filteredLogs.length} logs within date range`);

        // Apply user filter if selected
        if (this.selectedUser) {
          filteredLogs = filteredLogs.filter(log => log.userId === this.selectedUser);
          console.log(`Further filtered to ${filteredLogs.length} logs for selected user`);
        }

        // Apply department filter if selected
        if (this.selectedDepartment) {
          // Get users in the selected department
          const departmentUsers = users
            .filter(user => user.departmentId === this.selectedDepartment)
            .map(user => user.id);

          filteredLogs = filteredLogs.filter(log => departmentUsers.includes(log.userId));
          console.log(`Further filtered to ${filteredLogs.length} logs for selected department`);
        }

        // Group logs by user
        const userActivityGroups = {};

        filteredLogs.forEach(log => {
          const userId = log.userId;
          if (!userId) return;

          if (!userActivityGroups[userId]) {
            const user = users.find(u => u.id === userId);

            userActivityGroups[userId] = {
              userId,
              userName: user ? user.fullName : 'Unknown User',
              department: user ? this.getDepartmentName(user.departmentId) : 'Unknown',
              totalActions: 0,
              issuesHandled: new Set(),
              statusChanges: 0,
              comments: 0,
              resolutions: 0,
              averageResponseTime: 0,
              totalResponseTime: 0,
              responseCount: 0
            };
          }

          userActivityGroups[userId].totalActions++;

          if (log.issueId) {
            userActivityGroups[userId].issuesHandled.add(log.issueId);
          }

          // Categorize log types
          if (log.type === 'Status Change' || log.description?.includes('status')) {
            userActivityGroups[userId].statusChanges++;
          } else if (log.type === 'Comment' || log.description?.includes('comment')) {
            userActivityGroups[userId].comments++;
          } else if (log.type === 'Resolution' || log.description?.includes('resolved')) {
            userActivityGroups[userId].resolutions++;
          }

          // Calculate response time if applicable
          if (log.issueId && log.timestamp) {
            const issue = issues.find(i => i.issueId === log.issueId);
            if (issue && issue.reportedAt) {
              const reportedDate = new Date(issue.reportedAt);
              const logDate = new Date(log.timestamp);

              // Only count first response for each issue
              if (!userActivityGroups[userId][`responded_${log.issueId}`]) {
                const responseTimeHours = (logDate - reportedDate) / (1000 * 60 * 60);
                userActivityGroups[userId].totalResponseTime += responseTimeHours;
                userActivityGroups[userId].responseCount++;
                userActivityGroups[userId][`responded_${log.issueId}`] = true;
              }
            }
          }
        });

        console.log(`Generated activity data for ${Object.keys(userActivityGroups).length} users`);

        // Format data for display
        this.reportData = Object.values(userActivityGroups).map(userData => {
          const avgResponseTime = userData.responseCount > 0
            ? (userData.totalResponseTime / userData.responseCount).toFixed(2)
            : 0;

          return {
            userName: userData.userName,
            department: userData.department,
            totalActions: userData.totalActions,
            issuesHandled: userData.issuesHandled.size,
            statusChanges: userData.statusChanges,
            comments: userData.comments,
            resolutions: userData.resolutions,
            avgResponseTime: avgResponseTime
          };
        });

        console.log("Activity report data:", this.reportData);

        if (this.reportData.length === 0) {
          console.warn("No activity data found for the selected filters");
          //this.$toast.warning("No activity data found for the selected filters");
        }
      } catch (error) {
        console.error("Error generating activity report:", error);
        //this.$toast.error("Failed to generate activity report: " + (error.message || "Unknown error"));
        this.reportData = [];
      }
    },

    getDepartmentName(departmentId) {
      if (!departmentId) return "Unknown";

      // Try to find by departmentId first
      let department = this.departments.find(d => d.departmentId === departmentId);

      // If not found, try to find by id
      if (!department) {
        department = this.departments.find(d => d.id === departmentId);
      }

      return department ? department.name : "Unknown";
    },

    getServiceName(serviceId) {
      if (!serviceId) return "Unknown";

      // Try to find by i_service_type first (as seen in the UI)
      let service = this.services.find(s => s.i_service_type === serviceId);

      // If not found, try to find by id
      if (!service) {
        service = this.services.find(s => s.id === serviceId);
      }

      return service ? service.name : "Unknown";
    },

    renderCharts() {
      console.log("Rendering charts for report type:", this.selectedReportType);

      // Check if charting libraries are available
      if (!dc || !crossfilter) {
        console.error("Charting libraries not available. Cannot render charts.");

        // Wait for the DOM to be updated with the correct report type containers
        this.$nextTick(() => {
          // Display error message in chart containers based on report type
          if (this.selectedReportType === "performance") {
            const container1 = document.getElementById("resolution-time-chart");
            const container2 = document.getElementById("sla-compliance-chart");

            if (container1) {
              d3.select("#resolution-time-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }

            if (container2) {
              d3.select("#sla-compliance-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }
          } else if (this.selectedReportType === "issues") {
            const container1 = document.getElementById("issues-by-status-chart");
            const container2 = document.getElementById("issues-by-priority-chart");

            if (container1) {
              d3.select("#issues-by-status-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }

            if (container2) {
              d3.select("#issues-by-priority-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }
          } else if (this.selectedReportType === "sla") {
            const container1 = document.getElementById("sla-compliance-by-service-chart");
            const container2 = document.getElementById("resolution-vs-target-chart");

            if (container1) {
              d3.select("#sla-compliance-by-service-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }

            if (container2) {
              d3.select("#resolution-vs-target-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }
          } else if (this.selectedReportType === "activity") {
            const container1 = document.getElementById("activity-by-user-chart");
            const container2 = document.getElementById("activity-by-type-chart");

            if (container1) {
              d3.select("#activity-by-user-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }

            if (container2) {
              d3.select("#activity-by-type-chart")
                .html("<div class='empty-chart'>Charting libraries not available</div>");
            }
          }
        });

        return;
      }

      // Wait for the DOM to be updated with the correct report type containers
      this.$nextTick(() => {
        console.log("DOM updated, proceeding with chart rendering");

        // Proceed with rendering the appropriate charts
        if (this.selectedReportType === "performance") {
          this.renderPerformanceCharts();
        } else if (this.selectedReportType === "issues") {
          this.renderIssueCharts();
        } else if (this.selectedReportType === "sla") {
          this.renderSLACharts();
        } else if (this.selectedReportType === "activity") {
          this.renderActivityCharts();
        }
      });
    },

    renderPerformanceCharts() {
      try {
        console.log("Rendering performance charts with data:", this.reportData);

        // Check if chart containers exist
        const resolutionChartContainer = document.getElementById("resolution-time-chart");
        const slaChartContainer = document.getElementById("sla-compliance-chart");

        if (!resolutionChartContainer || !slaChartContainer) {
          console.error("Chart containers not found in DOM:", {
            resolutionChartContainer: !!resolutionChartContainer,
            slaChartContainer: !!slaChartContainer
          });
          //this.$toast.error("Chart containers not found. Please try refreshing the page.");
          return;
        }

        console.log("Chart containers found in DOM");

        if (!this.reportData || this.reportData.length === 0) {
          console.warn("No performance data available to render charts");

          // Display a message in the chart containers
          d3.select("#resolution-time-chart")
            .html("<div class='empty-chart'>No performance data available</div>");
          d3.select("#sla-compliance-chart")
            .html("<div class='empty-chart'>No performance data available</div>");

          return;
        }

        // Clear existing charts
        d3.select("#resolution-time-chart").html("");
        d3.select("#sla-compliance-chart").html("");

        // Check if crossfilter is available
        if (!crossfilter) {
          console.error("crossfilter library not available");
          throw new Error("crossfilter library not available");
        }

        console.log(crossfilter,"udyu")

        // Create crossfilter instance
        const ndx = crossfilter.default(this.reportData);

      // Resolution Time Chart
      const departmentDim = ndx.dimension(d => d.department);
      const avgResolutionByDept = departmentDim.group().reduceSum(d => parseFloat(d.avgResolutionTime));

      // Create a color scale using application theme colors
      const colorScale = d3.scaleOrdinal()
        .range(['#302e77', '#9368E9', '#87CB16', '#FFA534', '#FB404B', '#45c0fd', '#533ce1', '#6dc030']);

      // Create the resolution time chart
      const resolutionTimeChart = dc.barChart("#resolution-time-chart");
      resolutionTimeChart
        .width(null) // Responsive width
        .height(300)
        .dimension(departmentDim)
        .group(avgResolutionByDept)
        .x(d3.scaleBand())
        .xUnits(dc.units.ordinal)
        .elasticY(true)
        .colors(colorScale)
        .barPadding(0.2)
        .renderHorizontalGridLines(true)
        // Enhanced tooltip with formatted values
        .title(d => `${d.key}\nAvg. Resolution Time: ${d.value.toFixed(2)} hours`)
        // Increased margins for better spacing
        .margins({top: 40, right: 60, bottom: 90, left: 70})
        .xAxisLabel("Department")
        .yAxisLabel("Avg. Resolution Time (hrs)")
        .renderTitle(true);

      // Enhanced x-axis and y-axis styling
      resolutionTimeChart.on('renderlet', function(chart) {
        // Rotate x-axis labels for better readability
        chart.selectAll('g.x text')
          .attr('transform', 'translate(-10,10) rotate(315)')
          .style('text-anchor', 'end')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style y-axis labels
        chart.selectAll('g.y text')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style grid lines
        chart.selectAll('g.grid-line line')
          .style('stroke', '#e5e5e5')
          .style('stroke-width', '1px');

        // Style axis lines
        chart.selectAll('.axis path')
          .style('stroke', '#999')
          .style('stroke-width', '1px');

        // Add hover effect to bars
        chart.selectAll('.bar')
          .on('mouseover', function() {
            d3.select(this)
              .style('fill-opacity', 0.8);
          })
          .on('mouseout', function() {
            d3.select(this)
              .style('fill-opacity', 1);
          });
      });

      // SLA Compliance Chart
      const slaComplianceDim = ndx.dimension(d => d.department);
      const slaComplianceByDept = slaComplianceDim.group().reduceSum(d => parseFloat(d.slaCompliance));

      const slaComplianceChart = dc.pieChart("#sla-compliance-chart");
      slaComplianceChart
        .width(null) // Responsive width
        .height(300)
        .dimension(slaComplianceDim)
        .group(slaComplianceByDept)
        .colors(colorScale)
        // Enhanced legend with better positioning and styling
        .legend(dc.legend()
          .x(10)
          .y(20)
          .itemHeight(15)
          .gap(7)
          .horizontal(false)
          .legendWidth(140)
          .itemWidth(140))
        .renderLabel(true)
        .renderTitle(true)
        // Enhanced tooltip with formatted values
        .title(d => `${d.key}\nSLA Compliance: ${d.value.toFixed(2)}%`)
        // Add inner radius for donut chart style
        .innerRadius(40)
        // Add padding between slices
        .slicesCap(10)
        .externalLabels(30)
        .drawPaths(true)
        .externalRadiusPadding(30);

      // Add custom styling to the pie chart
      slaComplianceChart.on('renderlet', function(chart) {
        // Style the pie slices
        chart.selectAll('.pie-slice')
          .style('stroke', '#fff')
          .style('stroke-width', '1px');

        // Style the labels
        chart.selectAll('.pie-label')
          .style('font-size', '12px')
          .style('fill', '#333')
          .style('font-weight', 'bold');

        // Style the legend text
        chart.selectAll('.dc-legend-item text')
          .style('font-size', '12px')
          .style('fill', '#333');
      });

      // Render all charts
      dc.renderAll();

      console.log("Performance charts rendered successfully");
      } catch (error) {
        console.error("Error rendering performance charts:", error);

        // Display error message in the chart containers
        d3.select("#resolution-time-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);
        d3.select("#sla-compliance-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);

        //this.$toast.error("Failed to render performance charts: " + error.message);
      }
    },

    renderIssueCharts() {
      try {
        console.log("Rendering issue charts with data:", this.reportData);

        // Check if chart containers exist
        const statusChartContainer = document.getElementById("issues-by-status-chart");
        const priorityChartContainer = document.getElementById("issues-by-priority-chart");

        if (!statusChartContainer || !priorityChartContainer) {
          console.error("Chart containers not found in DOM:", {
            statusChartContainer: !!statusChartContainer,
            priorityChartContainer: !!priorityChartContainer
          });
          //this.$toast.error("Chart containers not found. Please try refreshing the page.");
          return;
        }

        console.log("Chart containers found in DOM");

        if (!this.reportData || this.reportData.length === 0) {
          console.warn("No issue data available to render charts");

          // Display a message in the chart containers
          d3.select("#issues-by-status-chart")
            .html("<div class='empty-chart'>No issue data available</div>");
          d3.select("#issues-by-priority-chart")
            .html("<div class='empty-chart'>No issue data available</div>");

          return;
        }

        // Clear existing charts
        d3.select("#issues-by-status-chart").html("");
        d3.select("#issues-by-priority-chart").html("");

        // Check if crossfilter is available
        if (!crossfilter) {
          console.error("crossfilter library not available");
          throw new Error("crossfilter library not available");
        }

        // Create crossfilter instance
        const ndx = crossfilter.default(this.reportData);

      // Status Chart
      const statusDim = ndx.dimension(d => d.status);
      const issuesByStatus = statusDim.group().reduceCount();

      // Create a color scale using application theme colors
      const colorScale = d3.scaleOrdinal()
        .range(['#302e77', '#9368E9', '#87CB16', '#FFA534', '#FB404B', '#45c0fd', '#533ce1', '#6dc030']);

      const statusChart = dc.pieChart("#issues-by-status-chart");
      statusChart
        .width(null) // Responsive width
        .height(300)
        .dimension(statusDim)
        .group(issuesByStatus)
        .colors(colorScale)
        // Enhanced legend with better positioning and styling
        .legend(dc.legend()
          .x(10)
          .y(20)
          .itemHeight(15)
          .gap(7)
          .horizontal(false)
          .legendWidth(140)
          .itemWidth(140))
        .renderLabel(true)
        .renderTitle(true)
        // Enhanced tooltip with formatted values
        .title(d => `${d.key}\nIssues: ${d.value}`)
        // Add inner radius for donut chart style
        .innerRadius(40)
        // Add padding between slices
        .slicesCap(10)
        .externalLabels(30)
        .drawPaths(true)
        .externalRadiusPadding(30);

      // Add custom styling to the pie chart
      statusChart.on('renderlet', function(chart) {
        // Style the pie slices
        chart.selectAll('.pie-slice')
          .style('stroke', '#fff')
          .style('stroke-width', '1px');

        // Style the labels
        chart.selectAll('.pie-label')
          .style('font-size', '12px')
          .style('fill', '#333')
          .style('font-weight', 'bold');

        // Style the legend text
        chart.selectAll('.dc-legend-item text')
          .style('font-size', '12px')
          .style('fill', '#333');
      });

      // Priority Chart
      const priorityDim = ndx.dimension(d => d.priority);
      const issuesByPriority = priorityDim.group().reduceCount();

      const priorityChart = dc.barChart("#issues-by-priority-chart");
      priorityChart
        .width(null) // Responsive width
        .height(300)
        .dimension(priorityDim)
        .group(issuesByPriority)
        .x(d3.scaleBand())
        .xUnits(dc.units.ordinal)
        .elasticY(true)
        .colors(colorScale)
        .barPadding(0.2)
        .renderHorizontalGridLines(true)
        // Enhanced tooltip with formatted values
        .title(d => `${d.key}\nIssues: ${d.value}`)
        // Increased margins for better spacing
        .margins({top: 40, right: 60, bottom: 60, left: 70})
        .xAxisLabel("Priority")
        .yAxisLabel("Number of Issues")
        .renderTitle(true);

      // Enhanced x-axis and y-axis styling
      priorityChart.on('renderlet', function(chart) {
        // Style x-axis labels
        chart.selectAll('g.x text')
          .style('text-anchor', 'middle')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style y-axis labels
        chart.selectAll('g.y text')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style grid lines
        chart.selectAll('g.grid-line line')
          .style('stroke', '#e5e5e5')
          .style('stroke-width', '1px');

        // Style axis lines
        chart.selectAll('.axis path')
          .style('stroke', '#999')
          .style('stroke-width', '1px');

        // Add hover effect to bars
        chart.selectAll('.bar')
          .on('mouseover', function() {
            d3.select(this)
              .style('fill-opacity', 0.8);
          })
          .on('mouseout', function() {
            d3.select(this)
              .style('fill-opacity', 1);
          });
      });

      // Render all charts
      dc.renderAll();

      console.log("Issue charts rendered successfully");
      } catch (error) {
        console.error("Error rendering issue charts:", error);

        // Display error message in the chart containers
        d3.select("#issues-by-status-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);
        d3.select("#issues-by-priority-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);

        //this.$toast.error("Failed to render issue charts: " + error.message);
      }
    },

    renderSLACharts() {
      try {
        console.log("Rendering SLA charts with data:", this.reportData);

        // Check if chart containers exist
        const complianceChartContainer = document.getElementById("sla-compliance-by-service-chart");
        const resolutionChartContainer = document.getElementById("resolution-vs-target-chart");

        if (!complianceChartContainer || !resolutionChartContainer) {
          console.error("Chart containers not found in DOM:", {
            complianceChartContainer: !!complianceChartContainer,
            resolutionChartContainer: !!resolutionChartContainer
          });
          //this.$toast.error("Chart containers not found. Please try refreshing the page.");
          return;
        }

        console.log("Chart containers found in DOM");

        if (!this.reportData || this.reportData.length === 0) {
          console.warn("No SLA data available to render charts");

          // Display a message in the chart containers
          d3.select("#sla-compliance-by-service-chart")
            .html("<div class='empty-chart'>No SLA data available</div>");
          d3.select("#resolution-vs-target-chart")
            .html("<div class='empty-chart'>No SLA data available</div>");

          return;
        }

        // Clear existing charts
        d3.select("#sla-compliance-by-service-chart").html("");
        d3.select("#resolution-vs-target-chart").html("");

        // Check if crossfilter is available
        if (!crossfilter) {
          console.error("crossfilter library not available");
          throw new Error("crossfilter library not available");
        }

        // Create crossfilter instance
        const ndx = crossfilter.default(this.reportData);

      // SLA Compliance by Service Chart
      const serviceDim = ndx.dimension(d => d.service);
      const complianceByService = serviceDim.group().reduceSum(d => parseFloat(d.slaComplianceRate));

      // Create a color scale using application theme colors
      const colorScale = d3.scaleOrdinal()
        .range(['#302e77', '#9368E9', '#87CB16', '#FFA534', '#FB404B', '#45c0fd', '#533ce1', '#6dc030']);

      const slaComplianceChart = dc.barChart("#sla-compliance-by-service-chart");
      slaComplianceChart
        .width(null) // Responsive width
        .height(300)
        .dimension(serviceDim)
        .group(complianceByService)
        .x(d3.scaleBand())
        .xUnits(dc.units.ordinal)
        .elasticY(true)
        .colors(colorScale)
        .barPadding(0.2)
        .renderHorizontalGridLines(true)
        // Enhanced tooltip with formatted values
        .title(d => `${d.key}\nSLA Compliance: ${d.value.toFixed(2)}%`)
        // Increased margins for better spacing
        .margins({top: 40, right: 60, bottom: 90, left: 70})
        .xAxisLabel("Service")
        .yAxisLabel("SLA Compliance %")
        .renderTitle(true);

      // Enhanced x-axis and y-axis styling
      slaComplianceChart.on('renderlet', function(chart) {
        // Rotate x-axis labels for better readability
        chart.selectAll('g.x text')
          .attr('transform', 'translate(-10,10) rotate(315)')
          .style('text-anchor', 'end')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style y-axis labels
        chart.selectAll('g.y text')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style grid lines
        chart.selectAll('g.grid-line line')
          .style('stroke', '#e5e5e5')
          .style('stroke-width', '1px');

        // Style axis lines
        chart.selectAll('.axis path')
          .style('stroke', '#999')
          .style('stroke-width', '1px');

        // Add hover effect to bars
        chart.selectAll('.bar')
          .on('mouseover', function() {
            d3.select(this)
              .style('fill-opacity', 0.8);
          })
          .on('mouseout', function() {
            d3.select(this)
              .style('fill-opacity', 1);
          });
      });

      // Resolution vs Target Chart
      const resolutionDim = ndx.dimension(d => d.service);
      const avgResolutionByService = resolutionDim.group().reduceSum(d => parseFloat(d.avgResolutionHours));
      const targetByService = resolutionDim.group().reduceSum(d => parseFloat(d.targetHours));

      const resolutionVsTargetChart = dc.compositeChart("#resolution-vs-target-chart");
      resolutionVsTargetChart
        .width(null) // Responsive width
        .height(300)
        .dimension(resolutionDim)
        .x(d3.scaleBand())
        .xUnits(dc.units.ordinal)
        .elasticY(true)
        .renderHorizontalGridLines(true)
        // Enhanced legend with better positioning and styling
        .legend(dc.legend()
          .x(50)
          .y(15)
          .itemHeight(15)
          .gap(7)
          .horizontal(true)
          .legendWidth(300)
          .itemWidth(150))
        // Increased margins for better spacing
        .margins({top: 40, right: 60, bottom: 90, left: 70})
        .xAxisLabel("Service")
        .yAxisLabel("Hours")
        .compose([
          dc.barChart(resolutionVsTargetChart)
            .group(avgResolutionByService, "Avg. Resolution Time")
            .colors(['#302e77']) // Primary blue from theme
            .barPadding(0.1)
            .title(d => `${d.key}\nAvg. Resolution Time: ${d.value.toFixed(2)} hours`),
          dc.barChart(resolutionVsTargetChart)
            .group(targetByService, "Target Resolution Time")
            .colors(['#9368E9']) // Purple from theme
            .barPadding(0.1)
            .title(d => `${d.key}\nTarget Resolution Time: ${d.value.toFixed(2)} hours`)
        ]);

      // Enhanced x-axis and y-axis styling
      resolutionVsTargetChart.on('renderlet', function(chart) {
        // Rotate x-axis labels for better readability
        chart.selectAll('g.x text')
          .attr('transform', 'translate(-10,10) rotate(315)')
          .style('text-anchor', 'end')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style y-axis labels
        chart.selectAll('g.y text')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Style grid lines
        chart.selectAll('g.grid-line line')
          .style('stroke', '#e5e5e5')
          .style('stroke-width', '1px');

        // Style axis lines
        chart.selectAll('.axis path')
          .style('stroke', '#999')
          .style('stroke-width', '1px');

        // Style legend text
        chart.selectAll('.dc-legend-item text')
          .style('font-size', '12px')
          .style('fill', '#333');

        // Add hover effect to bars
        chart.selectAll('.bar')
          .on('mouseover', function() {
            d3.select(this)
              .style('fill-opacity', 0.8);
          })
          .on('mouseout', function() {
            d3.select(this)
              .style('fill-opacity', 1);
          });
      });

      // Render all charts
      dc.renderAll();

      console.log("SLA charts rendered successfully");
      } catch (error) {
        console.error("Error rendering SLA charts:", error);

        // Display error message in the chart containers
        d3.select("#sla-compliance-by-service-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);
        d3.select("#resolution-vs-target-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);

        //this.$toast.error("Failed to render SLA charts: " + error.message);
      }
    },

    renderActivityCharts() {
      try {
        console.log("Rendering activity charts with data:", this.reportData);

        // Check if chart containers exist
        const userChartContainer = document.getElementById("activity-by-user-chart");
        const typeChartContainer = document.getElementById("activity-by-type-chart");

        if (!userChartContainer || !typeChartContainer) {
          console.error("Chart containers not found in DOM:", {
            userChartContainer: !!userChartContainer,
            typeChartContainer: !!typeChartContainer
          });
          //this.$toast.error("Chart containers not found. Please try refreshing the page.");
          return;
        }

        console.log("Chart containers found in DOM");

        if (!this.reportData || this.reportData.length === 0) {
          console.warn("No activity data available to render charts");

          // Display a message in the chart containers
          d3.select("#activity-by-user-chart")
            .html("<div class='empty-chart'>No activity data available</div>");
          d3.select("#activity-by-type-chart")
            .html("<div class='empty-chart'>No activity data available</div>");

          return;
        }

        // Clear existing charts
        d3.select("#activity-by-user-chart").html("");
        d3.select("#activity-by-type-chart").html("");

        // Check if crossfilter is available
        if (!crossfilter) {
          console.error("crossfilter library not available");
          throw new Error("crossfilter library not available");
        }

        // Create crossfilter instance
        const ndx = crossfilter.default(this.reportData);

        // Activity by User Chart
        const userDim = ndx.dimension(d => d.userName);
        const actionsByUser = userDim.group().reduceSum(d => {
          // Ensure totalActions is a number
          const actions = parseInt(d.totalActions) || 0;
          console.log(`User ${d.userName} has ${actions} actions`);
          return actions;
        });

        // Create a color scale using application theme colors
        const colorScale = d3.scaleOrdinal()
          .range(['#302e77', '#9368E9', '#87CB16', '#FFA534', '#FB404B', '#45c0fd', '#533ce1', '#6dc030']);

        // Log dimension and group data for debugging
        console.log("User dimension groups:", actionsByUser.all());

        const userActivityChart = dc.barChart("#activity-by-user-chart");
        userActivityChart
          .width(null) // Responsive width
          .height(300)
          .dimension(userDim)
          .group(actionsByUser)
          .x(d3.scaleBand())
          .xUnits(dc.units.ordinal)
          .elasticY(true)
          .colors(colorScale)
          .barPadding(0.2)
          .renderHorizontalGridLines(true)
          // Enhanced tooltip with formatted values
          .title(d => `${d.key}\nTotal Actions: ${d.value}`)
          // Increased margins for better spacing
          .margins({top: 40, right: 60, bottom: 90, left: 70})
          .xAxisLabel("User")
          .yAxisLabel("Total Actions")
          .renderTitle(true);

        // Enhanced x-axis and y-axis styling
        userActivityChart.on('renderlet', function(chart) {
          // Rotate x-axis labels for better readability
          chart.selectAll('g.x text')
            .attr('transform', 'translate(-10,10) rotate(315)')
            .style('text-anchor', 'end')
            .style('font-size', '12px')
            .style('fill', '#333');

          // Style y-axis labels
          chart.selectAll('g.y text')
            .style('font-size', '12px')
            .style('fill', '#333');

          // Style grid lines
          chart.selectAll('g.grid-line line')
            .style('stroke', '#e5e5e5')
            .style('stroke-width', '1px');

          // Style axis lines
          chart.selectAll('.axis path')
            .style('stroke', '#999')
            .style('stroke-width', '1px');

          // Add hover effect to bars
          chart.selectAll('.bar')
            .on('mouseover', function() {
              d3.select(this)
                .style('fill-opacity', 0.8);
            })
            .on('mouseout', function() {
              d3.select(this)
                .style('fill-opacity', 1);
            });
        });

        // Activity by Type Chart
        // Create a new dataset for activity types
        const activityTypes = this.reportData.map(user => {
          return [
            { user: user.userName, type: 'Status Changes', count: parseInt(user.statusChanges) || 0 },
            { user: user.userName, type: 'Comments', count: parseInt(user.comments) || 0 },
            { user: user.userName, type: 'Resolutions', count: parseInt(user.resolutions) || 0 }
          ];
        }).flat();

        console.log("Activity types data:", activityTypes);

        // Create a new crossfilter for activity types
        if (!crossfilter) {
          console.error("crossfilter library not available for activity types");
          throw new Error("crossfilter library not available");
        }
        const activityNdx = crossfilter.default(activityTypes);
        const typeDim = activityNdx.dimension(d => d.type);
        const countByType = typeDim.group().reduceSum(d => d.count);

        // Log dimension and group data for debugging
        console.log("Activity type groups:", countByType.all());

        const activityTypeChart = dc.pieChart("#activity-by-type-chart");
        activityTypeChart
          .width(null) // Responsive width
          .height(300)
          .dimension(typeDim)
          .group(countByType)
          .colors(colorScale)
          // Enhanced legend with better positioning and styling
          .legend(dc.legend()
            .x(10)
            .y(20)
            .itemHeight(15)
            .gap(7)
            .horizontal(false)
            .legendWidth(140)
            .itemWidth(140))
          .renderLabel(true)
          .renderTitle(true)
          // Enhanced tooltip with formatted values
          .title(d => `${d.key}\nActions: ${d.value}`)
          // Add inner radius for donut chart style
          .innerRadius(40)
          // Add padding between slices
          .slicesCap(10)
          .externalLabels(30)
          .drawPaths(true)
          .externalRadiusPadding(30);

        // Add custom styling to the pie chart
        activityTypeChart.on('renderlet', function(chart) {
          // Style the pie slices
          chart.selectAll('.pie-slice')
            .style('stroke', '#fff')
            .style('stroke-width', '1px');

          // Style the labels
          chart.selectAll('.pie-label')
            .style('font-size', '12px')
            .style('fill', '#333')
            .style('font-weight', 'bold');

          // Style the legend text
          chart.selectAll('.dc-legend-item text')
            .style('font-size', '12px')
            .style('fill', '#333');
        });

        // Store chart references for resize handling
        this.charts.activity.userActivityChart = userActivityChart;
        this.charts.activity.activityTypeChart = activityTypeChart;

        // Render all charts
        dc.renderAll();

        console.log("Activity charts rendered successfully");
      } catch (error) {
        console.error("Error rendering activity charts:", error);

        // Display error message in the chart containers
        d3.select("#activity-by-user-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);
        d3.select("#activity-by-type-chart")
          .html(`<div class='empty-chart'>Error rendering chart: ${error.message}</div>`);
      }
    },

    exportReport() {
      this.showExportOptions = true;
    },

    downloadReport() {
      // Implementation for downloading report in selected format
      this.showExportOptions = false;

      if (this.exportFormat === "csv") {
        this.exportToCSV();
      } else if (this.exportFormat === "excel") {
        this.exportToExcel();
      } else if (this.exportFormat === "pdf") {
        this.exportToPDF();
      }
    },

    exportToCSV() {
      // Implementation for CSV export
      const headers = this.getReportHeaders();
      const csvContent = this.convertToCSV(this.reportData, headers);

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);

      link.setAttribute("href", url);
      link.setAttribute("download", `${this.selectedReportType}-report.csv`);
      link.style.visibility = "hidden";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    exportToExcel() {
      // Implementation for Excel export
      //this.$toast.info("Excel export functionality will be implemented soon");
    },

    exportToPDF() {
      // Implementation for PDF export
      //this.$toast.info("PDF export functionality will be implemented soon");
    },

    getReportHeaders() {
      if (this.selectedReportType === "performance") {
        return this.performanceColumns.map(col => ({
          label: col.label,
          key: col.prop
        }));
      } else if (this.selectedReportType === "issues") {
        return this.issueColumns.map(col => ({
          label: col.label,
          key: col.prop
        }));
      } else if (this.selectedReportType === "sla") {
        return this.slaColumns.map(col => ({
          label: col.label,
          key: col.prop
        }));
      } else if (this.selectedReportType === "activity") {
        return this.activityColumns.map(col => ({
          label: col.label,
          key: col.prop
        }));
      }

      return [];
    },

    convertToCSV(data, headers) {
      if (!data.length) return "";

      const headerRow = headers.map(h => h.label).join(",");
      const rows = data.map(item => {
        return headers.map(header => {
          const value = item[header.key];
          // Handle values with commas by wrapping in quotes
          return value !== undefined && value !== null
            ? `"${value}"`
            : '""';
        }).join(",");
      });

      return [headerRow, ...rows].join("\n");
    },

    handleResize() {
      // Redraw all charts when window is resized
      if (this.reportData.length > 0 && dc) {
        try {
          dc.renderAll();
          console.log("Charts redrawn on resize");
        } catch (error) {
          console.error("Error redrawing charts on resize:", error);
        }
      }
    }
  }
};
</script>

<style scoped>
.reports-container {
  min-height: 600px;
}

.w-100 {
  width: 100%;
}

.report-content {
  margin-top: 20px;
}

/* Chart Styles */
.dc-chart {
  width: 100%;
  height: 100%;
  font-family: 'Montserrat', sans-serif;
}

/* Axis styling */
.dc-chart .axis text {
  font-size: 12px;
  fill: #333;
  font-weight: 400;
}

.dc-chart .axis path,
.dc-chart .axis line {
  stroke: #999;
  stroke-width: 1px;
}

/* Grid lines */
.dc-chart .grid-line line {
  stroke: #e5e5e5;
  stroke-width: 1px;
  shape-rendering: crispEdges;
}

/* Bar chart styling */
.dc-chart .bar {
  stroke: none;
  transition: fill-opacity 0.2s ease;
}

.dc-chart .bar:hover {
  fill-opacity: 0.8;
}

/* Pie chart styling */
.dc-chart .pie-slice {
  fill-opacity: 0.8;
  stroke: #fff;
  stroke-width: 1px;
  transition: fill-opacity 0.2s ease;
}

.dc-chart .pie-slice:hover {
  fill-opacity: 0.9;
}

.dc-chart .pie-label {
  font-size: 12px;
  font-weight: bold;
  fill: #333;
}

/* Selected elements */
.dc-chart .selected path,
.dc-chart .selected circle {
  fill-opacity: 1;
  stroke-width: 2;
  stroke: #fff;
}

/* Legend styling */
.dc-chart .dc-legend-item text {
  font-size: 12px;
  fill: #333;
}

/* Empty chart message */
.dc-chart .empty-chart {
  text-align: center;
  font-style: italic;
  color: #888;
  padding: 20px;
  background-color: rgba(245, 245, 245, 0.7);
  border-radius: 4px;
  border: 1px dashed #ccc;
}

/* Enhanced tooltips */
.dc-tooltip {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
  border: 1px solid #302e77;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  z-index: 1000;
  font-size: 12px;
  font-weight: 500;
  white-space: pre-line;
  line-height: 1.4;
}

/* Chart loading indicator */
.chart-container {
  position: relative;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 5;
}

.chart-loading i {
  font-size: 2rem;
  color: #302e77;
  margin-bottom: 10px;
}
</style>
