@import "../variables";
@import "tags";

@mixin select($type, $color) {
  .select-#{$type}.el-select {
    .el-input {
      .el-input__suffix {
        display: flex;
        align-items: center;
      }

      &:hover {
        .el-input__inner {
          &::placeholder {
            color: white;
          }
          color: white;
          background-color: $color;
          border-color: $color;
        }

        .el-input__icon {
          color: white;
        }
      }

      .el-input__inner {
        background-color: white;
        border-color: if($color == $default-color, $medium-gray, $color);
        border-width: 1px;
        color: $color;
      }

      .el-input__icon {
        color: $color;
      }
    }

    .el-select__tags {
      .el-tag {
        border-radius: 3px;
        border-color: $color;
        background-color: white;
        color: $color;

        .el-tag__close {
          background-color: transparent;
          color: $color;
          opacity: 1;
          font-size: 12px;

          &:hover {
            background-color: transparent;
            color: $color;
            font-weight: bold;
            font-size: 18px;
            transition: font-size 0.2s ease-out;
          }
        }
      }
    }
  }

  .el-select-dropdown.is-multiple.select-#{$type},
  .el-select-dropdown__item.selected.select-#{$type} {
    color: $color !important;
  }
}

.el-select-dropdown {
  border-radius: 10px;
}

// Apply the mixin for different types
@include select('default', $default-color);
@include select('info', $info-color);
@include select('primary', $primary-color);
@include select('success', $success-color);
@include select('warning', $warning-color);
@include select('danger', $danger-color);