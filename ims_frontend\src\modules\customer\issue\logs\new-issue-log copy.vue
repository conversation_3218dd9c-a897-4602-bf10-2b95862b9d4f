<template>
    <div class="container mx-auto p-4">
      <card>
        <template slot="header">
          <h2 class="card-title">Update Issue Log</h2>
        </template>
        <div class="card-body">
          <form @submit.prevent="submitForm">
            <div class="row">
              <div class="col-md-6">
                <fg-input label="Issue Title" :value="issue.title" readonly />
              </div>
              <div class="col-md-6">
                <fg-input label="Issue Description" :value="issue.description" readonly />
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <fg-input label="Action Taken"  required>
                    <el-input type="textarea"  v-model="form.actionTaken" rows="3" placeholder="Enter action" />
                </fg-input>
              </div>
              <div class="col-md-6">
                <fg-input label="Status" required>
                  <el-select v-model="form.status" placeholder="Select Status" class="w-100" required>
                    <el-option value="Open">Open</el-option>
                    <el-option value="In Progress">In Progress</el-option>
                    <el-option value="Resolved">Resolved</el-option>
                    <el-option value="Closed">Closed</el-option>
                  </el-select>
                </fg-input>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <fg-input label="Comments" required>
                  <el-input type="textarea" v-model="form.comments" rows="3" placeholder="Enter comments" />
                </fg-input>
              </div>
            </div>
            <div class="mt-4">
              <button type="submit" class="btn btn-fill btn-info" :disabled="loading">
                {{ loading ? "Saving..." : "Save Log Entry" }}
              </button>
            </div>
          </form>
  
          <!-- Recent Log Entries -->
          <div class="mt-6">
            <h3 class="text-lg font-semibold mb-3">Recent Log Entries</h3>
            <el-table :data="recentLogs" stripe border style="width: 100%">
              <el-table-column prop="updatedAt" label="Date" :formatter="formatDateTime"></el-table-column>
              <el-table-column prop="actionTaken" label="Action"></el-table-column>
              <el-table-column label="Updated By" :formatter="getUserNameFormatter"></el-table-column>
              <el-table-column prop="comments" label="Comments"></el-table-column>
            </el-table>
          </div>
  
        </div>
  
        <!-- Issue Selector -->
        <!-- This part is removed as the issue ID will be taken from the URL -->
  
      </card>
  
    </div>
  </template>
  
  <script>
  // Import necessary components and libraries
  import { Select, Option, Input, Table, TableColumn } from "element-ui";
  import { FormGroupInput as FgInput } from "src/components/index"; // Adjust path as needed
  import Card from "src/components/Cards/Card.vue"; // Adjust path as needed
  import API from "@/services/api";
  
  export default {
    components: {
      "el-select": Select,
      "el-option": Option,
      "el-input": Input,
      "el-table": Table,
      "el-table-column": TableColumn,
      FgInput,
      Card,
    },
    data() {
      return {
        issueService: new API(process.env.VUE_APP_API_URL, "issues"),
        issueLogsService: new API(process.env.VUE_APP_API_URL, "issuelogs"),
        form: {
          actionTaken: "",
          status: "",
          comments: "",
        },
        issues: [],
        recentLogs: [],
        issueStatuses: ["New", "In Progress", "Resolved", "Closed"],
        loading: false,
        issue: {
          title: "",
          description: "",
        },
      };
    },
    mounted() {
      this.loadIssueDetails();
    },
    methods: {
      async loadIssueDetails() {
        // Extract the issue ID from the URL
        const issueId = this.$route.params.id; // Assuming you are using Vue Router
  
        if (!issueId) return;
  
        try {
          // Load the selected issue details
          const issueResponse = await this.issueService.getById(issueId);
          this.issue.title = issueResponse.title;
          this.issue.description = issueResponse.description;
  
          // Load related logs for the selected issue
          const logsResponse = await this.issueLogsService.getByIssueId(issueId);
          this.recentLogs = logsResponse;
  
          // Reset form fields for action taken and comments
          this.form.actionTaken = "";
          this.form.status = "";
          this.form.comments = "";
  
        } catch (error) {
          this.$alert.error("Failed to load issue details or logs");
        }
      },
  
      async submitForm() {
        try {
          this.loading = true;
  
          const logEntry = { 
            ...this.form,
            updatedAt: new Date(),
            issueID: this.$route.params.id, // Include the issue ID in the log entry
          };
  
          await this.issueLogsService.create(logEntry); // Save log entry
  
          // Refresh logs after saving
          await this.loadIssueDetails();
  
          alert("Log entry saved successfully");
  
        } catch (error) {
          console.error("Error saving log:", error);
          alert("Failed to save log entry");
  
        } finally {
          this.loading = false;
        }
      },
  
      formatDateTime(row, column, cellValue) {
        if (!cellValue) return "";
        return new Date(cellValue).toLocaleString();
      },
  
      getUserNameFormatter(row) {
        const user = this.users.find((u) => u.id === row.updatedBy);
        return user ? user.name : row.updatedBy;
      },
    },
  };
  </script>
  
  <style scoped>
  /* Add your scoped styles here */
  </style>
  
  