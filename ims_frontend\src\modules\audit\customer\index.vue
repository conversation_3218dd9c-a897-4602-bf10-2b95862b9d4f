<template>
  <div class="customer-management">
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">Customer List</h4></div>
          <div class="col-6 text-right">
            <router-link to="/audit/users" class="btn btn-warning mx-2">
             Users
            </router-link>
          </div>
        </div>
      </template>
      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search records"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table :data="queriedData" stripe border style="width: 100%">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
            ></el-table-column>

            <!-- Actions Column -->
            <el-table-column label="Actions" width="140">
              <template v-slot="{ row }">
                <button
                  class="btn btn-info btn-sm mr-2"
                  @click="editCustomer(row)"
                >
                  <i class="fa fa-pencil"></i>
                </button>
                <button
                  class="btn btn-danger btn-sm"
                  @click="deleteCustomer(row.customerId)"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {  Select, Option,Table, TableColumn, Input, Pagination } from "element-ui";
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import API from "@/services/api";

export default {
  components: {
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    "el-select": Select,
    "el-option": Option,
    LPagination,
  },
  data() {
    return {
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      tableColumns: [
        { prop: "i_customer", label: "ID", minWidth: 100 },
        { prop: "name", label: "Name", minWidth: 150 },
        { prop: "firstname", label: "Firstname", minWidth: 120 },
        { prop: "lastname", label: "Lastname", minWidth: 120 },
        { prop: "email", label: "Email", minWidth: 150 },
        { prop: "phone1", label: "Phone1", minWidth: 100 },
        { prop: "phone2", label: "phone2", minWidth: 100 },
        { prop: "city", label: "City", minWidth: 100 },
        { prop: "address_line_2", label: "Address", minWidth: 200 },
      ],
      customers: [],
      loading: false,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.customers?.filter((customer) =>
        Object.values(customer).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );

      return filtered?.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.customers?.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    async fetchCustomers() {
      this.loading = true;
      try {
        const response = await this.customerService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          searchQuery: this.searchQuery,
        });

        this.customers = response;
        this.pagination.total = response.total;
      } catch (error) {
        this.$alert.error(
          error.response?.message || "Failed to fetch customers"
        );
        console.error("Error fetching customers:", error);
      } finally {
        this.loading = false;
      }
    },
    handlePageChange(page) {
      this.pagination.currentPage = page;
      this.fetchCustomers();
    },
  },
  watch: {
    searchQuery() {
      this.pagination.currentPage = 1;
      this.fetchCustomers();
    },
  },
  created() {
    this.fetchCustomers();
  },
};
</script>

<style scoped>
.el-table {
  margin-top: 20px;
}

.el-pagination {
  margin-top: 20px;
}

.el-input {
  margin-bottom: 20px;
}
</style>