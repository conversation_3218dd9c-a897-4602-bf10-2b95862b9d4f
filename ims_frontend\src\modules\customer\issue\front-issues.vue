<template>
  <div class="issues-management">
    <!-- Issue Form -->
    <card v-if="showIssueForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">{{ isEditing ? "Edit Issue" : "Add Issue" }}</h4>
      </template>
      <form @submit.prevent="submitIssue">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Customer">
              <el-select
                v-model="currentIssue.customerId"
                placeholder="Select customer"
                filterable
                class="w-100"
              >
                <el-option
                  v-for="customer in customers"
                  :key="customer.customerId"
                  :label="customer.name + ' (' + customer.account + ')'"
                  :value="customer.customerId"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Service">
              <el-select
                v-model="currentIssue.serviceId"
                placeholder="Select service"
                filterable
                class="w-100"
              >
                <el-option
                  v-for="service in services"
                  :key="service.serviceId"
                  :label="`${service.serviceName} - ${service.serviceCode}`"
                  :value="service.serviceId"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Title" required>
              <input
                v-model="currentIssue.title"
                type="text"
                class="form-control"
                placeholder="Enter issue title"
                required
              />
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Category">
              <el-select
                v-model="currentIssue.category"
                placeholder="Select category"
                class="w-100"
              >
                <el-option
                  v-for="cat in issueCategories"
                  :key="cat"
                  :label="cat"
                  :value="cat"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Priority">
              <el-select
                v-model="currentIssue.priority"
                placeholder="Select priority"
                class="w-100"
              >
                <el-option
                  v-for="priority in issuePriorities"
                  :key="priority"
                  :label="priority"
                  :value="priority"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Status">
              <el-select
                v-model="currentIssue.status"
                placeholder="Select status"
                class="w-100"
              >
                <el-option
                  v-for="status in issueStatuses"
                  :key="status"
                  :label="status"
                  :value="status"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Phone" required>
              <input
                v-model="currentIssue.phone"
                type="phone"
                class="form-control"
                placeholder="Enter phone"
                required
              />
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Email" required>
              <input
                v-model="currentIssue.email"
                type="email"
                class="form-control"
                placeholder="Enter email"
              />
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <fg-input label="Description">
              <textarea
                v-model="currentIssue.description"
                class="form-control"
                rows="4"
                placeholder="Enter issue description"
              ></textarea>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <button type="submit" class="btn btn-fill btn-info">
              {{ isEditing ? "Update Issue" : "Add Issue" }}
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeIssueForm"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </card>

    <!-- Issues List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">Issues List</h4></div>
          <div class="col-6 text-right">
            <button class="btn btn-primary mx-2" @click="showIssueForm = true">
              Add Issue
            </button>
            <router-link
              to="/customer/assignments"
              class="btn btn-warning mx-2"
            >
              Assignments
            </router-link>
            <router-link to="/customer/timeline" class="btn btn-success mx-2">
              Issues Timeline
            </router-link>
          </div>
        </div>
      </template>

      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search issues"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table :data="queriedData" stripe border style="width: 100%">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            ></el-table-column>

            <!-- Assignment Column -->
            <el-table-column label="Assigned To" min-width="150">
              <template v-slot="{ row }">
                <div v-if="getAssignment(row.issueId)">
                  {{ formatAssignee(getAssignment(row.issueId)) }}
                </div>
                <router-link
                  v-else
                  class="btn btn-warning btn-sm"
                  :to="`/customer/assignments/${row.issueId}`"
                >
                  Assign Issue
                </router-link>
              </template>
            </el-table-column>

            <!-- Actions Column -->
            <el-table-column label="Actions" width="120">
              <template v-slot="{ row }">
                <router-link
                  v-if="getAssignment(row.issueId)"
                  :to="'/customer/issue-logs/' + row.issueId"
                  class="btn btn-success btn-sm mx-1"
                >
                  <i class="fa fa-plus"></i>
                </router-link>

                <router-link
                  v-if="getAssignment(row.issueId)"
                  :to="'/customer/issue-timeline/' + row.issueId"
                  class="btn btn-primary btn-sm mx-1"
                >
                  <i class="fa fa-history"></i>
                </router-link>
                <!-- <button class="btn btn-info btn-sm mx-1" @click="editIssue(row)">
                  <i class="fa fa-pencil"></i>
                </button> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import {
  Select,
  Option,
  Table,
  TableColumn,
  Input
} from "element-ui";
import API from "@/services/api";
import { slaCalculator } from "../../../services/slacalculator";
import moment from "moment";

export default {
  name: "IssuesManagement",
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input
  },
  data() {
    return {
      // Services for different APIs
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      teamMemberService: new API(process.env.VUE_APP_API_URL, "users"),

      // Search and Pagination
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },

      // Table Columns with Formatters
      tableColumns: [
        {
          prop: "issueId",
          label: "Issue ID",
          minWidth: 100,
        },
        {
          prop: "customerId",
          label: "Customer",
          minWidth: 140,
          formatter: this.formatCustomer,
        },
        {
          prop: "account",
          label: "Account",
          minWidth: 100,
          formatter: this.formatAccount,
        },
        {
          prop: "title",
          label: "Title",
          minWidth: 200,
        },
        {
          prop: "email",
          label: "Email",
          minWidth: 210,
        },
        {
          prop: "phone",
          label: "Phone",
          minWidth: 140,
        },
        {
          prop: "description",
          label: "Details",
          minWidth: 250,
        },
        {
          prop: "serviceId",
          label: "Service",
          minWidth: 90,
          formatter: this.formatService,
        },
        {
          prop: "category",
          label: "Category",
          minWidth: 100,
        },
        {
          prop: "priority",
          label: "Priority",
          minWidth: 80,
        },
        {
          prop: "status",
          label: "Status",
          minWidth: 70,
        },
        {
          prop: "occuredAt",
          label: "Occurred At",
          minWidth: 120,
          formatter: this.formatDateTime,
        },
        {
          prop: "reportedAt",
          label: "Reported At",
          minWidth: 120,
          formatter: this.formatDateTime,
        },
      ],

      // Dropdown Options
      issueCategories: ["Internal", "Customer"],
      issuePriorities: ["Low", "Medium", "High", "Critical"],
      issueStatuses: ["New", "In Progress"],

      // Data Arrays
      issues: [],
      customers: [],
      services: [],
      assignments: [],
      teamMembers: [],

      // Current Issue for Form
      currentIssue: {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
      },
      dueDate:"",
      // Form Control
      showIssueForm: false,
      isEditing: false,
      showAssignmentModal: false,
      selectedAssignee: "",
      currentIssueForAssignment: null,
    };
  },
  computed: {
    queriedData() {
      let filtered = this.issues.filter((issue) =>
        Object.values(issue).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.issues.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },
  methods: {
    onDialogOpen() {
      const selectComponent = this.$refs.selectRef;
      if (selectComponent) {
        console.log('el-select is now accessible', selectComponent);
        // Access the value of the select component
        console.log('Selected value:', selectComponent.value);
      }
    },
    setTechnician(){
      const selectComponent = this.$refs.selectRef;
      if (selectComponent) {
        console.log('el-select is now accessible', selectComponent);
        console.log(selectComponent.value)
      }
      //console.log(val)
    },
    // Formatting Methods for Table
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },

    formatCustomer(row, column, cellValue) {
      const customer = this.customers.find((c) => c.customerId === cellValue);
      return customer ? `${customer.name}` : "Unknown Customer";
    },

    formatAccount(row, column, cellValue) {
      const customer = this.customers.find(
        (c) => c.customerId === row.customerId
      );
      return customer ? `${customer.account}` : "Unknown Customer";
    },

    formatService(row, column, cellValue) {
      const service = this.services.find((s) => s.serviceId === cellValue);
      return service ? service.serviceName : "Unknown Service";
    },

    // Fetch Methods
    async fetchIssues() {
      try {
        const response = await this.issueService.getAll({
          page: this.pagination.currentPage,
          limit: this.pagination.perPage,
          searchQuery: this.searchQuery,
        });
        this.issues = response;
        this.pagination.total = response.total;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch Issues");
      }
    },

    async fetchCustomers() {
      try {
        this.customers = await this.customerService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },

    async fetchServices() {
      try {
        this.services = await this.serviceService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },

    async fetchAssignments() {
      try {
        this.assignments = await this.assignmentService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Assignments");
      }
    },

    async fetchTeamMembers() {
      try {
        this.teamMembers = await this.teamMemberService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Team Members");
      }
    },

    // Assignment Methods
    getAssignment(issueId) {
      return this.assignments.find((a) => a.issueId === issueId);
    },

    formatAssignee(assignment) {
      const member = this.teamMembers.find(
        (m) => m.id === assignment.assignedTo
      );
      return member ? member.fullName : "Unknown";
    },

    showAssignModal(issue) {
      this.currentIssueForAssignment = issue;
      this.selectedAssignee = null;
      this.showAssignmentModal = true;
    },

    async assignIssue() {
      console.log(this.selectedAssignee)
      try {
        let startDate=new Date();
        await this.GetSLAHrsPriorty(this.currentIssueForAssignment.issueId, startDate)
        await this.assignmentService.create({
          issueId: this.currentIssueForAssignment.issueId,
          AssignedTo: this.selectedAssignee,
          assignedAt: new Date(),
          startDate,
          dueDate:this.dueDate
        });

        await this.fetchAssignments();
        this.showAssignmentModal = false;
        this.$alert.success("Issue assigned successfully");
      } catch (error) {
        console.log(error)
        this.$alert.error("Failed to assign issue");
      }
    },
    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },
    async GetSLAHrsPriorty(issueId, start) {
      let issue = await this.getIssue(issueId);

      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.customerId,
        issue.serviceId,
        start
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.dueDate = result.dueDate;
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },
    // CRUD Methods
    async submitIssue() {
      if (this.isEditing) {
        await this.updateIssue();
      } else {
        await this.addIssue();
      }
    },

    async addIssue() {
      try {
        const newObject = { ...this.currentIssue };
        delete newObject.issueId;

        await this.issueService.create(newObject);
        this.$alert.success("Issue added successfully");
        await this.loadall();
        this.closeIssueForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to add Issue"
        );
      }
    },

    async updateIssue() {
      try {
        await this.issueService.update(
          this.currentIssue.issueId,
          this.currentIssue
        );
        await this.loadall();
        this.$alert.success("Issue updated successfully");
        this.closeIssueForm();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update Issue"
        );
      }
    },

    editIssue(issue) {
      this.currentIssue = { ...issue };
      this.isEditing = true;
      this.showIssueForm = true;
    },

    async loadall() {
      await Promise.all([
        this.fetchIssues(),
        this.fetchCustomers(),
        this.fetchServices(),
        this.fetchAssignments(),
        this.fetchTeamMembers(),
      ]);
    },

    closeIssueForm() {
      this.isEditing = false;
      this.showIssueForm = false;
      this.currentIssue = {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
      };
    },
  },

  async created() {
    await this.loadall();
  },

  watch: {
    // Watch for changes in search query and pagination
    searchQuery() {
      this.pagination.currentPage = 1;
      this.fetchIssues();
    },
    "pagination.currentPage"() {
      this.fetchIssues();
    },
    "pagination.perPage"() {
      this.pagination.currentPage = 1;
      this.fetchIssues();
    },
  },
};
</script>

<style>
.el-select {
  width: 100%;
}

.el-dialog__body {
  padding: 20px;
}

.form-group label {
  font-weight: bold;
  margin-bottom: 10px;
}
</style>