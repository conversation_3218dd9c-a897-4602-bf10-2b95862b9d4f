{"name": "issue-management-system", "version": "1.4.2", "description": "MTL Issue Management System", "author": "micha<PERSON>.<EMAIL>", "private": true, "scripts": {"build": "vue-cli-service build", "dev": "vue-cli-service serve --open", "lint": "vue-cli-service lint"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fullcalendar/core": "5.11.3", "@fullcalendar/daygrid": "5.11.3", "@fullcalendar/interaction": "5.11.3", "@fullcalendar/timegrid": "5.11.3", "@fullcalendar/vue": "5.11.3", "axios": "^1.7.9", "bootstrap": "4.6.0", "chart.js": "^4.4.7", "chartist": "0.11.4", "core-js": "3.26.1", "crossfilter2": "^1.5.4", "d3": "^7.6.1", "datamaps": "0.5.9", "dc": "^4.2.7", "easy-pie-chart": "2.1.7", "element-ui": "^2.15.14", "es6-promise": "4.2.8", "fuse.js": "6.6.2", "google-maps": "4.3.3", "moment": "^2.30.1", "perfect-scrollbar": "1.5.5", "sweetalert2": "11.6.14", "topojson": "3.0.2", "v-tooltip": "2.1.3", "vee-validate": "3.4.11", "vue": "2.7.14", "vue-chartjs": "^5.3.2", "vue-clickaway": "2.2.2", "vue-form-wizard": "0.8.4", "vue-nav-tabs": "0.5.7", "vue-router": "3.5.2", "vue2-google-maps": "0.10.7", "vue2-transitions": "0.3.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.13", "@vue/cli-plugin-eslint": "4.5.13", "@vue/cli-service": "4.5.13", "babel-plugin-component": "1.1.1", "es6-promise": "4.2.8", "eslint": "7.32.0", "eslint-plugin-vue": "7.20.0", "sass": "1.56.1", "sass-loader": "10.2.1", "vue-template-compiler": "2.7.14"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}