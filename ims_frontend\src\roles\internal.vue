<template>
  <div class="py-3">
    <!-- Quick Action Buttons -->
    <div class="row mb-4">
      <div class="col-12 text-start">
        <button class="btn btn-primary me-2 mr-3" @click="openReportIssue">
          <i class="fas fa-plus-circle me-1"></i> Report New Issue
        </button>

        <button class="btn btn-primary me-2 ml-3" @click="openMyIssues">
          <i class="fas fa-plus-circle me-1"></i> My Issues
        </button>
      </div>
    </div>

    <!-- Stats Cards Row -->
    <div class="row mb-4">
      <div class="col-12 col-sm-6 col-xl-3 mb-3" v-for="(stat, index) in statsList" :key="index">
        <stats-card
          :title="stat.title"
          :value="stat.value"
          :subtitle="stat.subtitle"
          :icon="stat.icon"
          :color="stat.color"
          :loading="loading"
        >
          <template #footer>
            <div v-if="stat.footer" class="d-flex justify-content-between">
              <small v-for="(footerText, idx) in stat.footer" :key="idx">{{ footerText }}</small>
            </div>
          </template>
        </stats-card>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
      <div class="col-12 col-lg-8 mb-3">
        <div class="card h-100">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Issue Trends</h5>
            <select v-model="trendsPeriod" class="form-select form-select-sm w-auto">
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
          </div>
          <div class="card-body">
            <canvas ref="trendsChart" v-if="!loading"></canvas>
            <div v-else class="d-flex justify-content-center align-items-center h-100">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-12 col-lg-4 mb-3">
        <div class="card h-100">
          <div class="card-header">
            <h5 class="card-title mb-0">Issues by Category</h5>
          </div>
          <div class="card-body">
            <canvas ref="categoryChart" v-if="!loading"></canvas>
            <div v-else class="d-flex justify-content-center align-items-center h-100">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Issues Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Recent Issues</h5>
            <button class="btn btn-sm btn-outline-primary" @click="refreshIssues">
              <i class="fas fa-sync-alt"></i> Refresh
            </button>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Title</th>
                    <th>Customer</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Assigned To</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody v-if="!loading">
                  <tr v-for="issue in recentIssues" :key="issue.id">
                    <td>#{{ issue.id }}</td>
                    <td>{{ issue.title }}</td>
                    <td>{{ issue.customer }}</td>
                    <td>
                      <span :class="getPriorityBadgeClass(issue.priority)">
                        {{ issue.priority }}
                      </span>
                    </td>
                    <td>
                      <span :class="getStatusBadgeClass(issue.status)">
                        {{ issue.status }}
                      </span>
                    </td>
                    <td>{{ issue.assignedTo || 'Unassigned' }}</td>
                    <td>{{ formatDate(issue.createdAt) }}</td>
                    <td>
                      <button class="btn btn-sm btn-outline-primary me-1" @click="viewIssue(issue.id)">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" @click="editIssue(issue.id)">
                        <i class="fas fa-edit"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
                <tbody v-else>
                  <tr>
                    <td colspan="8" class="text-center">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="card-footer text-center">
            <button class="btn btn-outline-primary" @click="viewAllIssues">
              View All Issues
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

  
  <script>

  import StatsCard from '@/components/Cards/StatsCard.vue';
  
  export default {
    name: 'Dashboard',
    
    components: {
      StatsCard
    },
  
    data() {
      return {
        loading: true,
        trendsPeriod: '30',
        trendsChart: null,
        categoryChart: null,
        stats: {
          activeIssues: 0,
          criticalIssues: 0,
          highPriorityIssues: 0,
          slaComplianceRate: 0,
          issuesWithinSLA: 0,
          pendingAssignments: 0,
          recentAssignments: 0,
          satisfactionRate: 0,
          totalFeedbacks: 0
        },
        recentIssues: []
      };
    },
  
    async mounted() {
      await this.fetchDashboardData();
      this.startAutoRefresh();
    },
  
    beforeDestroy() {
      this.stopAutoRefresh();
      if (this.trendsChart) {
        this.trendsChart.destroy();
      }
      if (this.categoryChart) {
        this.categoryChart.destroy();
      }
    },
  
    methods: {
      async fetchDashboardData() {
        try {
          this.loading = true;
          const [statsResponse, issuesResponse, trendsResponse, categoriesResponse] = await Promise.all([
            this.$api.get('/api/dashboard/stats'),
            this.$api.get('/api/issues/recent'),
            this.$api.get(`/api/dashboard/trends/${this.trendsPeriod}`),
            this.$api.get('/api/dashboard/categories')
          ]);
  
          this.stats = statsResponse.data;
          this.recentIssues = issuesResponse.data;
          this.updateCharts(trendsResponse.data, categoriesResponse.data);
        } catch (error) {
          //console.error('Error fetching dashboard data:', error);
          this.$toast.error('Failed to load dashboard data');
        } finally {
          this.loading = false;
        }
      },
  
      updateCharts(trendsData, categoryData) {
        // Update Trends Chart
        if (this.trendsChart) {
          this.trendsChart.destroy();
        }
        
        const trendsCtx = this.$refs.trendsChart.getContext('2d');
        this.trendsChart = new Chart(trendsCtx, {
          type: 'line',
          data: {
            labels: trendsData.dates,
            datasets: [
              {
                label: 'New Issues',
                borderColor: '#ff6384',
                data: trendsData.newIssues,
                fill: false
              },
              {
                label: 'Resolved Issues',
                borderColor: '#36a2eb',
                data: trendsData.resolvedIssues,
                fill: false
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              yAxes: [{
                ticks: {
                  beginAtZero: true
                }
              }]
            }
          }
        });
  
        // Update Category Chart
        if (this.categoryChart) {
          this.categoryChart.destroy();
        }
  
        const categoryCtx = this.$refs.categoryChart.getContext('2d');
        this.categoryChart = new Chart(categoryCtx, {
          type: 'doughnut',
          data: {
            labels: categoryData.map(item => item.category),
            datasets: [{
              data: categoryData.map(item => item.count),
              backgroundColor: [
                '#ff6384',
                '#36a2eb',
                '#cc65fe',
                '#ffce56'
              ]
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false
          }
        });
      },
  
      getPriorityBadgeClass(priority) {
        const classes = {
          Critical: 'badge bg-danger',
          High: 'badge bg-warning',
          Medium: 'badge bg-info',
          Low: 'badge bg-secondary'
        };
        return classes[priority] || 'badge bg-secondary';
      },
  
      getStatusBadgeClass(status) {
        const classes = {
          New: 'badge bg-info',
          'In Progress': 'badge bg-warning',
          Resolved: 'badge bg-success',
          Closed: 'badge bg-secondary'
        };
        return classes[status] || 'badge bg-secondary';
      },
  
      formatDate(date) {
        return formatDistanceToNow(new Date(date), { addSuffix: true });
      },
  
      startAutoRefresh() {
        this.refreshInterval = setInterval(this.fetchDashboardData, 300000); // 5 minutes
      },
  
      stopAutoRefresh() {
        if (this.refreshInterval) {
          clearInterval(this.refreshInterval);
        }
      },
  
      async refreshIssues() {
        await this.fetchDashboardData();
        this.$toast.success('Dashboard data refreshed');
      },
  
      openReportIssue() {
        this.$router.push('./internal/issues');
      },
  
      openMyIssues() {
        this.$router.push('./internal/issues/my-issues');
      },
  
      openDashboardSettings() {
        this.$router.push('/dashboard/settings');
      },
  
      viewIssue(id) {
        this.$router.push(`/issues/${id}`);
      },
  
      editIssue(id) {
        this.$router.push(`/issues/${id}/edit`);
      },
  
      viewAllIssues() {
        this.$router.push('/issues');
      }
    },
  
    watch: {
      trendsPeriod: {
        handler: 'fetchDashboardData',
        immediate: false
      }
    }
  };
  </script>
  
  <style scoped>
  .card-tools {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
  
  .table td {
    vertical-align: middle;
  }
  
  .badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
  }
  
  canvas {
    min-height: 300px;
  }
  </style>