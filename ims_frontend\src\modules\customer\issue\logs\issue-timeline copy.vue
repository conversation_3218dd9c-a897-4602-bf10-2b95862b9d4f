<template>
  <div class="issue-timeline-container">
    <el-card class="timeline-wrapper">
      <div slot="header" class="clearfix">
        <span class="font-bold text-lg">Issue Timeline</span>
        <router-link
          type="primary"
          icon="el-icon-plus"
          size="small"
          class="float-right el-button btn-primary"
          :to="'/admin/issues'"
        >
          Create Issue
        </router-link>
      </div>

      <el-timeline>
        <el-timeline-item
          placement="top"
          v-for="issue in issues"
          :key="issue.id"
          :timestamp="formatDate(issue.createdAt)"
          :type="getIssueStatusColor(issue.status)"
        >
          <el-card @click.native="openIssueDetails(issue)" class="issue-card">
            <div class="flex justify-between items-center">
              <div>
                <h3 class="text-lg font-semibold">{{ issue.title }}</h3>
                <p class="text-gray-600">{{ issue.description }}</p>
              </div>
              <el-tag :type="getIssueStatusColor(issue.status)" size="mini">
                {{ issue.status }}
              </el-tag>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- Issue Details Dialog -->
    <el-dialog
      :visible.sync="showIssueDetailsDialog"
      :title="selectedIssue ? selectedIssue.title : 'Issue Details'"
      width="70%"
    >
      <el-steps
        :active="getActiveStep(selectedIssue)"
        finish-status="success"
        align-center
      >
        <el-step title="Open" description="Issue Reported"></el-step>
        <el-step
          title="In Progress"
          description="Under Investigation"
        ></el-step>
        <el-step title="Resolved" description="Solution Implemented"></el-step>
        <el-step title="Closed" description="Issue Completed"></el-step>
      </el-steps>
      <br />
      <el-timeline class="mt-6">
        <el-timeline-item
          v-for="log in selectedIssueLogs"
          placement="top"
          :key="log.id"
          :timestamp="formatDate(log.createdAt)"
          :type="getLogStatusColor(log.type)"
        >
          <el-card shadow>
            <div class="flex justify-between items-center">
              <div>
                <h4 class="font-semibold">{{ log.title }}</h4>
                <p class="text-gray-600">{{ log.description }}</p>
              </div>
              <el-tag :type="getLogStatusColor(log.type)" size="mini">
                {{ log.type }}
              </el-tag>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>

      <div class="mt-4 flex">
        <el-select
          v-model="newLogType"
          placeholder="Select Action Type"
          class="mr-2"
        >
          <el-option
            v-for="type in logTypes"
            :key="type"
            :label="type"
            :value="type"
          ></el-option>
        </el-select>
        <el-input
          v-model="newLogDescription"
          placeholder="Add action details"
          class="mr-2"
        ></el-input>
        <el-button type="primary" @click="addIssueLog" :disabled="!isLogValid">
          Add Log
        </el-button>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="updateIssueStatus">Update Status</el-button>
        <el-button type="primary" @click="showIssueDetailsDialog = false"
          >Close</el-button
        >
      </div>
    </el-dialog>

    <!-- New Issue Dialog -->
    <el-dialog
      title="Create New Issue"
      :visible.sync="showNewIssueDialog"
      width="500px"
    >
      <el-form :model="newIssue" label-width="100px">
        <el-form-item label="Title">
          <el-input v-model="newIssue.title"></el-input>
        </el-form-item>
        <el-form-item label="Description">
          <el-input
            type="textarea"
            v-model="newIssue.description"
            :rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="Priority">
          <el-select v-model="newIssue.priority" style="width: 100%">
            <el-option
              v-for="priority in priorities"
              :key="priority"
              :label="priority"
              :value="priority"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showNewIssueDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          @click="createNewIssue"
          :disabled="!isNewIssueValid"
        >
          Create
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  Card,
  Button,
  Timeline,
  TimelineItem,
  Dialog,
  Form,
  FormItem,
  Input,
  Select,
  Option,
  Steps,
  Step,
  Tag,
} from "element-ui";

export default {
  name: "IssueTimeline",

  // Register components locally
  components: {
    ElCard: Card,
    ElButton: Button,
    ElTimeline: Timeline,
    ElTimelineItem: TimelineItem,
    ElDialog: Dialog,
    ElForm: Form,
    ElFormItem: FormItem,
    ElInput: Input,
    ElSelect: Select,
    ElOption: Option,
    ElSteps: Steps,
    ElStep: Step,
    ElTag: Tag,
  },
  data() {
    return {
      // Issue Management
      issues: [],
      selectedIssue: null,
      selectedIssueLogs: [],

      // Dialogs
      showIssueDetailsDialog: false,
      showNewIssueDialog: false,

      // New Issue Data
      newIssue: {
        title: "",
        description: "",
        priority: "Medium",
      },

      // New Log Data
      newLogType: "",
      newLogDescription: "",

      // Configurations
      priorities: ["Low", "Medium", "High", "Critical"],
      logTypes: ["Investigation", "Update", "Action", "Resolution"],
      issueStatuses: ["Open", "In Progress", "Resolved", "Closed"],
    };
  },

  computed: {
    isNewIssueValid() {
      return (
        this.newIssue.title.trim() !== "" &&
        this.newIssue.description.trim() !== ""
      );
    },
    isLogValid() {
      return this.newLogType && this.newLogDescription.trim() !== "";
    },
  },

  methods: {
    // Date Formatting
    formatDate(date) {
      return new Date(date).toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    // Status Color Mapping
    getIssueStatusColor(status) {
      const colorMap = {
        Open: "warning",
        "In Progress": "primary",
        Resolved: "success",
        Closed: "info",
      };
      return colorMap[status] || "warning";
    },

    getLogStatusColor(type) {
      const colorMap = {
        Investigation: "warning",
        Update: "primary",
        Action: "danger",
        Resolution: "success",
      };
      return colorMap[type] || "info";
    },

    // Active Step Calculation
    getActiveStep(issue) {
      if (!issue) return 0;
      const statuses = ["Open", "In Progress", "Resolved", "Closed"];
      return statuses.indexOf(issue.status);
    },

    // Issue Management
    openIssueDetails(issue) {
      this.selectedIssue = issue;
      this.selectedIssueLogs = this.getMockLogsForIssue(issue.id);
      this.showIssueDetailsDialog = true;
    },

    openNewIssueDialog() {
      this.showNewIssueDialog = true;
      this.resetNewIssueForm();
    },

    createNewIssue() {
      if (!this.isNewIssueValid) return;

      const newIssue = {
        id: Date.now(),
        ...this.newIssue,
        status: "Open",
        createdAt: new Date(),
      };

      this.issues.push(newIssue);
      this.showNewIssueDialog = false;
    },

    addIssueLog() {
      if (!this.isLogValid) return;

      const newLog = {
        id: Date.now(),
        title: this.newLogType,
        description: this.newLogDescription,
        type: this.newLogType,
        createdAt: new Date(),
      };

      this.selectedIssueLogs.push(newLog);
      this.newLogType = "";
      this.newLogDescription = "";
    },

    updateIssueStatus() {
      if (!this.selectedIssue) return;

      const currentIndex = this.issueStatuses.indexOf(
        this.selectedIssue.status
      );
      const nextStatusIndex = (currentIndex + 1) % this.issueStatuses.length;

      this.selectedIssue.status = this.issueStatuses[nextStatusIndex];
    },

    resetNewIssueForm() {
      this.newIssue = {
        title: "",
        description: "",
        priority: "Medium",
      };
    },

    // Mock Data Generation (replace with actual API calls)
    getMockLogsForIssue(issueId) {
      return [
        {
          id: 1,
          title: "Initial Investigation",
          description: "Preliminary analysis of the issue",
          type: "Investigation",
          createdAt: new Date(Date.now() - 86400000).toISOString(),
        },
        {
          id: 2,
          title: "Action Required",
          description: "Need to implement fix",
          type: "Action",
          createdAt: new Date(),
        },
      ];
    },
  },

  mounted() {
    // Initialize with some mock issues
    this.issues = [
      {
        id: 1,
        title: "Database Connection Issue",
        description: "Intermittent database connection failures",
        status: "Open",
        priority: "High",
        createdAt: new Date(),
      },
      {
        id: 2,
        title: "Performance Bottleneck",
        description: "Slow response times during peak hours",
        status: "In Progress",
        priority: "Critical",
        createdAt: new Date(Date.now() - 86400000).toISOString(),
      },
    ];
  },
};
</script>

<style scoped>
.issue-timeline-container {
  @apply max-w-4xl mx-auto p-4;
}

.issue-card {
  margin-bottom: 10px;
  cursor: pointer; /* Default cursor for the entire card */
  transition: box-shadow 0.3s;
}
.issue-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer; /* Change to hand pointer on hover */
}
</style>