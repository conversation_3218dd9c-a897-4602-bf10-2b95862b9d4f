<template>
  <div class="wrapper" :class="{ 'nav-open': $sidebar.showSidebar }">
    <notifications></notifications>
    <side-bar>
      <user-menu :title="name"></user-menu>
      <mobile-menu></mobile-menu>
      <template slot="links">
        <sidebar-item
          :link="{
            name: 'Dashboard',
            icon: 'fas fa-dashboard',
            path: `/customer/dashboard`,
          }"
        ></sidebar-item>


        <sidebar-item
          :link="{
            name: 'Issues',
            icon: 'fas fa-circle-exclamation',
            path: `/customer/issues`,
          }"
        ></sidebar-item>
        <sidebar-item
          :link="{
            name: 'Reports',
            icon: 'fas fa-chart-bar',
            path: `/customer/reports`,
          }"
        ></sidebar-item>
        </template>
      </side-bar>

    <div class="main-panel">
      <top-navbar :account="cleanRole"></top-navbar>

      <dashboard-content @click.native="toggleSidebar"> </dashboard-content>

      <content-footer :data="cleanRole"></content-footer>
    </div>
  </div>
</template>

<script>
import TopNavbar from "./TopNavbar.vue";
import ContentFooter from "./ContentFooter.vue";
import DashboardContent from "./Content.vue";
import MobileMenu from "./Extra/MobileMenu.vue";
import UserMenu from "./Extra/UserMenu.vue";
import PerfectScrollbar from "perfect-scrollbar";
import "perfect-scrollbar/css/perfect-scrollbar.css";

function hasElement(className) {
  return document.getElementsByClassName(className).length > 0;
}

function initScrollbar(className) {
  if (hasElement(className)) {
    new PerfectScrollbar(`.${className}`);
  } else {
    // try to init it later in case this component is loaded async
    setTimeout(() => {
      initScrollbar(className);
    }, 100);
  }
}

export default {
  components: {
    TopNavbar,
    ContentFooter,
    DashboardContent,
    MobileMenu,
    UserMenu,
  },
  data() {
    return {
      role: "",
      cleanRole: "",
      name: "",
    };
  },
  methods: {
    toggleSidebar() {
      if (this.$sidebar.showSidebar) {
        this.$sidebar.displaySidebar(false);
      }
    },
    initScrollbar() {
      let docClasses = document.body.classList;
      let isWindows = navigator.platform.startsWith("Win");
      if (isWindows) {
        initScrollbar("main-panel");
        docClasses.add("perfect-scrollbar-on");
      } else {
        docClasses.add("perfect-scrollbar-off");
      }
    },
    initUserData() {
      const user = JSON.parse(localStorage.getItem("user") || '{}');
      this.name = user.fullName || '';

      // Handle role with proper null checking
      const userRole = localStorage.getItem("userRole");
      this.role = userRole || "";
      this.cleanRole = userRole ? userRole.replace("-", " ") : "";
    }
  },
  mounted() {
    this.initScrollbar();
    this.initUserData();
  },
};
</script>
