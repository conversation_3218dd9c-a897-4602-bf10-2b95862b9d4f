<template>
  <div>
    <!-- Filters Section -->
    <div class="row mb-3">
      <div class="col-md-3">
        <el-select v-model="filters.department" placeholder="Select Department" @change="onDepartmentChange">
          <el-option
            v-for="department in departments"
            :key="department.id"
            :label="department.name"
            :value="department.id"
          />
        </el-select>
      </div>
      <div class="col-md-3">
        <el-select v-model="filters.technician" placeholder="Select Technician" :loading="loadingTechnicians">
          <el-option key="all" label="All Technicians" :value="null" />
          <el-option
            v-for="tech in filteredTechnicians"
            :key="tech.id"
            :label="tech.fullName"
            :value="tech.id"
          />
        </el-select>
      </div>
      <div class="col-md-2">
        <el-select v-model="filters.status" placeholder="Select Status">
          <el-option
            v-for="status in statuses"
            :key="status"
            :label="status"
            :value="status"
          />
        </el-select>
      </div>
      <div class="col-md-2">
        <el-date-picker
          v-model="filters.dateRange"
          type="daterange"
          range-separator="to"
          start-placeholder="Start date"
          end-placeholder="End date"
        />
      </div>
      <div class="col-md-2">
        <el-input v-model="filters.searchQuery" placeholder="Search..." />
      </div>
    </div>

    <!-- Existing Dashboard Content -->
    <div class="row">
      <div class="col-xl-3 col-md-6">
        <stats-card title="150GB" subTitle="Numbers">
          <div slot="header" class="icon-warning">
            <i class="nc-icon nc-chart text-warning"></i>
          </div>
          <template slot="footer">
            <i class="fa fa-refresh"></i>Updated now
          </template>
        </stats-card>
      </div>

      <div class="col-xl-3 col-md-6">
        <stats-card title="$ 1,345" subTitle="Revenue">
          <div slot="header" class="icon-success">
            <i class="nc-icon nc-light-3 text-success"></i>
          </div>
          <template slot="footer">
            <i class="fa fa-calendar-o"></i>Last day
          </template>
        </stats-card>
      </div>

      <div class="col-xl-3 col-md-6">
        <stats-card title="23" subTitle="Errors">
          <div slot="header" class="icon-danger">
            <i class="nc-icon nc-vector text-danger"></i>
          </div>
          <template slot="footer">
            <i class="fa fa-clock-o"></i>Last day
          </template>
        </stats-card>
      </div>

      <div class="col-xl-3 col-md-6">
        <stats-card title="+45K" subTitle="Followers">
          <div slot="header" class="icon-info">
            <i class="nc-icon nc-favourite-28 text-primary"></i>
          </div>
          <template slot="footer">
            <i class="fa fa-refresh"></i>Updated now
          </template>
        </stats-card>
      </div>

    </div>
    <div class="row">

      <div class="col-md-4">
        <chart-card :chart-data="pieChart.data" chart-type="Pie">
          <template slot="header">
            <h4 class="card-title">Email Statistics</h4>
            <p class="card-category">Last Campaign Performance</p>
          </template>
          <template slot="footer">
            <div class="legend">
              <i class="fa fa-circle text-info"></i> Open
              <i class="fa fa-circle text-danger"></i> Bounce
              <i class="fa fa-circle text-warning"></i> Unsubscribe
            </div>
            <hr>
            <div class="stats">
              <i class="fa fa-clock-o"></i> Campaign sent 2 days ago
            </div>
          </template>
        </chart-card>
      </div>

      <div class="col-md-8">
        <chart-card :chart-data="lineChart.data"
                    :chart-options="lineChart.options"
                    :responsive-options="lineChart.responsiveOptions">
          <template slot="header">
            <h4 class="card-title">Users Behavior</h4>
            <p class="card-category">24 Hours performance</p>
          </template>
          <template slot="footer">
            <div class="legend">
              <i class="fa fa-circle text-info"></i> Open
              <i class="fa fa-circle text-danger"></i> Click
              <i class="fa fa-circle text-warning"></i> Click Second Time
            </div>
            <hr>
            <div class="stats">
              <i class="fa fa-history"></i> Updated 3 minutes ago
            </div>
          </template>
        </chart-card>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6">
        <chart-card
          :chart-data="barChart.data"
          :chart-options="barChart.options"
          :chart-responsive-options="barChart.responsiveOptions"
          chart-type="Bar">
          <template slot="header">
            <h4 class="card-title">2014 Sales</h4>
            <p class="card-category">All products including Taxes</p>
          </template>
          <template slot="footer">
            <div class="legend">
              <i class="fa fa-circle text-info"></i> Tesla Model S
              <i class="fa fa-circle text-danger"></i> BMW 5 Series
            </div>
            <hr>
            <div class="stats">
              <i class="fa fa-check"></i> Data information certified
            </div>
          </template>
        </chart-card>
      </div>

      <div class="col-md-6">
        <card class="card-tasks" title="Tasks" subTitle="Backend development">
          <l-table :data="tableData.data">
            <template slot-scope="{row}">
              <td>
                <Checkbox v-model="row.checked"></Checkbox>
              </td>
              <td>{{row.title}}</td>
              <td class="td-actions d-flex justify-content-end">
                <div class="btn btn-info btn-simple btn-link" v-tooltip.top-center="editTooltip">
                  <i class="fa fa-edit"></i>
                </div>
                <div class="btn btn-danger btn-simple btn-link" v-tooltip.top-center="deleteTooltip">
                  <i class="fa fa-times"></i>
                </div>
              </td>
            </template>
          </l-table>
          <div class="stats" slot="footer">
            <i class="fa fa-history"></i> Updated 3 minutes ago
          </div>
        </card>

      </div>
    </div>
  </div>
</template>
<script>
  import { ChartCard, StatsCard, Card, Table as LTable, Checkbox } from "src/components/index";
  import API from "@/services/api";

  export default {
    components: {
      Checkbox,
      Card,
      LTable,
      ChartCard,
      StatsCard
    },
    data () {
      return {
        filters: {
          department: null,
          status: null,
          dateRange: null,
          searchQuery: "",
          technician: null,
        },
        departments: [],
        statuses: ["New", "In Progress", "Resolved"],
        teamMembers: [],
        loadingTechnicians: false,
        editTooltip: 'Edit Task',
        deleteTooltip: 'Remove',
        pieChart: {
          data: {
            labels: ['40%', '20%', '40%'],
            series: [40, 20, 40]
          }
        },
        lineChart: {
          data: {
            labels: ['9:00AM', '12:00AM', '3:00PM', '6:00PM', '9:00PM', '12:00PM', '3:00AM', '6:00AM'],
            series: [
              [287, 385, 490, 492, 554, 586, 698, 695],
              [67, 152, 143, 240, 287, 335, 435, 437],
              [23, 113, 67, 108, 190, 239, 307, 308]
            ]
          },
          options: {
            low: 0,
            high: 800,
            showArea: false,
            height: '245px',
            axisX: {
              showGrid: false
            },
            lineSmooth: true,
            showLine: true,
            showPoint: true,
            fullWidth: true,
            chartPadding: {
              right: 50
            }
          },
          responsiveOptions: [
            ['screen and (max-width: 640px)', {
              axisX: {
                labelInterpolationFnc: function (value) {
                  return value[0]
                }
              }
            }]
          ]
        },
        barChart: {
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mai', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            series: [
              [542, 443, 320, 780, 553, 453, 326, 434, 568, 610, 756, 895],
              [412, 243, 280, 580, 453, 353, 300, 364, 368, 410, 636, 695]
            ]
          },
          options: {
            seriesBarDistance: 10,
            axisX: {
              showGrid: false
            },
            height: '245px'
          },
          responsiveOptions: [
            ['screen and (max-width: 640px)', {
              seriesBarDistance: 5,
              axisX: {
                labelInterpolationFnc (value) {
                  return value[0]
                }
              }
            }]
          ]
        },
        tableData: {
          data: [
            {id: 'id-1', title: 'Sign contract for "What are conference organizers afraid of?"', checked: false},
            {id: 'id-2', title: 'Lines From Great Russian Literature? Or E-mails From My Boss?', checked: true},
            {
              id: 'id-3',
              title: 'Flooded: One year later, assessing what was lost and what was found when a ravaging rain swept through metro Detroit',
              checked: true
            },
            {id: 'id-4', title: 'Create 4 Invisible User Experiences you Never Knew About', checked: false},
            {id: 'id-5', title: 'Read "Following makes Medium better"', checked: false},
            {id: 'id-6', title: 'Unfollow 5 enemies from twitter', checked: false}
          ]
        }
      }
    },
    computed: {
      filteredTechnicians() {
        // Filter technicians to only show those with usertype "internal"
        // and belonging to the selected department (if a department is selected)
        if (!this.teamMembers || this.teamMembers.length === 0) {
          return [];
        }

        let technicians = this.teamMembers.filter(user => user.userType === "internal");

        // If a department is selected, filter technicians by department
        if (this.filters.department) {
          technicians = technicians.filter(tech => tech.departmentId === this.filters.department);
        }

        return technicians;
      },
    },
    methods: {
      async fetchDepartments() {
        try {
          const response = await new API(process.env.VUE_APP_API_URL, "departments").getAll();
          this.departments = response;
        } catch (error) {
          //console.error("Failed to fetch departments", error);
        }
      },
      async fetchTeamMembers() {
        try {
          this.loadingTechnicians = true;
          const teamMemberService = new API(process.env.VUE_APP_API_URL, "users");
          this.teamMembers = await teamMemberService.getAll();
        } catch (error) {
          console.error("Failed to fetch team members", error);
        } finally {
          this.loadingTechnicians = false;
        }
      },
      onDepartmentChange() {
        // Reset technician filter when department changes
        this.filters.technician = null;
      },
    },
    mounted() {
      this.fetchDepartments();
      this.fetchTeamMembers();
    },
  }
</script>
<style>

</style>
