<template>
  <auth-layout pageClass="signup-page">
    <div class="row d-flex justify-content-center align-items-center mt-4">
      <div class="col-lg-4 col-md-6 col-sm-8">
        <ValidationObserver v-slot="{ handleSubmit }">
          <form @submit.prevent="handleSubmit(submit)">
            <fade-render-transition>
              <card>
                <div slot="header">
                  <h3 class="card-title text-center">Login</h3>
                </div>
                <div>
                  <ValidationProvider
                    name="email/username"
                    rules="required"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="text"
                      :error="failed ? 'The Username field is required' : null"
                      :hasSuccess="passed"
                      label="Email/Username"
                      v-model="email"
                    >
                    </fg-input>
                  </ValidationProvider>
                  <ValidationProvider
                    name="password"
                    rules="required|min:5"
                    v-slot="{ passed, failed }"
                  >
                    <fg-input
                      type="password"
                      :error="failed ? 'The Password field is required' : null"
                      :hasSuccess="passed"
                      label="Password"
                      v-model="password"
                    >
                    </fg-input>
                  </ValidationProvider>
                </div>
                <div class="text-center mt-3">
                  <button
                    type="submit"
                    class="btn btn-fill btn-info btn-round btn-wd"
                    :disabled="loading"
                  >
                    Login
                  </button>
                  <br />
                  <router-link to="/forgotpassword" class="card-category mt-2">
                    Forgot your password?
                  </router-link>

                  <div class="forgot">
                    <router-link
                      to="/signup"
                      class="card-category text-success"
                    >
                      Do not have an account? Signup
                    </router-link>
                  </div>
                </div>
              </card>
            </fade-render-transition>
          </form>
        </ValidationObserver>
      </div>
    </div>
  </auth-layout>
</template>

<script>
import { FadeRenderTransition } from "src/components/index";
import AuthLayout from "./Layout/AuthLayout.vue";
import { extend } from "vee-validate";
import { required, email, min } from "vee-validate/dist/rules";
import API from "@/services/api";

extend("email", email);
extend("required", required);
extend("min", min);

export default {
  components: {
    FadeRenderTransition,
    AuthLayout,
  },
  data() {
    return {
      userService: new API(process.env.VUE_APP_API_URL, "auth/login"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments/supervisor"),
      email: "",
      password: "",
      loading: false, // Tracks the loading state of the form submission
    };
  },
  methods: {
    async fetchSupervisor(department) {
      try {
        const response = await this.departmentService.getById(department);
        return response;
      } catch (error) {
        this.$alert.error("Failed to fetch Departments");
      }
    },
    async submit() {
      try {
        this.loading = true;

        const loginData = {
          username: this.email,
          email: this.email,
          password: this.password,
        };

        // API call to backend for login
        const response = await this.userService.create(loginData);


        
        // Handle response
        if (response?.result?.isNotAllowed === true) {
          this.$alert.error(
            "You are not allowed, your account could be deactivated"
          );
        } if (response?.user.isActive==false) {
          this.$alert.error(
            "You are not allowed, your account have been deactivated"
          );
        }else if (response?.result?.isLockedOut === true) {
          this.$alert.error(
            "Your account is locked. Contact support for assistance."
          );
        } else if (response?.result?.user?.EmailConfirmed === true) {
          this.$alert.error(
            "your email address is not verified, please check your email inbox and verify"
          );
        } else {
          // Successful login
          let user=response?.user;

          if (user?.userType) {

            this.$alert.success("Login successful!");
            localStorage.setItem("user", JSON.stringify(response?.user));
            localStorage.setItem("userId", response?.user?.id);
            localStorage.setItem("departmentId", response?.user?.departmentId);
            localStorage.setItem("token", JSON.stringify(response?.token));

            let supervisor=this.fetchSupervisor(response?.user?.department)
            if(supervisor){
              localStorage.setItem("isSupervisor",supervisor==response?.employeeTitle)
            }
            

            if (user.userType === "customer") {
              localStorage.setItem("userRole","customer");
              this.$router.push(`/customer-dashboard`);
            } else if (user.userType == "internal" && user?.role=="") {
              this.$router.push(`/role-selection`);
            } else {
              localStorage.setItem("userRole",user.role?.replace(/\s+/g, "-")?.toLowerCase());
              this.$router.push(`/${user.role?.replace(/\s+/g, "-")?.toLowerCase()}`);
            }
          } else {
            this.$alert.error(
              "User role is not defined. Please contact support."
            );
          }
        }
      } catch (error) {
        console.log(error)
        this.$alert.error(
          error?.response?.data?.message || "Invalid email or password."
        );
      } finally {
        this.loading = false; // Reset loading state
      }
    },
    toggleNavbar() {
      document.body.classList.toggle("nav-open");
    },
    closeMenu() {
      document.body.classList.remove("nav-open");
      document.body.classList.remove("off-canvas-sidebar");
    },
  },
  beforeDestroy() {
    this.closeMenu();
  },
};
</script>

<style scoped>
.navbar-nav .nav-item p {
  line-height: inherit;
  margin-left: 5px;
}
</style>
