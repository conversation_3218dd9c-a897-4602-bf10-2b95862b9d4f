<template>
  <div class="view-issue container-fluid">
    <!-- Back Button -->
    <button class="btn btn-primary mb-4" @click="goBack" :disabled="loading">
      <i
        class="fas"
        :class="loading ? 'fa-spinner fa-spin' : 'fa-arrow-left'"
      ></i>
      {{ loading ? "Loading..." : "Back" }}
    </button>

    <button
      class="btn btn-warning mb-4 pull-right"
      :class="{ disabled: loading }"
      @click="showReassignDialog = true"
    >
      {{ loading ? "Loading..." : "Transfer issue" }}
      <i
        class="fa-solid"
        :class="loading ? 'fa-spinner fa-spin' : 'fa-arrows-up-down-left-right'"
      ></i>
    </button>

    <!-- Loading state -->
    <div v-if="loading" class="loading text-center">
      <i class="fas fa-spinner fa-spin"></i> Loading issue details...
    </div>

    <!-- Error state -->
    <div v-if="error" class="error text-center">
      <i class="fas fa-exclamation-circle"></i> {{ error }}
    </div>

    <!-- Main Content -->
    <div v-if="issue" class="row">
      <!-- Left Column: Issue Details -->
      <div class="col-lg-5 col-md-12 mb-4">
        <div class="card p-4">
          <span
            class="mb-4 font-bold"
            style="font-size: 18px !important; text-transform: capitalize"
            >{{ issue.title }}-({{ issue.issueRef }})</span
          >
          <div class="details-grid row">
            <div class="detail-item col-md-12" v-if="issue.customer">
              <strong>{{
                issue.issueType == "customer" ? "Customer" : "Employee"
              }}</strong>
              <p>{{ issue.customer || "Loading..." }}</p>
              <i>{{ issue.company || "Loading..." }}</i>
            </div>
            <div
              v-if="issue.issueType == 'customer' && issue.account"
              class="detail-item col-md-12"
            >
              <strong>Account:</strong>
              <p>
                {{ issue.account }} | MK{{ account.balance }} |
                {{ account.product_visible_name }}
              </p>
            </div>
            <div class="detail-item col-md-12">
              <strong>Description:</strong>
              <p>{{ issue.description }}</p>
            </div>

            <div class="detail-item col-md-12">
              <strong>Assigned Department:</strong>
              <p>{{ departmentName || "Not assigned" }}</p>
            </div>

            <div class="detail-item col-md-12">
              <strong>Assigned Technician:</strong>
              <p>{{ assignedTechnician || "Not assigned" }}</p>
            </div>

            <div class="detail-item col-md-12">
              <strong>Assigned Date:</strong>
              <p>
                {{
                  issue.assignedDate ? formatDate(issue.assignedDate) : "N/A"
                }}
              </p>
            </div>
            <div
              class="detail-item col-md-12"
              v-if="issue.issueType == 'customer'"
            >
              <strong>Service:</strong>
              <p>{{ serviceName || "Loading..." }}</p>
            </div>
           
            <div class="detail-item col-md-12">
              <strong>Location:</strong>
              <p>{{ issue.location || "N/A" }}</p>
            </div>
            <div class="detail-item col-md-12">
              <strong>District:</strong>
              <p>{{ issue.district || "N/A" }}</p>
            </div>
            <div class="detail-item col-md-12">
              <strong>Phone:</strong>
              <p>{{ issue.phone || "N/A" }}</p>
            </div>
            <div class="detail-item col-md-12">
              <strong>Email:</strong>
              <p>{{ issue.email || "N/A" }}</p>
            </div>
            <div class="detail-item col-md-12">
              <strong>Occurred At:</strong>
              <p>{{ issue.occuredAt ? formatDate(issue.occuredAt) : "N/A" }}</p>
            </div>
            <div class="detail-item col-md-12">
              <strong>Reported At:</strong>
              <p>
                {{ issue.reportedAt ? formatDate(issue.reportedAt) : "N/A" }}
              </p>
            </div>
            <div
              class="detail-item col-md-12"
              v-if="issue.issueType == 'customer'"
            >
              <strong>Category:</strong>
              <p>{{ issue.category || "N/A" }}</p>
            </div>
            <div class="detail-item col-md-12">
              <strong>Priority:</strong>
              <p>{{ issue.priority || "N/A" }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Timeline -->
      <div class="col-lg-7 col-md-12">
        <div class="issue-timeline-container">
          <div class="timeline-wrapper">
            <div class="d-flex justify-content-between align-items-center">
              <span class="mb-0">
                <b
                  ><span class="text-primary">Issue Progress</span> | Source-{{
                    issue.source
                  }}</b
                >
                <span class="text-danger" v-if="duedate"
                  >| {{ duedate ?? "" }}</span
                >
              </span>
              <button
                class="btn btn-primary"
                @click="
                  showUpdateLogDialog = true;
                  buttonDisabled = false;
                "
              >
                <i class="fas fa-plus"></i> Update Status
              </button>
            </div>

            <el-timeline>
              <el-timeline-item
                placement="top"
                v-for="log in issueLogs"
                :key="log.id"
                :timestamp="formatDate(log.updatedAt)"
                :type="getIssueStatusColor(log.status)"
              >
                <div class="card issue-card">
                  <div
                    class="card-body d-flex justify-content-between align-items-center"
                  >
                    <div>
                      <p class="text-gray-500">
                        <b>Action Taken:</b> {{ log.actionTaken }}
                      </p>
                      <p class="text-gray-500">
                        <b>Comment:</b> {{ log.comment }}
                      </p>
                      <p class="text-gray-500" v-if="log.fullName">
                        <b>Updated By:</b> {{ log.fullName || "Loading..." }}
                      </p>
                      <p class="text-gray-500" v-if="log.fullName">
                        <b>Customer alerted:</b> {{ log.expose ? "Yes" : "No" }}
                      </p>
                    </div>
                    <el-tag
                      :type="getIssueStatusColor(log.status)"
                      size="medium"
                      class="float-right"
                      bordered
                    >
                      {{ log.status }}
                    </el-tag>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-content-center">
      <el-dialog
        title="Update Status"
        :visible.sync="showUpdateLogDialog"
        class="custom-dialog"
        width="400px"
        @close="resetLogForm"
      >
        <el-form :model="newLog" label-width="120px">
          <el-form-item label="Action Taken">
            <el-input
              v-model="newLog.actionTaken"
              placeholder="Enter action taken"
            ></el-input>
          </el-form-item>
          <el-form-item label="Comment">
            <el-input
              v-model="newLog.comment"
              type="textarea"
              placeholder="Enter comment"
            ></el-input>
            <el-checkbox v-model="newLog.expose"> Alert Customer </el-checkbox>
          </el-form-item>
          <el-form-item label="Status">
            <el-select v-model="newLog.status" placeholder="Select status">
              <el-option
                v-for="status in issueStatuses"
                :key="status"
                :label="status"
                :value="status"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showUpdateLogDialog = false">Cancel</el-button>
          <el-button type="primary" :disabled="buttonDisabled" @click="addLog"
            >Save</el-button
          >
        </span>
      </el-dialog>

      <!-- Reassignment Dialog -->
      <el-dialog
        title="Transfer issue"
        :visible.sync="showReassignDialog"
        width="400px"
        @close="resetReassignForm"
      >
        <el-form :model="reassignForm" label-width="120px">
          <el-form-item label="Department">
            <el-select
              v-model="reassignForm.departmentId"
              placeholder="Select department"
              @change="loadTechnicians"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.departmentId"
                :label="dept.name"
                :value="dept.departmentId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="Responsible">
            <el-select
              v-model="reassignForm.technicianId"
              placeholder="Select technician/manager"
              :loading="loadingTechnicians"
            >
              <el-option
                v-for="tech in departmentTechnicians"
                :key="tech.id"
                :label="tech.fullName"
                :value="tech.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="Comment">
            <el-input
              v-model="reassignForm.comment"
              type="textarea"
              placeholder="Enter reassignment reason"
            />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showReassignDialog = false">Cancel</el-button>
          <el-button
            type="primary"
            :loading="reassigning"
            @click="handleReassign"
          >
            Confirm Transfer
          </el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>


<script>
import {
  Card,
  Checkbox,
  Button,
  Timeline,
  TimelineItem,
  Tag,
  Dialog,
  Form,
  FormItem,
  Input,
  Select,
  Option,
} from "element-ui";
import API from "@/services/api";
import moment from "moment";

export default {
  name: "ViewIssue",
  components: {
    ElTimeline: Timeline,
    ElTimelineItem: TimelineItem,
    ElCheckbox: Checkbox,
    ElTag: Tag,
    ElDialog: Dialog,
    ElForm: Form,
    ElFormItem: FormItem,
    ElInput: Input,
    ElSelect: Select,
    ElOption: Option,
    ElButton: Button,
  },
  watch: {
    showReassignDialog(value) {
      if (value) {
        this.reassignForm.departmentId=this.issue.assignedDepartmentId
      }
    }
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    userId() {
      return this.loggedInUser?.id;
    },
    userName() {
      return this.loggedInUser?.fullName;
    },
  },
  data() {
    return {
      assignedTechnician:null,
      account: {},
      issuesService: new API(process.env.VUE_APP_API_URL, "issues"),
      issuesLogService: new API(process.env.VUE_APP_API_URL, "issuelogs"),
      usersLogService: new API(process.env.VUE_APP_API_URL, "users"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      searchByAccountsByCustomerIdService: new API(
        process.env.VUE_APP_API_URL,
        "accounts"
      ),
      buttonDisabled: false,
      issue: null,
      services: [],
      customerName: null,
      serviceName: null,
      departmentName: null,
      loading: false,
      error: null,
      issueLogs: [],
      duedate: "",
      showUpdateLogDialog: false,
      newLog: {
        actionTaken: "",
        comment: "",
        status: "",
        expose: false,
      },
      issueStatuses: ["Open", "In Progress", "Resolved", "Closed"],
      showReassignDialog: false,
      reassigning: false,
      loadingTechnicians: false,
      departmentTechnicians: [],
      reassignForm: {
        departmentId: null,
        technicianId: null,
        comment: "",
      },
      maskedCustomerInfo: null,
    };
  },
  async created() {
    this.loading = true;
    await this.fetchServices();
    await this.fetchUsers();
    await this.fetchIssue();
    await this.fetchIssueLogs();
    await this.fetchDepartments();
  },
  methods: {
    getDepartment(issueId) {
      let department = this.departments?.find((a) => a.departmentId == issueId);
      return `${department?.name}`;
    },
    async fetchAccountByCustomerId(id) {
      try {
        const input = id;
        if (!input) {
          this.$alert.warning("Please enter a customer Id");
          return;
        }
        const response = await this.searchByAccountsByCustomerIdService.getById(
          input
        );
        this.account = response[0];
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to find accounts"
        );
      } finally {
      }
    },
    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll()).services;
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },
    async fetchUsers() {
      try {
        const response = await this.usersLogService.getAll();
       
        this.users = response;
      
      } catch (error) {
        console.log(error)
        this.$alert.error(error.response?.message || "Failed to fetch users");
      }
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);

        let issue = await this.issue;

        this.departmentName = response.find(
          (x) => x.departmentId == issue.assignedDepartmentId
        ).name;

        this.reassignForm.departmentId=issue.assignedDepartmentId
      } catch (error) {
        // console.log("hjchjch",error)
        // this.$alert.error(
        //   error.response?.message || "Failed to fetch departments"
        // );
      }
    },
    async fetchIssue() {
      const issueId = this.$route.params.id;
      try {
        const issueResponse = await this.issuesService.getById(issueId);
        this.issue = issueResponse; // Use raw data without masking

        this.fetchAccountByCustomerId(issueResponse.customerId);

        if (this.issue.customerId) {
          const customerResponse = await this.users?.find((x) => {
            return x.account == this.issue.customerId;
          });
          this.customerName = customerResponse?.fullName ?? "unknown";
        }

    

        try {
          this.serviceName = this.services.find(
            (x) => x.i_service_type == this.issue.serviceId
          ).name;

          this.assignedTechnician = this.users.find(
          (x) => x.id == this.issue.assignedTechnicianId
        ).fullName;

        this.departmentTechnicians = this.users.filter(
          (x) => x.departmentId == this.issue.assignedDepartmentId
        );

        } catch (error) {
          console.error("Service not found");
        }
      } catch (err) {
        this.$alert.error("Some details failed to load.");
      } finally {
        this.loading = false;
      }
    },
    async fetchIssueLogs() {
      try {
        const response = await this.issuesLogService.getAll();
        this.issueLogs = response
          ?.filter((x) => x.issueId == this.$route.params.id)
          ?.map((x) => {
            const updatedUser = this.users.find((y) => y.id == x.updatedBy);
            return { ...x, fullName: updatedUser?.fullName };
          });

        //console.log(this.issueLogs)
      } catch (error) {
        //console.log(error)
        this.$alert.error("Failed to fetch issue logs:", error);
      }
    },
    async addLog() {
      this.buttonDisabled = true;

      if (this.newLog.actionTaken == "" || this.newLog.status == "") {
        this.$alert.error("Please set the status and action taken");
        this.buttonDisabled = false;
        return;
      }

      try {
        const logData = {
          issueId: this.$route.params.id,
          ...this.newLog,
          updatedAt: new Date(),
          updatedBy: this.userId,
          fullName: this.userName,
        };

        await this.issuesLogService.create(logData);
        this.issueLogs.unshift(logData);

        // If status is changed to Closed, update issue visibility
        if (this.newLog.status === "Closed") {
          await this.issueService.update(this.issue.issueId, {
            ...this.issue,
            isClosed: true,
            visibleToDepartment: false,
          });
        }

        this.showUpdateLogDialog = false;
        this.resetLogForm();
        await this.fetchIssue(); // Refresh issue data
      } catch (error) {
        this.buttonDisabled = false;
        this.$alert.error("Failed to add log:", error);
      }
    },
    resetLogForm() {
      this.newLog = {
        actionTaken: "",
        comment: "",
        status: "",
      };
    },
    formatDate(dateString) {
      return moment(dateString).format("YYYY-MM-DD HH:mm");
    },
    getIssueStatusColor(status) {
      const colorMap = {
        Open: "warning",
        "In Progress": "primary",
        Resolved: "success",
        Closed: "info",
      };
      return colorMap[status] || "warning";
    },
    getUserName(row) {
      if (!row) return "";
      let user = this.users.find((x) => x.id == row.updatedBy);
      let department = this.departments.find(
        (x) => x.departmentId == user.departmentId
      );
      return `${user.fullName} [${department.name}]` ?? "Unknown";
    },
    goBack() {
      this.$router.go(-1);
    },
    async loadTechnicians() {
      if (!this.reassignForm.departmentId) return;

      this.loadingTechnicians = true;
      try {
        const response = await this.usersLogService.getAll();
        this.departmentTechnicians = response.filter(
          (x) => x.departmentId == this.reassignForm.departmentId
        );
      } catch (error) {
        this.$alert.error("Failed to load technicians");
      } finally {
        this.loadingTechnicians = false;
      }
    },

    async handleReassign() {
      if (!this.reassignForm.departmentId || !this.reassignForm.technicianId) {
        this.$alert.error("Please select both department and technician/manager");
        return;
      }
      this.reassigning = true;
      try{
        this.issue.source=this.this.getDepartment(this.loggedInUser?.departmentId)
      }catch{

      }
      try {
        
        // Update issue assignment
        await this.issueService.update(this.issue.issueId, {
          ...this.issue,
          assignedDepartmentId: this.reassignForm.departmentId,
          assignedTechnicianId: this.reassignForm.technicianId,
          assignedDate: new Date(),
        });

        // Create log entry for reassignment
        const logData = {
          issueId: this.issue.issueId,
          actionTaken: `Issue Transferred from ${this.getDepartment(
            this.issue.assignedDepartmentId
          )} to ${this.getDepartment(this.reassignForm.departmentId)}`,
          comment: this.reassignForm.comment,
          status: "Transferred",
          updatedAt: new Date(),
          updatedBy: this.userId,
          expose: false,
        };

        await this.issuesLogService.create(logData);
        this.issueLogs.unshift(logData);

        // Update UI
        await this.fetchIssue();
        this.showReassignDialog = false;
        this.$alert.success("Issue successfully reassigned");
      } catch (error) {
        this.$alert.error("Failed to reassign issue");
      } finally {
        this.reassigning = false;
      }
    },

    resetReassignForm() {
      this.reassignForm = {
        departmentId: null,
        technicianId: null,
        comment: "",
      };
      this.departmentTechnicians = [];
    },
  },
};
</script>

<style scoped>
.view-issue {
  padding: 20px;
}

.issue-details,
.issue-timeline-container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.details-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.detail-item {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #eee;
  flex: 1 1 calc(50% - 15px); /* Two columns with gap */
}

.detail-item strong {
  color: #555;
}

.detail-item p {
  margin: 5px 0 0;
  color: #333;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: capitalize;
}

.status-new {
  background: #ffc107;
  color: #333;
}

.status-resolved {
  background: #28a745;
  color: white;
}

.status-in-progress {
  background: #17a2b8;
  color: white;
}

.issue-card {
  margin-bottom: 10px;
  transition: box-shadow 0.3s;
}

.issue-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading,
.error {
  text-align: center;
  font-size: 18px;
  margin-top: 20px;
}

.loading i,
.error i {
  margin-right: 10px;
}

.error {
  color: #dc3545;
}

.view-issue {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-content {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.issue-details {
  flex: 1.5; /* Takes 60% of the width */
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.issue-timeline-container {
  flex: 1; /* Takes 40% of the width */
}

.timeline-wrapper {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.detail-item {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #eee;
}

.detail-item strong {
  color: #555;
}

.detail-item p {
  margin: 5px 0 0;
  color: #333;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: capitalize;
}

.status-new {
  background: #ffc107;
  color: #333;
}

.status-resolved {
  background: #28a745;
  color: white;
}

.status-in-progress {
  background: #17a2b8;
  color: white;
}

.issue-card {
  margin-bottom: 10px;
  transition: box-shadow 0.3s;
}

.issue-card:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading,
.error {
  text-align: center;
  font-size: 18px;
  margin-top: 20px;
}

.loading i,
.error i {
  margin-right: 10px;
}

.error {
  color: #dc3545;
}

.custom-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>